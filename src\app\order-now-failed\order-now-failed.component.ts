import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-order-now-failed',
  templateUrl: './order-now-failed.component.html',
  styleUrls: ['./order-now-failed.component.scss']
})
export class OrderNowFailedComponent implements OnInit {
  product_id: any;

  constructor(private route :ActivatedRoute) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.product_id = params['id']
    })
  }

}
