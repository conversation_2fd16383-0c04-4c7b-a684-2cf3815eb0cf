<!-- <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="square-jelly-box" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner>

<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif">
</div> -->

<div class="bannerSection">
  <swiper [config]="bannerMain">
    <ng-template swiperSlide *ngFor="let banner of homeData?.banners">
      <div class="slideItems">
        <a
          (click)="bannerRedirections(banner?.redirection_type,banner?.slug,banner?.title,banner?.bannerName,banner?.bannerCategory)">
          <img src="{{imageBase}}/{{banner?.image}}" alt="banner image" class="img-fluid" />
        </a>
      </div>
    </ng-template>
  </swiper>
</div>

<div class="specification">
  <div class="container">
    <div class="contents">
      <swiper [config]="specificationSlider">
        <ng-template swiperSlide>
          <div class="box">
            <img src="\assets\images\leading-brands.svg" alt="Leading Brands Confidence icon" />
            <div class="text">Leading Brands Confidence</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img src="\assets\images\certified.svg" alt="Certified Diamonds icon" />
            <div class="text">Certified Diamonds</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img src="\assets\images\easy-return.svg" alt="Easy Returns icon" />
            <div class="text">Easy Returns</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img src="\assets\images\free-shipping.svg" alt="Free Insured Shipping icon" />
            <div class="text">Free Insured Shipping</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img src="\assets\images\lifetime-exchange.svg" alt="Life Time Exchange icon" />
            <div class="text">Life Time Exchange</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img src="\assets\images\guaranteed.svg" alt="Guaranteed Buy back image" />
            <div class="text">Guaranteed Buy back</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img src="\assets\images\customisation.svg" alt="Customisation image" />
            <div class="text">Customisation</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img src="\assets\images\bis.svg" alt="BIS Hallmarked 100% HUID ornaments image" />
            <div class="text">BIS Hallmarked 100% HUID ornaments</div>
          </div>
        </ng-template>
      </swiper>
    </div>
  </div>
</div>

<div class="container">
  <div class="topTrending">
    <div class="imageSection">
      <a (click)="bannerRedirections(trendingCollectionData?.redirection_type,trendingCollectionData?.top_trending_first_slug,'' )"
        class="image first"><img src="{{imageBase}}/{{trendingCollectionData?.top_trending_first_image}}"
          alt="collection banner" /></a>
      <a (click)="bannerRedirections(trendingCollectionData?.redirection_type,trendingCollectionData?.top_trending_second_slug,'' )"
        class="image second"><img src="{{imageBase}}/{{trendingCollectionData?.top_trending_second_image}}"
          alt="collection banner" /></a>
    </div>
    <div class="content">
      <h1>{{trendingCollectionData?.top_trending_title}}</h1>
      <p>{{trendingCollectionData?.top_trending_description}}</p>
    </div>
  </div>
</div>

<div class="container">
  <div class="forMenWomen">
    <a (click)="bannerRedirections(menWomenSection?.redirection_type,menWomenSection?.for_men_slug, '', menWomenSection?.menImageName, menWomenSection?.menImageCategory)"
      class="for">
      <img class="image" src="{{imageBase}}/{{menWomenSection?.for_men_image}}" alt="category banner" />
      <h2><span>{{menWomenSection?.for_men_title}}</span><img src="\assets\images\arrow.svg" alt="arrow image" /></h2>
    </a>
    <a (click)="bannerRedirections(menWomenSection?.redirection_type,menWomenSection?.for_women_slug, '', menWomenSection?.womenImageName, menWomenSection?.womenImageCategory)"
      class="for">
      <img class="image" src="{{imageBase}}/{{menWomenSection?.for_women_image}}" alt="category banner" />
      <h2>
        <span>{{menWomenSection?.for_women_title}}</span><img src="\assets\images\arrow.svg" alt="arrow image" />
      </h2>
    </a>
  </div>
</div>

<div class="trendingSection">
  <div class="container">
    <div class="title">
      <h2>{{trendingListData?.title}}</h2>
    </div>
    <div class="trendings">
      <swiper [config]="trendSlide">
        <ng-template swiperSlide *ngFor="let product of trendingListData?.products">
          <div class="slideItems">
            <!-- <div class="ar"><img src="\assets\images\ar.png" alt="" /></div> -->
            <div class="wishlist" [ngClass]="{active: product?.isfavorite}">
              <img class="fav" src="\assets\images\favorite.png" alt="wishlist icon" />
              <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="wishlist icon" />
            </div>
            <a class="productImage" [routerLink]="['/detail',product?.slug]">
              <img src="{{imageBase}}/{{product?.image}}" alt="product image" class="img-fluid" /></a>
            <a [routerLink]="['/detail',product?.slug]" class="productName">{{product?.productName}}</a>
            <a [routerLink]="['/detail',product?.slug]" class="price">
              <div class="offer"><span>₹</span> {{product?.price}}</div>
              <div class="old" *ngIf="product?.actualPrice"><span>₹</span>{{product?.actualPrice}}</div>
            </a>
          </div>
        </ng-template>
        <!-- <ng-template swiperSlide>
          <div class="slideItems">
            <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
            <div class="wishlist">
              <img class="fav" src="\assets\images\favorite.png" alt="" />
              <img
                class="fav-gold"
                src="\assets\images\favorite-gold.png"
                alt=""
              />
            </div>
            <a class="productImage" href="#"
              ><img
                src="\assets\images\product-images\0581521.jpg"
                alt=""
                class="img-fluid"
            /></a>
            <a href="#" class="productName"
              >TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a
            >
            <a href="#" class="price">
              <div class="offer"><span>₹</span> 47,447.00</div>
              <div class="old"><span>₹</span> 47,447.00</div>
            </a>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="slideItems">
            <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
            <div class="wishlist">
              <img class="fav" src="\assets\images\favorite.png" alt="" />
              <img
                class="fav-gold"
                src="\assets\images\favorite-gold.png"
                alt=""
              />
            </div>
            <a class="productImage" href="#"
              ><img
                src="\assets\images\product-images\0567054.jpg"
                alt=""
                class="img-fluid"
            /></a>
            <a href="#" class="productName"
              >TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a
            >
            <a href="#" class="price">
              <div class="offer"><span>₹</span> 47,447.00</div>
              <div class="old"><span>₹</span> 47,447.00</div>
            </a>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="slideItems">
            <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
            <div class="wishlist">
              <img class="fav" src="\assets\images\favorite.png" alt="" />
              <img
                class="fav-gold"
                src="\assets\images\favorite-gold.png"
                alt=""
              />
            </div>
            <a class="productImage" href="#"
              ><img
                src="\assets\images\product-images\0539835.jpg"
                alt=""
                class="img-fluid"
            /></a>
            <a href="#" class="productName"
              >TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a
            >
            <a href="#" class="price">
              <div class="offer"><span>₹</span> 47,447.00</div>
              <div class="old"><span>₹</span> 47,447.00</div>
            </a>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="slideItems">
            <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
            <div class="wishlist">
              <img class="fav" src="\assets\images\favorite.png" alt="" />
              <img
                class="fav-gold"
                src="\assets\images\favorite-gold.png"
                alt=""
              />
            </div>
            <a class="productImage" href="#"
              ><img
                src="\assets\images\product-images\0548637.jpg"
                alt=""
                class="img-fluid"
            /></a>
            <a href="#" class="productName"
              >TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a
            >
            <a href="#" class="price">
              <div class="offer"><span>₹</span> 47,447.00</div>
              <div class="old"><span>₹</span> 47,447.00</div>
            </a>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="slideItems">
            <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
            <div class="wishlist">
              <img class="fav" src="\assets\images\favorite.png" alt="" />
              <img
                class="fav-gold"
                src="\assets\images\favorite-gold.png"
                alt=""
              />
            </div>
            <a class="productImage" href="#"
              ><img
                src="\assets\images\product-images\0540263.jpg"
                alt=""
                class="img-fluid"
            /></a>
            <a href="#" class="productName"
              >TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a
            >
            <a href="#" class="price">
              <div class="offer"><span>₹</span> 47,447.00</div>
              <div class="old"><span>₹</span> 47,447.00</div>
            </a>
          </div>
        </ng-template> -->
      </swiper>
    </div>
    <div class="slide">
      <div class="swiper-button-prev" id="trend-Prev"></div>
      <div class="swiper-button-next" id="trend-Next"></div>
    </div>
  </div>
</div>

<div class="container">
  <div class="brandsLove">
    <div class="title">
      <h2>{{brands?.title}}</h2>
      <p>{{brands?.description}}</p>
    </div>
    <swiper [config]="brandSlide">
      <ng-template swiperSlide *ngFor="let item of brands?.items">
        <a (click)="bannerRedirections(item?.redirection_type,item?.slug,item?.title,item?.imageName, item?.imageCategory)"
          class="slideItems">
          <img src="{{imageBase}}/{{item?.image}}" alt="brand image" class="img-fluid" />
          <!-- <div class="brandImage">
            <img src="" alt="" />
          </div> -->
        </a>
      </ng-template>
      <!-- <ng-template swiperSlide>
        <a href="#" class="slideItems">
          <img src="\assets\images\brands\orlina(2).png" alt="" class="img-fluid" />
          <div class="brandImage">
            <img src="/assets/images/brands/ORLINA.png" alt="" />
          </div>
        </a>
      </ng-template>
      <ng-template swiperSlide>
        <a href="#" class="slideItems">
          <img src="\assets\images\brands\platinum(2).png" alt="" class="img-fluid" />
          <div class="brandImage"><img src="/assets/images/brands/PLATINUM.png" alt=""></div>
        </a>
      </ng-template>
      <ng-template swiperSlide>
        <a href="#" class="slideItems">
          <img src="\assets\images\brands\poyem.jpeg" alt="" class="img-fluid" />
          <div class="brandImage"><img src="/assets/images/brands/POYEMS.png" alt=""></div>
        </a>
      </ng-template>
      <ng-template swiperSlide>
        <a href="#" class="slideItems">
          <img src="\assets\images\brands\zora.jpeg" alt="" class="img-fluid" />
          <div class="brandImage"><img src="/assets/images/brands/ZORA.png" alt=""></div>
        </a>
      </ng-template> -->
    </swiper>

    <div class="slide">
      <div class="swiper-button-prev" id="category-Prev"></div>
      <div class="swiper-button-next" id="category-Next"></div>
    </div>
  </div>
</div>

<div class="container">
  <a (click)="bannerRedirections(homeData?.advance_booking_banner?.redirection_type,homeData?.advance_booking_banner?.slug, '', homeData?.bannerName, homeData?.advance_booking_banner?.bannerCategory)"
    class="bookGold">
    <img class="image desktop" src="{{imageBase}}/{{homeData?.advance_booking_banner?.mobile_image}}" alt="advance booking banner" />
    <img class="image mobile" src="{{imageBase}}/{{homeData?.advance_booking_banner?.image}}" alt="advance booking banner" />
    <div class="content">
      <h2>{{homeData?.advance_booking_banner?.title}}</h2>
      <span class="book">{{homeData?.advance_booking_banner?.button_text}}</span>
    </div>
  </a>
</div>

<div class="digitalWear">
  <div class="container">
    <div class="digital">
      <div class="content">
        <h2>{{homeData?.digital_wear_banner?.title}}</h2>
        <p>{{homeData?.digital_wear_banner?.description}}</p>
        <a class="digitalBtn">Start Digital Wear</a>
      </div>
      <img class="digitalImg" src="{{imageBase}}/{{homeData?.digital_wear_banner?.image}}" alt="digital wear banner" />
    </div>
  </div>
</div>

<div class="container">
  <a (click)="bannerRedirections(offerData?.redirection_type,offerData?.slug, '', offerData?.bannerName, offerData?.bannerCategory)"
    class="offSection">
    <img class="image mobile" src="{{imageBase}}/{{offerData?.image}}" alt="offer banner" />
    <img class="image desktop" src="{{imageBase}}/{{offerData?.mobile_image}}" alt="offer banner" />
    <div class="content">
      <h2>{{offerData?.title}}</h2>
      <p>{{offerData?.description}}</p>
      <div class="explore">
        <span>{{offerData?.button_text}}</span> <img src="\assets\images\arrow.svg" alt="arrow icon" />
      </div>
    </div>
  </a>
</div>

<div class="container">
  <div class="title">
    <h2>{{categories?.title}}</h2>
  </div>
  <div class="hotCategory">
    <swiper [config]="hotCategorySlide">
      <ng-template swiperSlide *ngFor="let item of categories?.items">
        <a (click)="bannerRedirections(item?.redirection_type,item?.slug,item?.title)" class="slideItems">
          <img src="{{imageBase}}/{{item?.image}}" alt="category image" class="img-fluid" />
          <div class="name">{{item?.title}}</div>
        </a>
      </ng-template>
      <!-- <ng-template swiperSlide>
        <a [routerLink]="['/listing',type,id]" class="slideItems">
          <img src="\assets\images\Rectangle 21 (1).webp" alt="" class="img-fluid" />
          <div class="name">Earings</div>
        </a>
      </ng-template>
      <ng-template swiperSlide>
        <a [routerLink]="['/listing',type,id]" class="slideItems">
          <img src="\assets\images\Rectangle 21 (2).webp" alt="" class="img-fluid" />
          <div class="name">Earings</div>
        </a>
      </ng-template>
      <ng-template swiperSlide>
        <a [routerLink]="['/listing',type,id]" class="slideItems">
          <img src="\assets\images\Rectangle 23.webp" alt="" class="img-fluid" />
          <div class="name">Earings</div>
        </a>
      </ng-template>
      <ng-template swiperSlide>
        <a [routerLink]="['/listing',type,id]" class="slideItems">
          <img src="\assets\images\Rectangle 23.webp" alt="" class="img-fluid" />
          <div class="name">Earings</div>
        </a>
      </ng-template>
      <ng-template swiperSlide>
        <a [routerLink]="['/listing',type,id]" class="slideItems">
          <img src="\assets\images\Rectangle 21.webp" alt="" class="img-fluid" />
          <div class="name">Earings</div>
        </a>
      </ng-template> -->
    </swiper>

    <div class="slide">
      <div class="swiper-button-prev" id="category-Prev"></div>
      <div class="swiper-button-next" id="category-Next"></div>
    </div>
  </div>
</div>

<div class="container">
  <div class="title">
    <h2>{{perfectBride?.title}}</h2>
    <p>{{perfectBride?.description}}</p>
  </div>
  <div class="perfectBride">
    <div class="leftSection">
      <div class="top">
        <ng-container *ngFor="let item of perfectBride?.items">
          <a (click)="bannerRedirections(item?.redirection_type,item?.slug,item?.title)" class="image"
            *ngIf="item?.sequence == 1"><img src="{{imageBase}}/{{item?.image}}" [alt]="item?.title + ' image'" /><span
              class="name">{{item.title}}</span></a>
        </ng-container>
        <ng-container *ngFor="let item of perfectBride?.items">
          <a (click)="bannerRedirections(item?.redirection_type,item?.slug,item?.title)" class="image"
            *ngIf="item?.sequence == 2"><img src="{{imageBase}}/{{item?.image}}" [alt]="item?.title + ' image'" /><span
              class="name">{{item.title}}</span></a>
        </ng-container>
        <ng-container *ngFor="let item of perfectBride?.items">
          <a (click)="bannerRedirections(item?.redirection_type,item?.slug,item?.title)" class="image"
            *ngIf="item?.sequence == 3"><img src="{{imageBase}}/{{item?.image}}" [alt]="item?.title + ' image'" /><span
              class="name">{{item.title}}</span></a>
        </ng-container>
      </div>
      <div class="bottom">
        <ng-container *ngFor="let item of perfectBride?.items">
          <a (click)="bannerRedirections(item?.redirection_type,item?.slug,item?.title)" class="image image_2"
            *ngIf="item?.sequence == 5"><img src="{{imageBase}}/{{item?.image}}" [alt]="item?.title + ' image'" /><span
              class="name">{{item.title}}</span></a>
        </ng-container>
        <ng-container *ngFor="let item of perfectBride?.items">
          <a (click)="bannerRedirections(item?.redirection_type,item?.slug,item?.title)" class="image image_1"
            *ngIf="item?.sequence == 6"><img src="{{imageBase}}/{{item?.image}}" [alt]="item?.title + ' image'" /><span
              class="name">{{item.title}}</span></a>
        </ng-container>
      </div>
    </div>
    <div class="rightSection">
      <ng-container *ngFor="let item of perfectBride?.items">
        <a (click)="bannerRedirections(item?.redirection_type,item?.slug,item?.title)" class="image"
          *ngIf="item?.sequence == 4"><img src="{{imageBase}}/{{item?.image}}" alt="jewellery wearing image" /></a>
      </ng-container>
    </div>
  </div>
</div>

<div class="container">
  <div class="clientSay">
    <div class="heading d-flex">
      <h2>{{testimonials?.title}}</h2>
    </div>
    <swiper [config]="clientSlide">
      <ng-template swiperSlide *ngFor="let item of testimonials?.items">
        <div class="slideItems">
          <div class="image">
            <img src="{{item?.image}}" alt="customer image" />
          </div>
          <div class="name">{{item?.name}}</div>
          <div class="date">{{item?.date}}</div>
          <p class="description">{{item?.description}}</p>
          <div class="quote"><img src="\assets\images\quote.svg" alt="quote icon" /></div>
        </div>
      </ng-template>
      <!-- <ng-template swiperSlide>
        <div class="slideItems">
          <div class="image">
            <img src="\assets\images\Rectangle 25.png" alt="" />
          </div>
          <div class="name">Amelia</div>
          <div class="date">Jan 20 2023</div>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Pellentesque mauris amet auctor mauris ipsum. At velit, volutpat
            pellentesque rhoncus ac at proin diam enim.
          </p>
          <div class="quote"><img src="\assets\images\quote.svg" alt="" /></div>
        </div>
      </ng-template>
      <ng-template swiperSlide>
        <div class="slideItems">
          <div class="image">
            <img src="\assets\images\Rectangle 25.png" alt="" />
          </div>
          <div class="name">Amelia</div>
          <div class="date">Jan 20 2023</div>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Pellentesque mauris amet auctor mauris ipsum. At velit, volutpat
            pellentesque rhoncus ac at proin diam enim.
          </p>
          <div class="quote"><img src="\assets\images\quote.svg" alt="" /></div>
        </div>
      </ng-template>
      <ng-template swiperSlide>
        <div class="slideItems">
          <div class="image">
            <img src="\assets\images\Rectangle 25.png" alt="" />
          </div>
          <div class="name">Amelia</div>
          <div class="date">Jan 20 2023</div>
          <p>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            Pellentesque mauris amet auctor mauris ipsum. At velit, volutpat
            pellentesque rhoncus ac at proin diam enim.
          </p>
          <div class="quote"><img src="\assets\images\quote.svg" alt="" /></div>
        </div>
      </ng-template> -->
    </swiper>
    <div class="slide">
      <div class="swiper-button-prev" id="client-Prev"></div>
      <div class="swiper-button-next" id="client-Next"></div>
    </div>

    <div class="testimonialBtn">
      <div class="primary-button" (click)="popUp()">Submit your Testimonial</div>
    </div>

  </div>
</div>

<div class="container">
  <a (click)="bannerClicked(miGoldSection?.bannerName, miGoldSection?.bannerCategory, '/gold-schemes')"
    class="miSection">
    <div class="leftImage">
      <img src="{{imageBase}}/{{miGoldSection?.image}}" alt="gold scheme banner" />
    </div>
    <div class="rightContent">
      <h2>{{miGoldSection?.title}}</h2>
      <p>{{miGoldSection?.description}}</p>
      <span class="join">{{miGoldSection?.button_text}}</span>
    </div>
  </a>
</div>

<div class="featured">
  <div class="container">
    <div class="contents">
      <div class="leftSection">
        <h2>{{featuredListData?.title}}</h2>
        <p>{{featuredListData?.description}}</p>
        <div class="slide">
          <div class="swiper-button-prev" id="category-Prev"></div>
          <div class="swiper-button-next" id="category-Next"></div>
        </div>
      </div>
      <div class="rightSection">
        <div class="trendings">
          <swiper [config]="featuredSlide">
            <ng-template swiperSlide *ngFor="let product of featuredListData?.products">
              <div class="slideItems">
                <!-- <div class="ar"><img src="\assets\images\ar.png" alt="" /></div> -->
                <div class="wishlist" [ngClass]="{active: product?.isfavorite}">
                  <img class="fav" src="\assets\images\favorite.png" alt="wishlist icon" />
                  <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="wishlist icon" />
                </div>
                <a class="productImage" [routerLink]="['/detail',product?.slug]"><img
                    src="{{imageBase}}/{{product?.image}}" alt="product image" class="img-fluid" /></a>
                <a [routerLink]="['/detail',product?.slug]" class="productName">{{product?.productName}}</a>
                <a [routerLink]="['/detail',product?.slug]" class="price">
                  <div class="offer"><span>₹</span> {{product?.price}}</div>
                  <div class="old" *ngIf="product?.actualPrice"><span>₹</span> {{product?.actualPrice}}</div>
                </a>
              </div>
            </ng-template>
            <!-- <ng-template swiperSlide>
              <div class="slideItems">
                <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
                <div class="wishlist">
                  <img class="fav" src="\assets\images\favorite.png" alt="" />
                  <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="" />
                </div>
                <a class="productImage" href="#"><img src="\assets\images\product-images\0567054.jpg" alt=""
                    class="img-fluid" /></a>
                <a href="#" class="productName">TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a>
                <a href="#" class="price">
                  <div class="offer"><span>₹</span> 47,447.00</div>
                  <div class="old"><span>₹</span> 47,447.00</div>
                </a>
              </div>
            </ng-template>
            <ng-template swiperSlide>
              <div class="slideItems">
                <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
                <div class="wishlist">
                  <img class="fav" src="\assets\images\favorite.png" alt="" />
                  <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="" />
                </div>
                <a class="productImage" href="#"><img src="\assets\images\product-images\0567054.jpg" alt=""
                    class="img-fluid" /></a>
                <a href="#" class="productName">TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a>
                <a href="#" class="price">
                  <div class="offer"><span>₹</span> 47,447.00</div>
                  <div class="old"><span>₹</span> 47,447.00</div>
                </a>
              </div>
            </ng-template>
            <ng-template swiperSlide>
              <div class="slideItems">
                <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
                <div class="wishlist">
                  <img class="fav" src="\assets\images\favorite.png" alt="" />
                  <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="" />
                </div>
                <a class="productImage" href="#"><img src="\assets\images\product-images\0567054.jpg" alt=""
                    class="img-fluid" /></a>
                <a href="#" class="productName">TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a>
                <a href="#" class="price">
                  <div class="offer"><span>₹</span> 47,447.00</div>
                  <div class="old"><span>₹</span> 47,447.00</div>
                </a>
              </div>
            </ng-template>
            <ng-template swiperSlide>
              <div class="slideItems">
                <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
                <div class="wishlist">
                  <img class="fav" src="\assets\images\favorite.png" alt="" />
                  <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="" />
                </div>
                <a class="productImage" href="#"><img src="\assets\images\Rectangle 9.png" alt=""
                    class="img-fluid" /></a>
                <a href="#" class="productName">TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a>
                <a href="#" class="price">
                  <div class="offer"><span>₹</span> 47,447.00</div>
                  <div class="old"><span>₹</span> 47,447.00</div>
                </a>
              </div>
            </ng-template>
            <ng-template swiperSlide>
              <div class="slideItems">
                <div class="ar"><img src="\assets\images\ar.png" alt="" /></div>
                <div class="wishlist">
                  <img class="fav" src="\assets\images\favorite.png" alt="" />
                  <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="" />
                </div>
                <a class="productImage" href="#"><img src="\assets\images\Rectangle 9.png" alt=""
                    class="img-fluid" /></a>
                <a href="#" class="productName">TT GOLD LIGHTWEIGHT 18KT BANGLE - 0000548735</a>
                <a href="#" class="price">
                  <div class="offer"><span>₹</span> 47,447.00</div>
                  <div class="old"><span>₹</span> 47,447.00</div>
                </a>
              </div>
            </ng-template> -->
          </swiper>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <div class="collectionSec">
    <a (click)="bannerRedirections(homeData?.traditional_collection?.traditional_redirection_type, homeData?.traditional_collection?.slug , '', homeData?.traditional_collection?.imageName, homeData?.traditional_collection?.imageCategory)"
      class="box">
      <div class="head">
        <h2>{{homeData?.traditional_collection?.traditional_title}}</h2>
        <p>
          {{homeData?.traditional_collection?.traditional_subtitle}}
        </p>
      </div>
      <img src="{{imageBase}}/{{homeData?.traditional_collection?.traditional_image}}" alt="collection image" />
    </a>
    <a (click)="bannerRedirections(homeData?.everyday_diamonds?.everyday_redirection_type, homeData?.everyday_diamonds?.slug , '', homeData?.everyday_diamonds?.imageName, homeData?.everyday_diamonds?.imageCategory)"
      class="box">
      <div class="head">
        <h2>{{homeData?.everyday_diamonds?.everyday_title}}</h2>
        <p>
          {{homeData?.everyday_diamonds?.everyday_subtitle}}
        </p>
      </div>
      <img src="{{imageBase}}/{{homeData?.everyday_diamonds?.everyday_image}}" alt="collection image" />
    </a>
  </div>
</div>

<a class="special"
  (click)="bannerRedirections(homeData?.video_banner?.redirection_type, homeData?.video_banner?.slug,homeData?.video_banner?.title)">
  <!-- <img src="\assets\images\special-bg.png" alt="" /> -->
  <video width="100%" height="100%" playsinline preload="metadata" [muted]="true" [autoplay]="true" [loop]="true"
    class="ng-star-inserted" src="{{imageBase}}/{{homeData?.video_banner?.video}}">
    <!-- <source type="video/mp4" class="ng-star-inserted"> -->
  </video>
  <div class="content">
    <h2>{{homeData?.video_banner?.title}}</h2>
    <span class="explore">{{homeData?.video_banner?.button_text}}</span>
  </div>
</a>

<div class="blogSec">
  <div class="container">
    <div class="title">
      <h2>Blogs</h2>
    </div>
    <div class="blogSection">
      <a [routerLink]="['/blog-details',item?.slug]" class="blogs" *ngFor="let item of blogs">
        <div class="image">
          <img class="featuredIcon" src="\assets\images\featured.svg" alt="featured image"
            *ngIf="item?.is_featured == 1" />
          <img class="blogImage" src="{{imageBase}}/{{item?.image}}" alt="blog image" />
        </div>
        <div class="content">
          <!-- <div class="date">{{item?.created_at}}</div> -->
          <div class="text">{{item?.title}}</div>
          <!-- <div class="name">{{item?.created_at}}</div> -->
        </div>
      </a>
    </div>
    <span class="view"><a routerLink="/blog">View All</a></span>
  </div>
</div>

<div class="container">
  <div class="WhatsGoing">
    <div class="title">
      <h2>Whats Going on</h2>
    </div>
    <div class="social">
      <div class="socialMedia">
        <div class="head"><img src="\assets\images\instagram.svg" alt="instagram icon"><span>Instagram</span> </div>
        <div class="content instagramPost">
          <script src="https://cdn.lightwidget.com/widgets/lightwidget.js"></script>
          <iframe allowtransparency="true" class="lightwidget-widget" scrolling="no"
            src="https://cdn.lightwidget.com/widgets/465f4b423f5d5110a3d53cbda13d6eef.html" width="100%" height="100%"
            style="border: 0px; overflow: hidden;"></iframe>

        </div>
      </div>
      <div class="socialMedia">
        <div class="head"><img src="\assets\images\youtube.svg" alt="youtube icon"><span>Youtube</span> </div>
        <div class="content">
          <iframe allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""
            frameborder="0" height="100%"
            src="https://www.youtube.com/embed/R_moTFmI3kk?rel=0&amp;autoplay=1&amp;mute=1" width="100%"></iframe>
        </div>
      </div>
      <div class="socialMedia">
        <div class="head"><img src="\assets\images\facebook.svg" alt="facebook icon"><span>Facebook</span> </div>
        <div class="content">
          <div class="fb-page fb_iframe_widget" data-adapt-container-width="false" data-height="293"
            data-hide-cover="true" data-href="https://www.facebook.com/ttdevassy/" data-show-facepile="false"
            data-small-header="true" data-tabs="timeline,messages" data-width="295" fb-xfbml-state="rendered"
            fb-iframe-plugin-query="adapt_container_width=false&amp;app_id=814191349452452&amp;container_width=294&amp;height=293&amp;hide_cover=true&amp;href=https%3A%2F%2Fwww.facebook.com%2Fttdevassy%2F&amp;locale=en_US&amp;sdk=joey&amp;show_facepile=false&amp;small_header=true&amp;tabs=timeline%2Cmessages&amp;width=295">
            <span style="vertical-align: bottom;width: 100%;height: 100%;"><iframe name="f22c572e261af58" width="100%" height="100%"
                data-testid="fb:page Facebook Social Plugin" title="fb:page Facebook Social Plugin" frameborder="0"
                allowtransparency="true" allowfullscreen="true" scrolling="no" allow="encrypted-media"
                src="https://www.facebook.com/plugins/page.php?adapt_container_width=false&amp;app_id=814191349452452&amp;channel=https%3A%2F%2Fstaticxx.facebook.com%2Fx%2Fconnect%2Fxd_arbiter%2F%3Fversion%3D46%23cb%3Dff2cf721e37f28%26domain%3Dwww.ttdevassyjewellery.com%26is_canvas%3Dfalse%26origin%3Dhttps%253A%252F%252Fwww.ttdevassyjewellery.com%252Ff393b7d818deec%26relation%3Dparent.parent&amp;container_width=294&amp;height=293&amp;hide_cover=true&amp;href=https%3A%2F%2Fwww.facebook.com%2Fttdevassy%2F&amp;locale=en_US&amp;sdk=joey&amp;show_facepile=false&amp;small_header=true&amp;tabs=timeline%2Cmessages&amp;width=295"
                style="border: none; visibility: visible;" class=""></iframe></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="connectSection">
  <div class="container">
    <div class="title">
      <h2>Connect with us</h2>
    </div>
    <div class="connect">
      <span (click)="openWhatsapp()" class="box">
        <img src="\assets\images\whatsapp-connect.svg" alt="whatsapp icon">
        <h6>Connect on whatsapp</h6>
      </span>
      <a routerLink="/book-appointment" class="box">
        <img src="\assets\images\book.svg" alt="appointment icon">
        <h6>Book an Appoinment</h6>
      </a>
      <a routerLink="/virtual-shopping" class="box">
        <img src="\assets\images\videocall.svg" alt="video call icon">
        <h6>Start a video call</h6>
      </a>
    </div>
  </div>
</div>



<div class="testimonialPopup" [ngClass]="testimonialModal?'show':'hide'">
  <div class="box">
    <div class="close" (click)="popUpClose()"><img src="\assets\images\close-white.svg" alt="close icon"></div>
    <div class="content">
      <form [formGroup]="form">
        <div class="field">
          <div class="name">Name</div>
          <input type="text" formControlName="name" />
          <div style="color: red;"
            *ngIf="testimonialControls['name'].errors && (!isSubmitted || testimonialControls['name'].touched)">
            Name is required</div>
        </div>
        <div class="field">
          <div class="name">Description</div>
          <textarea name="" id="" cols="30" rows="3" formControlName="description"></textarea>
          <div style="color: red;"
            *ngIf="testimonialControls['description'].errors && (!isSubmitted || testimonialControls['description'].touched)">
            Description is required</div>
        </div>
        <div class="field">
          <div class="name">Upload image (png or jpg)*</div>
          <span class="uploading">
            <input type="file" (change)="uploadImage($event.target)" />

            <div>
              <img src="{{imageURL}}" alt="uploaded image" *ngIf="imageURL" />
            </div>

          </span>
          <div style="color: red;" *ngIf="!isImageUploaded">
            Image is required
          </div>
        </div>
      </form>
      <div class="submitBtn">
        <div class="primary-button golden" (click)="submitTestimonial()">Submit</div>
      </div>
    </div>
  </div>
</div>