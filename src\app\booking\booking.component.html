<!-- <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="square-jelly-box" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner> -->

<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div>

<div class="booking">
  <div class="container" [ngClass]="{ hide: sectionTwoHide == true }">
    <div class="title">
      <h1>Advance Booking</h1>
      <p>
        If the price changes at the time of purchases, get the jewellery at
        booked rate or prevailing rate whichever is lower
      </p>
    </div>
    <div class="paging">
      <div class="numbering">
        <span class="number active">1</span>
        <span>Specify Quantity and period</span>
      </div>
      <div class="numbering">
        <span class="number" [ngClass]="{ active: sectionHide == true }">2</span>
        <span>Personal Details</span>
      </div>
    </div>
  </div>
  <div class="container hide" [ngClass]="{ show: sectionTwoHide == true }">
    <div class="title p-0">
      <h2>Confirm Your Details</h2>
      <p>
        consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
        et dolore magna aliqua. Ut enim ad minim veniam,
      </p>
    </div>
  </div>

  <div class="container">
    <form action="" [formGroup]="bookingForm">
      <div class="bookingForm FirstSection" *ngIf="addSectionOne">
        <div class="head">Specify Quantity and period</div>
        <div class="sections">
          <div class="leftSide">
            <div class="field">
              <div class="name">Select Store<span>*</span></div>
              <ng-select class="filtername selectOption" [items]="stores" bindLabel="name" bindValue="id"
                [(ngModel)]="selectedStore" [ngModelOptions]="{standalone: true}" [clearable]="false"
                [searchable]="false">
              </ng-select>
            </div>
            <div class="field">
              <div class="name">Period (days)<span>*</span></div>
              <ng-select class="filtername selectOption" [items]="periods" bindLabel="period" bindValue="id"
                [(ngModel)]="selectedPeriod" [ngModelOptions]="{standalone: true}" [clearable]="false"
                [searchable]="false" (ngModelChange)="getPaymentDetails()">
              </ng-select>
            </div>
            <div class="field">
              <div class="name">Gold Weight (gm)<span>*</span></div>
              <input type="number" formControlName="weight" (keydown)="onKeyDown($event)" (ngModelChange)="getPaymentDetails()" />
            </div>
          </div>
          <div class="rightSide" *ngIf="paymentDetails">
            <div class="box">
              <div class="rates">
                <div class="rateOn">Advance percentage</div>
                <div class="rt">{{paymentDetails?.adv_percentage}}%</div>
              </div>
              <div class="rates">
                <div class="rateOn">Gold value</div>
                <div class="rt">₹{{paymentDetails?.gold_value}}</div>
              </div>
              <div class="rates">
                <div class="rateOn">Advance Amount You Need to Pay</div>
                <div class="rt">₹{{paymentDetails?.adv_amount}}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center">
          <div class="primary-button golden" (click)="nextOne()">Next</div>
        </div>
      </div>
      <div class="bookingForm SecondSection" *ngIf="addPersonalInfo">
        <div class="head">Personal Details</div>
        <div class="sections">
          <div class="leftSide">
            <div class="field">
              <div class="name">Name<span>*</span></div>
              <input type="text" formControlName="name" required />
              <span *ngIf="bf.name.invalid && bf.name.touched">
                <span class="text-danger" *ngIf="bf.name.errors?.required"> Name is required </span>
              </span>
            </div>
            <div class="field">
              <div class="name">Mail ID<span>*</span></div>
              <input type="email" formControlName="mail" required />
              <span *ngIf="bf.mail.invalid && bf.mail.touched">
                <span class="text-danger" *ngIf="bf.mail.errors?.required"> Email is required </span>
              </span>
              <span class="text-danger" *ngIf="bf.mail.errors?.pattern">Please Enter Valid Email </span>
            </div>
            <div class="field drop">
              <div class="name">Country<span>*</span></div>
              <ng-select class="filtername selectOption" [items]="countries" bindLabel="name" bindValue="id"
                [(ngModel)]="selectedCountry" [ngModelOptions]="{standalone: true}" [clearable]="false"
                [searchable]="false" (change)="selectCountry()">
              </ng-select>
              <span class="text-danger" *ngIf="!selectedCountry">{{countryValidation}}</span>
            </div>
            <div class="field">
              <div class="name">House Name/Flat No<span>*</span></div>
              <input type="text" formControlName="house_name" required />
              <span *ngIf="bf.house_name.invalid && bf.house_name.touched">
                <span class="text-danger" *ngIf="bf.house_name.errors?.required">House Name/Flat No is required </span>
              </span>
            </div>
            <div class="field">
              <div class="name">Street/Location<span>*</span></div>
              <input type="text" formControlName="street" required />
              <span *ngIf="bf.street.invalid && bf.street.touched">
                <span class="text-danger" *ngIf="bf.street.errors?.required"> Street/Location is required </span>
              </span>
            </div>
            <div class="field">
              <div class="name">City<span>*</span></div>
              <input type="text" formControlName="city" />
              <span *ngIf="bf.city.invalid && bf.city.touched">
                <span class="text-danger" *ngIf="bf.city.errors?.required"> City is required </span>
              </span>
            </div>
          </div>
          <div class="rightSide">
            <div class="field">
              <div class="name">Mobile number<span>*</span></div>
              <div class="country">
                <select class="selectOption" name="" id="" formControlName="code">
                  <option [value]="country.phonecode" *ngFor="let country of countries">
                    +{{country.phonecode}}
                  </option>
                </select>
                <input type="number" formControlName="mobile" (keydown)="onKeyDown($event)"/>
              </div>
              <span class="text-danger" *ngIf="bf.mobile.invalid && bf.mobile.touched">
                <span *ngIf="bf.mobile.errors?.required"> Mobile Number is required </span>
                <span class="text-danger" *ngIf="bf.mobile.errors?.pattern">Please Enter Valid Mobile Number </span>
              </span>
            </div>
            <div class="field">
              <div class="name">PAN number<span>*</span></div>
              <input type="text" formControlName="pan" (input)="onInputChange($event)" #myInput/>
              <span class="text-danger" *ngIf="bf.pan.invalid && bf.pan.touched">
                <span *ngIf="bf.pan.errors?.required"> Pan Number is required </span>
              </span>
              <span class="text-danger" *ngIf="bf.pan.errors?.pattern">Please Enter Valid Pan Number </span>
            </div>
            <div class="field drop">
              <div class="name">State<span>*</span></div>
              <ng-select class="filtername selectOption" [items]="states" bindLabel="name" bindValue="id"
                [(ngModel)]="selectedState" [ngModelOptions]="{standalone: true}" [clearable]="false"
                [searchable]="false" [disabled]="!states || states.length == 0">
              </ng-select>
              <span class="text-danger" *ngIf="!selectedState">{{stateValidation}}</span>
            </div>
            <div class="field">
              <div class="name">District<span>*</span></div>
              <input type="text" formControlName="district" />
              <span *ngIf="bf.district.invalid && bf.district.touched">
                <span class="text-danger" *ngIf="bf.district.errors?.required"> District is required </span>
              </span>
            </div>
            <div class="field">
              <div class="name">Aadhar number<span>*</span></div>
              <input type="number" formControlName="aadhar" (keydown)="onKeyDown($event)"/>
              <span *ngIf="bf.aadhar.invalid && bf.aadhar.touched">
                <span class="text-danger" *ngIf="bf.aadhar.errors?.required"> Aadhar Number is required </span>
              </span>
              <span class="text-danger" *ngIf="bf.aadhar.errors?.pattern">Please Enter Valid Aadhar Number </span>
            </div>
            <div class="field">
              <div class="name">Pincode<span>*</span></div>
              <input type="number" formControlName="pincode" (keydown)="onKeyDown($event)"/>
              <span *ngIf="bf.pincode.invalid && bf.pincode.touched">
                <span class="text-danger" *ngIf="bf.pincode.errors?.required"> Pincode is required </span>
              </span>
              <span class="text-danger" *ngIf="bf.pincode.errors?.pattern">Please Enter Valid Pincode </span>
            </div>
          </div>
        </div>
        <div class="head">Nominee Details</div>
        <div class="sections">
          <div class="leftSide">
            <div class="field">
              <div class="name">Name<span>*</span></div>
              <input type="text" formControlName="nominee_name" />
              <span class="text-danger" *ngIf="bf.nominee_name.invalid && bf.nominee_name.touched">
                <span *ngIf="bf.nominee_name.errors?.required"> Nominee is required </span>
              </span>
            </div>
          </div>
          <div class="rightSide">
            <div class="field">
              <div class="name">Relationship<span>*</span></div>
              <input type="text" formControlName="nominee_relation" />
              <span class="text-danger" *ngIf="bf.nominee_relation.invalid && bf.nominee_relation.touched">
                <span *ngIf="bf.nominee_relation.errors?.required"> Nominee Relation is required </span>
              </span>
            </div>
          </div>
        </div>
        <div class="text-center">
          <div class="primary-button golden" (click)="confirmDetails()">Next</div>
        </div>
      </div>
      <div class="bookingForm" *ngIf="confirmSection">
        <div class="head">Specify Quantity and period</div>
        <div class="box finalDetails">
          <div class="rates">
            <div class="rateOn">Select Store:</div>
            <div class="rt">{{bookingDetails?.branch_name}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">Gold Weight (gm):</div>
            <div class="rt">{{bookingDetails?.booked_weight}}gm</div>
          </div>
          <div class="rates">
            <div class="rateOn">Period (month):</div>
            <div class="rt">{{bookingDetails?.period}} Days</div>
          </div>
          <div class="rates">
            <div class="rateOn">Gold value:</div>
            <div class="rt">₹{{bookingDetails?.gold_rate}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">Advance percentage:</div>
            <div class="rt">{{bookingDetails?.adv_percentage}}%</div>
          </div>
          <div class="rates">
            <div class="rateOn">Advance Amount You Need to Pay:</div>
            <div class="rt">₹{{bookingDetails?.adv_amount}}</div>
          </div>
        </div>
        <div class="head">Personal Details</div>
        <div class="box finalDetails">
          <div class="rates">
            <div class="rateOn">Name:</div>
            <div class="rt">{{bookingDetails?.name}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">Mobile number:</div>
            <div class="rt">+{{bookingDetails?.country_code}} {{bookingDetails?.mobile_no}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">Mail ID:</div>
            <div class="rt">{{bookingDetails?.mail_id}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">Address:</div>
            <div class="rt">{{bookingDetails?.address_1}},{{bookingDetails?.address_2}},{{bookingDetails?.address_4}}
            </div>
          </div>
          <div class="rates">
            <div class="rateOn">Aadhar number:</div>
            <div class="rt">{{bookingDetails?.aadhar_no}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">PAN number:</div>
            <div class="rt">{{bookingDetails?.pan_no}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">State:</div>
            <div class="rt">{{bookingDetails?.state_name}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">City:</div>
            <div class="rt">{{bookingDetails?.address_3}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">Pincode:</div>
            <div class="rt">{{bookingDetails?.pincode}}</div>
          </div>
        </div>
        <div class="head">Nominee Details</div>
        <div class="box finalDetails mb-4">
          <div class="rates">
            <div class="rateOn">Name:</div>
            <div class="rt">{{bookingDetails?.nominee}}</div>
          </div>
          <div class="rates">
            <div class="rateOn">Relationship</div>
            <div class="rt">{{bookingDetails?.nominee_relation}}</div>
          </div>
        </div>
        <div class="here">
          <span class="hereBy">
            <input type="checkbox" [(ngModel)]="isChecked" [ngModelOptions]="{standalone: true}">I hereby accept the <a
              routerLink="/terms"> Terms&Conditions</a>
          </span>
        </div>
        <div class="buttons">
          <div class="primary-button  white" (click)="showEdit()">Edit</div>
          <div class="primary-button golden" (click)="bookNow()">Book Now</div>
        </div>
      </div>
    </form>
  </div>

  <!-- <form *ngIf="!checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
    action="{{ccAvenueUrl}}?command=initiateTransaction&enc_request={{encRequest}}&access_code={{accessCode}}&request_type=XML&response_type=XML&version=1.1">
    <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
    <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
  </form>
  <form *ngIf="checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
  action="{{ccAvenueBeta}}?command=initiateTransaction">
  <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
  <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
</form> -->

  <div class="helpiline">
    <div class="container">
      <div class="content">
        <span>Help Line Number</span>
        <a href="callto:+91 9497 916 000"><img src="\assets\images\advance-booking\Vector.svg" alt="phone icon" />+91
          9497 916 000</a>
      </div>
    </div>
  </div>
</div>
