<div class="smallBanner"
  (click)="bannerRedirections(banners?.redirection_type,banners?.redirection_id)">
  <img src="{{imageBase}}/{{banners?.image}}" alt="virtual shopping banner">
</div>

<div class="designJewellery">
  <div class="container">
    <div class="title">
      <h1>Virtual Shopping Through Video Call</h1>
    </div>
    <form [formGroup]="virtualForm">
      <div class="form">
        <div class="leftSide">
          <div class="field">
            <span>Name</span>
            <input type="text" formControlName="name" />
            <span *ngIf="vf.name.invalid && vf.name.touched">
              <span class="text-danger" *ngIf="vf.name.errors?.required"> name is required </span>
            </span>
          </div>
          <div class="field">
            <span>Mobile Number</span>
            <input type="tel" formControlName="mobile" />
            <span *ngIf="vf.mobile.invalid && vf.mobile.touched">
              <span class="text-danger" *ngIf="vf.mobile.errors?.required"> mobile is required </span>
            </span>
            <span class="text-danger" *ngIf="vf.mobile.errors?.pattern">Please Enter Valid mobile number </span>
          </div>
          <div class="field">
            <span>choose store</span>
            <ng-select class="filtername" [items]="stores" bindLabel="name" bindValue="id" [(ngModel)]="selectedStore"
              [ngModelOptions]="{standalone: true}" [clearable]="false" [searchable]="false">
            </ng-select>
            <span class="text-danger" *ngIf="!selectedStore">{{storeValidation}}</span>
          </div>
          <div class="field">
            <span>Date</span>
            <input type="date" formControlName="date" />
            <!-- <ngx-datepicker [options]="options" formControlName="date"></ngx-datepicker> -->
            <span *ngIf="vf.date.invalid && vf.date.touched">
              <span class="text-danger" *ngIf="vf.date.errors?.required"> date is required </span>
            </span>
          </div>
        </div>
        <div class="rightSide">
          <div class="field">
            <span>Email</span>
            <input type="email" formControlName="email" />
            <span *ngIf="vf.email.invalid && vf.email.touched">
              <span class="text-danger" *ngIf="vf.email.errors?.required"> Email is required </span>
            </span>
            <span class="text-danger" *ngIf="vf.email.errors?.pattern">Please Enter Valid Email </span>
          </div>
          <div class="field">
            <span>What kind of jewellery are you looking for ?</span>
            <textarea name="" id="" cols="30" rows="6" formControlName="lookingFor"></textarea>
            <span *ngIf="vf.lookingFor.invalid && vf.lookingFor.touched">
              <span class="text-danger" *ngIf="vf.lookingFor.errors?.required"> Required </span>
            </span>
          </div>
          <div class="field time">
            <span>Time Slot</span>
            <div class="timePicker">
              <input [ngxTimepicker]="picker" formControlName="time">
              <ngx-material-timepicker #picker></ngx-material-timepicker>
            </div>
            <span *ngIf="vf.time.invalid && vf.time.touched">
              <span class="text-danger" *ngIf="vf.time.errors?.required"> time is required </span>
            </span>
          </div>
          <div class="submitBtn" (click)="submit()"><input type="submit" value="Submit" /></div>
        </div>
      </div>
    </form>
  </div>
</div>

<div class="container">
  <div class="bottomSection">
    <div class="box">
      <div class="image"><img src="\assets\images\virtual\appointment 1.svg" alt="icon"></div>
      <p>fill all details in the form and let us know the convenient date for video call and your jewellery preference.
      </p>
    </div>
    <div class="box">
      <div class="image"><img src="\assets\images\virtual\Vector.svg" alt="icon"></div>
      <p>Our agent will call you to book the timings for the call at your convenience.</p>
    </div>
    <div class="box">
      <div class="image"><img src="\assets\images\virtual\image (1) 2.svg" alt="icon"></div>
      <p>During the call, we will personally attend you to help you to choose from a collections that we will keep
        curated beforehand itself based on your preference.</p>
    </div>
  </div>
</div>
