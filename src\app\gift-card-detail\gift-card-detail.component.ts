import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from '../ApiEndPoints';
import { environment } from 'src/environments/environment.prod';
import { ApiService } from './../Services/api.service';

@Component({
  selector: 'app-gift-card-detail',
  templateUrl: './gift-card-detail.component.html',
  styleUrls: ['./gift-card-detail.component.scss']
})
export class GiftCardDetailComponent implements OnInit {
  order_id: any;
  data: any;
  deliveryAddress: any;
  billingAddress: any;
  productdata: any;
  imageBase: any;
  isCancelled: boolean = false;

  constructor(private route: ActivatedRoute, private ApiService: ApiService, private taost: ToastrService) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.order_id = params['id']
      this.getOrderDetail()
    })
    this.imageBase = environment.imageBase
  }

  getOrderDetail() {
    this.ApiService.postData(apiEndPoints.giftcard_order_details, { giftcard_id: this.order_id }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.data = res?.Data
        this.deliveryAddress = this.data?.gifting_person_details
        this.billingAddress = this.data?.billing_address
        this.productdata = this.data?.order_data
      }
    })
  }

  cancelOrder() {
    this.isCancelled = true;
    this.ApiService.postData(apiEndPoints.cancel_order, { order_product_id: this.productdata?.order_product_id }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.taost.success(res?.Message)
        this.getOrderDetail()
      }
      else this.taost.error(res?.Message)
    })
  }

  returnOrder() {
    this.isCancelled = true;
    this.ApiService.postData(apiEndPoints.return_order, { order_product_id: this.productdata?.order_product_id }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.taost.success(res?.Message)
        this.getOrderDetail()
      }
      else this.taost.error(res?.Message)
    })
  }

}
