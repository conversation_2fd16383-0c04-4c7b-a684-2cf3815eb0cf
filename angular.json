{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": "55593dc5-4955-45a1-bd3b-7c4e682730ec"}, "version": 1, "newProjectRoot": "projects", "projects": {"tt-devassy": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/tt-devassy", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "serviceWorker": true, "ngswConfigPath": "src/ngsw-config.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", "src/service-worker.js", {"glob": "**/*", "input": "src/assets/videos", "output": "/assets/videos"}], "styles": ["./node_modules/@angular/material/prebuilt-themes/purple-green.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/styles.scss", "node_modules/ngx-toastr/toastr.css"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "4mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "26kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "tt-devassy:build:production"}, "development": {"browserTarget": "tt-devassy:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "tt-devassy:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/purple-green.css", "src/styles.scss"], "scripts": []}}}}}, "defaultProject": "tt-devassy"}