import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Component, OnInit } from '@angular/core';
import { ApiService } from '../Services/api.service';
import { apiEndPoints } from '../ApiEndPoints';
import { CommonService } from '../Services/common.service';
import { environment } from 'src/environments/environment.prod';
import { advanceBooking } from '../shared/models/advance-booking.model';
import { Meta } from '@angular/platform-browser';

@Component({
  selector: 'app-advance-booking',
  templateUrl: './advance-booking.component.html',
  styleUrls: ['./advance-booking.component.scss'],
})
export class AdvanceBookingComponent implements OnInit {
  terms: any;
  showModal: boolean = false;
  banner: any;
  imageBase = environment.imageBase;
  firstBanner!: string;
  advanceBookingData!: advanceBooking;
  windowRef: any = window;

  constructor(
    private toast: ToastrService,
    private router: Router,
    private apiservice: ApiService,
    private commonService: CommonService,
    private meta: Meta
  ) {}

  ngOnInit(): void {
    this.getTnc();
    this.getBanners();
    this.getAdvanceBookingData();
  }

  loginCheck() {
    //Webengage Start
    this.windowRef.webengage.track('Book Now', {
      'Page URL': this.windowRef.location.href,
    });
    //Webengage End

    if (localStorage.getItem('isLogged')) this.router.navigateByUrl('/booking');
    else this.toggleModal();
  }

  getTnc() {
    this.apiservice
      .getData(apiEndPoints.advanceBookingTerms)
      .subscribe((res: any) => {
        if (res?.errorcode == 0) {
          this.terms = res?.data;
        }
      });
  }

  toggleModal() {
    this.commonService.sendClickEvent();
    this.showModal = true;
  }

  getBanners() {
    this.apiservice.getData(apiEndPoints.page_banners).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.banner = res?.Data?.advancebooking_page_banner;
      }
    });
  }

  getAdvanceBookingData() {
    this.apiservice
      .getAdvanceBookingData(apiEndPoints.advance_booking)
      .subscribe(
        (res) => {
          if (res.ErrorCode === 0) {
            this.advanceBookingData = res.Data;
            this.firstBanner =
              this.imageBase + '/' + this.advanceBookingData?.advance_banner;
            this.updateMeta();
          } else {
            this.toast.error('Something went wrong', 'Error');
          }
        },
        (error) => {
          this.toast.error(error, 'Error');
        }
      );
  }

  bannerRedirections(type: any, id: any) {
    if (type == 1) this.router.navigate(['/detail', id]);
    else if (type == 2 || 3 || 4) this.router.navigate(['/listing', type, id]);

    if (type == 5 && id == 3) {
      this.router.navigateByUrl('/virtual-shopping');
    } else if (type == 5 && id == 4) {
      this.router.navigateByUrl('/zora-collection');
    } else if (type == 5 && id == 5) {
      this.router.navigateByUrl('/divo-collection');
    } else if (type == 5 && id == 6) {
      this.router.navigateByUrl('/poyem-collection');
    } else if (type == 5 && id == 7) {
      this.router.navigateByUrl('/orlin-collection');
    } else if (type == 5 && id == 8) {
      this.router.navigateByUrl('/book-appointment');
    } else if (type == 5 && id == 2) {
      this.router.navigateByUrl('/gold-schemes');
    } else if (type == 5 && id == 1) {
      this.router.navigateByUrl('/advance-booking');
    } else if (type == 5 && id == 9) {
      this.router.navigateByUrl('/gift-voucher');
    } else if (type == 5 && id == 10) {
      this.router.navigateByUrl('/digital-gold');
    } else if (type == 5 && id == 11) {
      this.router.navigateByUrl('/about');
    }
  }

  updateMeta() {
    this.meta.updateTag({
      name: 'title',
      content: this.advanceBookingData?.advance_title,
    });
    this.meta.updateTag({
      name: 'description',
      content: this.advanceBookingData?.advance_description,
    });
    this.meta.updateTag({
      property: 'og:image',
      content: this.imageBase + '/' + this.advanceBookingData?.advance_banner,
    });
    this.meta.updateTag({
      property: 'og:title',
      content: this.advanceBookingData?.advance_title,
    });
    this.meta.updateTag({
      property: 'og:description',
      content: this.advanceBookingData?.advance_description,
    });
    this.meta.updateTag({
      rel: 'canonical',
      href: 'https://ttdevassyjewellery.com/advance-booking',
    });
  }
}
