import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CartComponent } from './cart/cart.component';
import { DeliveryDetailsComponent } from './delivery-details/delivery-details.component';
import { OrderDetailsComponent } from './order-details/order-details.component';
import { OrderPlacedComponent } from './order-placed/order-placed.component';
import { OrderFailedComponent } from './order-failed/order-failed.component';

const routes: Routes = [
  {path: '', component: CartComponent},
  {path: 'delivery-details', component: DeliveryDetailsComponent},
  {path: 'order-details', component: OrderDetailsComponent},
  {path: 'order-placed', component: OrderPlacedComponent},
  {path: 'order-failed', component: OrderFailedComponent}

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CheckoutRoutingModule { }
