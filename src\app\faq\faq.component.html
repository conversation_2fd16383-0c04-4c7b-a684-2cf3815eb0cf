<div class="container">
    <div class="breadCrumb">
        <a href="#" class="link">Home</a>
        <div class="link">FAQ</div>
    </div>
</div>
<div class="faqSection">
    <div class="container">
        <div class="title">
            <h1>FAQ</h1>
        </div>
        <div class="faq">
            <div class="firstSec">
              <ng-container *ngFor="let item of data; let i = index">
                <div class="drops">
                    <button class="faq-accordion" (click)="showAccordion(i+1)" [ngClass]="{'active': selectedIndex == i+1}">{{item?.question}}</button>
                    <div class="course-panel" [ngClass]="{'open-accordion': selectedIndex == i+1}">
                        <p>{{item?.answer}}</p>
                    </div>
                </div>
              </ng-container>
                <!-- <div class="drops">
                    <button class="faq-accordion" (click)="showAccordion(2)" [ngClass]="{'active': selectedIndex == 2}">How do I unsubscribe from The emails?</button>
                    <div class="course-panel" [ngClass]="{'open-accordion': selectedIndex == 2}">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Feugiat ultrices tellus vitae viverra dolor, quis id dictum diam. r sit amet, csectetur adipiscing elit. Feu</p>
                    </div>
                </div>
                <div class="drops">
                    <button class="faq-accordion" (click)="showAccordion(3)" [ngClass]="{'active': selectedIndex == 3}">Lorem ipsum dolor sit amet, consectetur adipiscing eli</button>
                    <div class="course-panel" [ngClass]="{'open-accordion': selectedIndex == 3}">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Feugiat ultrices tellus vitae viverra dolor, quis id dictum diam. r sit amet, csectetur adipiscing elit. Feu</p>
                    </div>
                </div> -->
            </div>
        </div>
    </div>
</div>
