{"name": "tt-devassy", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production", "build:analyze": "ng build --configuration production --stats-json && webpack-bundle-analyzer dist/tt-devassy/stats.json", "watch": "ng build --watch --configuration development", "test": "ng test", "analyze": "webpack-bundle-analyzer dist/tt-devassy/stats.json"}, "private": true, "dependencies": {"@angular-slider/ngx-slider": "^2.0.4", "@angular/animations": "^12.1.2", "@angular/cdk": "^12.2.13", "@angular/common": "~12.2.0", "@angular/compiler": "~12.2.0", "@angular/core": "~12.2.0", "@angular/fire": "^6.1.5", "@angular/forms": "~12.2.0", "@angular/material": "^12.2.13", "@angular/platform-browser": "~12.2.0", "@angular/platform-browser-dynamic": "~12.2.0", "@angular/router": "~12.2.0", "@angular/service-worker": "^12.2.17", "@ng-bootstrap/ng-bootstrap": "^10.0.0", "@ng-select/ng-select": "^7.4.0", "angular-moment": "^1.3.0", "angularx-qrcode": "^12.0.3", "bootstrap": "^5.2.3", "chart.js": "^3.4.0", "firebase": "^8.10.0", "google-libphonenumber": "^3.2.32", "hls.js": "^1.4.14", "ng-otp-input": "^1.8.5", "ng-recaptcha": "^8.0.1", "ng2-charts": "^3.0.0", "ng2-datepicker": "^12.0.0", "ng2-timepicker": "^1.4.1", "ngx-image-zoom": "^1.0.1", "ngx-infinite-scroll": "^10.0.1", "ngx-material-timepicker": "^5.5.3", "ngx-pagination": "^3.3.1", "ngx-read-more": "^1.0.0", "ngx-spinner": "^12.0.0", "ngx-toastr": "^14.3.0", "rxjs": "~6.6.0", "swiper": "^8.4.4", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~12.2.18", "@angular/cli": "~12.2.18", "@angular/compiler-cli": "~12.2.0", "@types/google-libphonenumber": "^7.4.23", "@types/hls.js": "^1.0.0", "@types/jasmine": "~3.8.0", "@types/node": "^12.11.1", "jasmine-core": "~3.8.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.3.5", "webpack-bundle-analyzer": "^4.10.2"}}