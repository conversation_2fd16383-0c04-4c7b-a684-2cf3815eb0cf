<div class="myAccount">
  <div class="container">
    <div class="title">
      <h1>My Account</h1>
    </div>
    <div class="contentSection">
      <div class="sideMenu">
        <app-sidebar></app-sidebar>
      </div>
      <div class="contents">
        <div class="head">Wishlist ({{ wishlistData?.totalCount }})</div>

        <!-- Empty State -->
        <div
          class="empty-state"
          *ngIf="
            !isLoading &&
            (!wishlistData?.products || wishlistData?.products.length === 0)
          "
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 200 200"
            class="empty-state-image"
          >
            <path
              d="M100 180c-2 0-4-.5-5.7-1.5C88 174.7 20 129.5 20 75c0-22.1 17.9-40 40-40 14.1 0 27.2 7.4 34.5 19.2l5.5 9 5.5-9C112.8 42.4 125.9 35 140 35c22.1 0 40 17.9 40 40 0 54.5-68 99.7-74.3 103.5-1.7 1-3.7 1.5-5.7 1.5z"
              fill="none"
              stroke="#E0E0E0"
              stroke-width="6"
            />

            <circle cx="100" cy="100" r="30" fill="#F5F5F5" />
            <path
              d="M85 100h30M100 85v30"
              stroke="#BDBDBD"
              stroke-width="4"
              stroke-linecap="round"
            />

            <circle
              cx="100"
              cy="100"
              r="50"
              stroke="#E0E0E0"
              stroke-width="4"
              stroke-dasharray="4 4"
              fill="none"
            />
          </svg>
          <h3>Your Wishlist is Empty</h3>
          <p>
            Save your favorite items to your wishlist and come back to them
            anytime.
          </p>
          <button class="primary-button golden" routerLink="/">
            Start Shopping
          </button>
        </div>

        <div
          class="wishlistSec"
          *ngIf="
            !isLoading &&
            wishlistData?.products &&
            wishlistData?.products.length > 0
          "
        >
          <ng-container *ngFor="let product of wishlistData?.products">
            <div class="slideItems">
              <div
                class="wishlist"
                [ngClass]="{ active: product.isfavorite }"
                (click)="addToWishlist(product.id, product.isfavorite)"
              >
                <img
                  class="fav"
                  src="\assets\images\favorite.png"
                  alt="wishlist icon"
                />
                <img
                  class="fav-gold"
                  src="\assets\images\favorite-gold.png"
                  alt="wishlist icon"
                />
              </div>
              <a class="productImage" [routerLink]="['/detail', product.slug]"
                ><img
                  src="{{ imageBase }}/{{ product?.image }}"
                  alt="product image"
                  class="img-fluid"
              /></a>
              <a [routerLink]="['/detail', product.slug]" class="productName">{{
                product?.productName
              }}</a>
              <a [routerLink]="['/detail', product.slug]" class="price">
                <div class="offer"><span>₹</span>{{ product?.price }}</div>
              </a>
              <div class="button" (click)="addToCart(product.id)">
                <span class="primary-button golden">Move to Cart</span>
              </div>
            </div>
          </ng-container>
        </div>
      </div>
    </div>
  </div>
</div>
