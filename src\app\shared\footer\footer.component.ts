import { ToastrService } from 'ngx-toastr';
import { ApiService } from './../../Services/api.service';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/Services/common.service';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
})
export class FooterComponent implements OnInit {
  emailForm!: FormGroup;
  showModal: boolean = false;
  loading: boolean = false;
  windowRef: any = window;

  constructor(
    private fb: FormBuilder,
    private ApiService: ApiService,
    private toast: ToastrService,
    private router: Router,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.emailForm = this.fb.group({
      email: [
        '',
        [
          Validators.required,
          Validators.pattern('[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}$'),
        ],
      ],
    });
  }

  get ef() {
    return this.emailForm.controls;
  }

  subsribe() {
    this.loading = true;
    let mail = this.emailForm.get('email')?.value;
    this.ApiService.postData(apiEndPoints.emailsubscription, {
      email: mail,
    }).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.emailForm.reset();
          this.toast.success('Subscription added successfully', 'Success');

          //Webengage
          this.windowRef.webengage.track('NewsLetter Subscribed', {
            Email: mail,
            Source: 'Website',
          });
          //Webengage

          this.loading = false;
        } else {
          this.loading = false;
          this.toast.error(res?.Message, 'Failed');
        }
      },
      (err) => {
        this.toast.error(err, 'Failed');
        this.loading = false;
      }
    );
  }

  shareToFacebook() {
    //Webengage Start
    this.windowRef.webengage.track('Social Media Clicked', {
      'Social Media': 'Facebook',
    });
    //Webengage End
    let url = 'https://www.facebook.com/ttdevassy';
    window.open(url, '_blank');
  }

  shareOnTwitter() {
    //Webengage Start
    this.windowRef.webengage.track('Social Media Clicked', {
      'Social Media': 'Twitter',
    });
    //Webengage End

    let url = 'https://twitter.com/ttdevassy';
    window.open(url, '_blank');
  }

  shareOnInstagram() {
    //Webengage Start
    this.windowRef.webengage.track('Social Media Clicked', {
      'Social Media': 'Instagram',
    });
    //Webengage End

    let url = 'https://www.instagram.com/ttdevassy/';
    window.open(url, '_blank');
  }

  shareOnPinterest() {
    //Webengage Start
    this.windowRef.webengage.track('Social Media Clicked', {
      'Social Media': 'Pinterest',
    });
    //Webengage End

    let url = 'https://www.pinterest.com/ttdevassy/';
    window.open(url, '_blank');
  }

  openOnPlayStore() {
    //Webengage Start
    this.windowRef.webengage.track('App Download', {
      Source: 'Play Store',
    });
    //Webengage End

    let url =
      'https://play.google.com/store/apps/details?id=com.ttdevassyjewellery.tt_devassy&pli=1';
    window.open(url, '_blank');
  }

  openAppStore() {
    //Webengage Start
    this.windowRef.webengage.track('App Download', {
      Source: 'App Store',
    });
    //Webengage End

    let url = 'https://apps.apple.com/in/app/tt-devassy/id6450362196';
    window.open(url, '_blank');
  }

  toggleModal() {
    this.commonService.sendClickEvent();
    this.showModal = true;
    this.footerClicked('', 'Sign Up');
  }

  openWhatsapp() {
    window.open('https://api.whatsapp.com/send?phone=9169160161', '_blank');
    this.router.navigate([''], { skipLocationChange: true });
  }

  footerClicked(url: string, name: string) {
    this.windowRef.webengage.track('Footer Clicked', {
      Name: name,
    });
    if (url) this.router.navigate([url]);
  }
}
