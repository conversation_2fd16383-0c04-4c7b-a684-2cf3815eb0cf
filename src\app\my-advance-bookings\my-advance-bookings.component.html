<div class="myAccount">
  <div class="container">
    <div class="title">
      <h1>My Account</h1>
    </div>
    <div class="contentSection">
      <div class="sideMenu">
        <app-sidebar></app-sidebar>
      </div>
      <div class="contents">
        <div class="head">
          <span>My Advance Booking</span>
        </div>
        <div class="goldSchemes" *ngFor="let item of data">
          <div class="data">
            <div class="leftSide">
              <div class="box">
                <div class="field">
                  <div class="text">Booking Id: </div>
                  <div class="name">{{item?.booking_id}}</div>
                </div>
                <div class="field">
                  <div class="text">Gold Rate:</div>
                  <div class="name">₹{{item?.gold_rate}}</div>
                </div>
                <div class="field">
                  <div class="text">Booked Weight(gm): </div>
                  <div class="name">{{item?.booked_weight}}</div>
                </div>

              </div>
              <div class="box">
                <div class="field">
                  <div class="text">Percentage:</div>
                  <div class="name">{{item?.percentage}}%</div>
                </div>
                <div class="field">
                  <div class="text">Percentage Amount:</div>
                  <div class="name">₹{{item?.percentage_amount}}</div>
                </div>
                <div class="field">
                  <div class="text">Period(days): </div>
                  <div class="name">{{item?.period}}</div>
                </div>
              </div>
              <div class="box">
                <div class="field">
                  <div class="text">Start Date:</div>
                  <div class="name">{{item?.start_date}}</div>
                </div>
                <div class="field">
                  <div class="text">due date:</div>
                  <div class="name">{{item?.due_date}}</div>
                </div>
                <div class="field">
                  <div class="text">Address:</div>
                  <div class="name">{{item?.address_1}}</div>
                </div>
              </div>
              <div class="box">
                <div class="field">
                  <div class="text">Store :</div>
                  <div class="name">{{item?.branch_name}}</div>
                </div>
                <div class="field">
                  <div class="text">Country :</div>
                  <div class="name">{{item?.country}}</div>
                </div>
                <div class="field">
                  <div class="text">State :</div>
                  <div class="name">{{item?.state_name}}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
