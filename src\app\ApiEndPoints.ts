export const apiEndPoints = {
  LOGIN: 'login',
  REGISTER: 'register',
  GET_PROFILE: 'getprofile',
  EDIT_PROFILE: 'editprofile',
  PRODUCT_LIST: 'product-list-web',
  PRODUCT_DETAIL: 'product-detail-web',
  WISHLIST: 'wishlist',
  GET_WISHLIST: 'get-wishlist',
  ADD_TO_CART: 'addtocart',
  GET_CART: 'get-cart',
  REMOVE_CART: 'remove-cartitem',
  GET_CATEGORIES: 'get-categories',
  MULTIPLE_CART_ADD: 'multiple-addtocart',
  WATCH_BRANDS: 'watch-brands',
  COUNTRY: 'country',
  STATE: 'state',
  ADD_ADDRESS: 'add-delivery-address',
  EDIT_ADDRESS: 'edit-delivery-address',
  GET_ADDRESS: 'get-delivery-address',
  DELETE_ADDRESS: 'delete-delivery-address',
  DEFAULT_ADDRESS: 'setdefault-delivery-address',
  GET_SINGLE_ADDRESS: 'get-single-delivery-address',
  CUS<PERSON>MIZE_ENQUIRY: 'customize-enquiry',
  HOM<PERSON>: 'home',
  ORDER_NOW_DETAILS: 'order-now-details',
  ORDER_NOW: 'order-now',
  GET_STORES: 'stores',
  GET_PERIOD: 'advance-period',
  ADVANCE_PERCENTAGE: 'advance-percentage',
  ADVANCE_BOOKING_STORE: 'advance-booking-store',
  ADVANCE_BOOKING_DETAILS: 'advance-booking-details',
  SEARCH: 'search',
  BOOK_APPOINTMENT: 'book-appointment',
  DELIVERY_CHECK: 'check-delivery-pincode',
  ADD_BILLING: 'add-billing-address',
  SCHEME_PLANS: 'scheme-plans',
  SCHEME_AMOUNTS: 'scheme-amounts',
  SCHEME_CALCULATOR: 'scheme-calculator',
  GIFT_CARD: 'giftCard',
  GIFT_DETAIL: 'giftCardDetail',
  EDIT_GIFT: 'personalizeGiftCard',
  REDEEM_GIFT: 'redeem-gift-card',
  SAVE_SCHEME: 'save-scheme',
  REFERAL_OCASSION: 'referral-occasion',
  REFERAL_REQUEST: 'referral-request',
  BLOGS: 'blog-list',
  BLOG_CATEGORY: 'blog-category/list',
  RECENT_BLOGS: 'recent-blogs',
  BLOG_DETAIL: 'blog-detail-web',
  GET_TnC: 'terms',
  BUYBACK_POLICY: 'buybackpolicy',
  CANCELLATION_POLICY: 'cancellationpolicy',
  REFUND_POLICY: 'refundpolicy',
  PRIVACY_POLICY: 'privacypolicy',
  ADD_COMMENT: 'postComment',
  CONTACT_US: 'contactUs',
  FAQ: 'faq',
  ZORA_COLLECTION: 'zoraCollections',
  scheme_first_transaction: 'scheme-first-transaction',
  existing_scheme: 'existing-scheme',
  existing_scheme_save: 'existing-scheme-save',
  my_gold_schemes: 'my-gold-schemes',
  my_gold_scheme_detail: 'my-gold-scheme-detail',
  scheme_paynow: 'scheme-paynow',
  scheme_paynow_success: 'scheme-paynow-success',
  redeem_scheme: 'redeem-scheme',
  redeem_scheme_verify: 'redeem-scheme-verify',
  my_wallet: 'myWallet',
  get_cart_details: 'get-cart-details',
  advance_booking_payment: 'advance-booking-payment',
  my_advance_booking: 'my-advance-bookings',
  goldSchemeFaq: 'goldSchemeFaq',
  checkout: 'checkout',
  order_payment_success: 'order-payment-success',
  emailsubscription: 'emailsubscription',
  my_orders: 'my-orders',
  order_details: 'order-details',
  virtualShop: 'virtualShop',
  jewelleryDesign: 'jewelleryDesign',
  ourCertification: 'ourCertification',
  ringSizeGuide: 'ringSizeGuide',
  bangleSizeGuide: 'bangleSizeGuide',
  diamondEducation: 'diamondEducation',
  goldpurities: 'goldpurities',
  cancel_order: 'cancel-order',
  post_testimonial: 'postTestimonials',
  return_order: 'return-order',
  advanceBookingTerms: 'advanceBookingTerms',
  image_search: 'image-search',
  order_now_details: 'ordernow-details',
  order_now_pay_balance: 'order-now-pay-balance',
  giftcard_order_details: 'giftcard-order-details',
  check_upi_status: 'check-upi-status',
  getSchemePlans: 'getSchemePlans',
  goldrates: 'goldrates',
  verify_vpa: 'verify-vpa',
  page_banners: 'page-banners',
  ttbrands: 'ttbrands',
  cartcount: 'cart-count',
  store_details: 'store-details',
  brand_page: 'brand-page',
  gift_card_page: 'giftCardPage',
  advance_booking: 'advancebooking-page',
  gold_scheme: 'goldscheme-page',
  request_otp: 'request-otp',
  verify_otp: 'verify-otp',
  request_scheme_otp: 'request-scheme-otp',
  digitalgold_page: 'digitalgold-page',
  aboutus_page: 'aboutus-page',
  newSaveScheme: 'new-save-scheme',
  newSchemePayNow: 'new-scheme-paynow',
  saveShemeRzp: 'save-scheme-razorpay',
  goldSchemeSuccess: 'goldscheme-payment-success',
  schemePayNowRzp: 'scheme-paynow-razorpay',
  careerPage: 'career-page',
  careerList: 'career-list',
  submitApplication: 'submit-application',
};
