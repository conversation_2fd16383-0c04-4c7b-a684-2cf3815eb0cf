import { Component, Inject, OnInit, Renderer2 } from '@angular/core';
import { Router } from '@angular/router';
import { CommonService } from './Services/common.service';
import { GoogleTagManagerService } from './Services/google-tag-manager.service';
import { Meta } from '@angular/platform-browser';
import { DOCUMENT } from '@angular/common';
import { environment } from 'src/environments/environment';

declare global {
  interface Window {
    webengage: any;
  }
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  currentRoute: any;
  title = 'tt-devassy';
  isHomePage: boolean = false;
  constructor(
    private router: Router,
    private commonService: CommonService,
    private googleTagService: GoogleTagManagerService,
    private meta: Meta,
    private renderer: Renderer2,
    @Inject(DOCUMENT) private document: Document
  ) {
    this.router.events.subscribe((value: any) => {
      this.isHomePage = this.router.url === '/';
      let array = window.location.pathname.split('/');
      this.currentRoute = array[1];
    });

    this.meta.addTags([
      { name: 'author', content: 'TT Devassy' },
      {
        name: 'description',
        content:
          'Discover exquisitely crafted jewellery by TT Devassy. Explore our stunning collection of rings, necklaces, bracelets, and more.',
      },
      {
        name: 'keywords',
        content:
          'TT Devassy, jewellery, rings, necklaces, bracelets, fine jewellery',
      },
      { name: 'robots', content: 'index, follow' },
      { name: 'googlebot', content: 'index, follow' },
      { name: 'og:title', content: 'TT Devassy Jewellery' },
      {
        name: 'og:description',
        content:
          'Explore the finest collection of jewellery by TT Devassy. Find rings, necklaces, bracelets, and more to elevate your style.',
      },
      { name: 'og:image', content: '/assets/images/logo.svg' },
      { name: 'og:type', content: 'website' },
      { name: 'og:url', content: 'https://beta.ttdevassyjewellery.com' },
      { name: 'og:site_name', content: 'TT Devassy Jewellery' },
      { rel: 'canonical', href: 'https://beta.ttdevassyjewellery.com' },
    ]);
  }

  ngOnInit(): void {
    this.checkUser();
    this.getGoogleTgas();
    this.initializeSliderInMobile();
    // this.initializeNotifications();
    if (environment.production) {
      this.insertGtmScript();
      this.insertTag();
    }
  }

  initializeSliderInMobile() {
    window.addEventListener('resize', (e: any) => {
      if (e.target.window.visualViewport.width < 1200) {
        localStorage.setItem('ismObile', JSON.stringify(true));
        this.commonService.checkMobile(true);
      } else {
        localStorage.setItem('ismObile', JSON.stringify(false));
        this.commonService.checkMobile(false);
      }
    });
  }

  checkUser() {
    const isLoggedIn: boolean | string | null = localStorage.getItem('isLogged')
      ? localStorage.getItem('isLogged')
      : false;
    if (!isLoggedIn && window.location.hostname.includes('beta')) {
      this.commonService.getAuthorization();
    }
  }

  getGoogleTgas() {
    this.googleTagService.loadTags();
  }

  insertGtmScript(): void {
    const script = this.renderer.createElement('script');
    script.innerHTML = environment.gtmScript;
    this.renderer.appendChild(this.document.body, script);
  }

  insertTag(): void {
    const script = this.renderer.createElement('script');
    script.innerHTML = environment.tagScript;
    this.renderer.appendChild(this.document.body, script);
  }

  private initializeNotifications() {
    if ('Notification' in window) {
      Notification.requestPermission().then((permission) => {
        if (permission === 'granted') {
        }
      });
    }
  }

  private initializeWebEngage() {
    if ('Notification' in window) {
      window.webengage?.notifications.options({
        serviceWorkerPath: '/service-worker.js',
        scope: '/',
        showPrompt: true,
        promptEnabled: true,
        promptDelay: 2000,
        notificationClickCallback: (notificationData: any) => {
        },
      });
    }
  }
}
