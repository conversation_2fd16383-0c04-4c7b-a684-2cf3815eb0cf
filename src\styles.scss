/* You can add global styles to this file, and also import other style files */
@import 'swiper/scss';
@import 'swiper/scss/navigation';
@import 'swiper/scss/pagination';

@font-face {
  font-family: "Gotham";
  src:
    url("assets/fonts/Gotham-Book.woff") format("woff");
  font-weight: 400;
}

@font-face {
  font-family: "Gotham";
  src:
    url("assets/fonts/GothamHTF-Medium.woff") format("woff");
  font-weight: 500;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-text-size-adjust: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

$fontFamily: 'Gotham';
$color: #1D1D1D;
$whiteClr: #fff;
$mainClr: #1D1D1D;
$hoverBg: #D2AB66;

body {
  margin: 0;
  padding: 0;
  background: #fff;
  font-family: 'Gotham';
  overflow-x: hidden;
  color: $color;
  width: 100%;
  font-size: 18px;
  font-weight: normal;
  word-wrap: break-word;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0px;
  color: $color;
  line-height: normal;
  font-weight: normal;
  margin-bottom: 10px;
  font-family: 'Libre Bodoni';
}

h1 {
  font-size: 50px;
}

h2 {
  font-size: 44px;
}

h3 {
  font-size: 37px;
}

h4 {
  font-size: 34px;
}

h5 {
  font-size: 30px;
}

h6 {
  font-size: 24px;
}

hr {
  margin: 10px 0px;
  border: 0px;
  border-bottom: 1px dashed #828282;
}

span {
  display: inline-block;
}

span,
p,
a {
  word-break: break-word;
}

p,
ul,
ol {
  margin: 0px;
  line-height: 25px;
  padding: 0px;
}

ul *,
ol * {
  margin: 0px;
  padding: 0px;
}

ol,
ul {
  padding-left: 25px;
}

ul {
  display: block;
}

ul li {
  line-height: 30px;
}

a img {
  border: 0px;
}

img {
  max-width: 100%;
  height: auto;
}

p,
ul,
ol {
  margin-bottom: 5px;
}

.clr {
  display: block;
  float: none;
  clear: both;
}

.c {
  overflow: hidden;
  float: none;
}

.underline {
  text-decoration: underline;
}

.left,
.flot_left {
  float: left;
}

.right,
.flot_right {
  float: right;
}

.a_left {
  text-align: left;
}

.a_center {
  text-align: center;
}

.a_right {
  text-align: right;
}

a {
  text-decoration: none;
  display: inline-block;
}

.hidden {
  display: none;
}

.italic {
  font-style: italic;
}

.bold {
  font-weight: bold;
}

table {
  border-spacing: 0px;
  border-collapse: collapse;
}

input[type="text"],
input[type="url"],
input[type="tel"],
input[type="email"],
input[type="password"],
input[type="number"],
select,
textarea,
input[type="date"] {
  background: transparent;
  border: none;
  border-bottom: 1px solid #F3F3F3;
  color: #535050;
  font-size: 12px;
  padding: 12px 15px 12px 12px;
  resize: vertical;
  width: 100%;
  font-weight: normal;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: none;
  font-family: "Gotham";
  margin-bottom: 15px;
}

textarea {
  width: 100%;
}

select {
  padding: 12px;
  width: 100%;
}

input[type="button"],
input[type="submit"],
input[type="reset"],
.primary-button,
.btn {
  color: #fff;
  display: inline-block;
  cursor: pointer;
  border: 1px solid $color;
  font-size: 16px;
  text-align: center;
  padding: 15px 10px;
  background: #1D1D1D;
  width: 100%;
  font-family: "Gotham";
  font-weight: 400;
}

input[type="button"]:hover,
input[type="submit"]:hover,
input[type="reset"]:hover,
.primary-button:hover,
.btn:hover,
.primary-button:focus,
.btn:focus {
  background: #D2AB66;
  border: 1px solid $hoverBg;
  //color: $whiteClr;
  outline: none;
}

.primary-button.golden {
  background: #D2AB66;
  border: 1px solid #D2AB66;

  &:hover {
    background: #1D1D1D;
    border: 1px solid $color;
  }
}

.primary-button.white {
  background: transparent;
  color: #000;
  border: 1px solid #000;

  &:hover {
    color: #D2AB66;
    border: 1px solid #D2AB66;
  }
}

button:focus {
  outline: none;
}

.gradient {
  background: #fff;
}

.form-control {
  border-radius: 0px;
  -moz-border-radius: 0px;
  -ms-border-radius: 0px;
  -o-border-radius: 0px;
  -webkit-border-radius: 0px;
  box-shadow: 0 0 0;
  height: auto;
}

a:focus {
  outline: none;
  text-decoration: none;
}

a {
  color: $color;
  cursor: pointer;
  transition: all linear 0.2s;
  -moz-transition: all linear 0.2s;
  -ms-transition: all linear 0.2s;
  -o-transition: all linear 0.2s;
  -webkit-transition: all linear 0.2s;
}

a:not([href]):not([tabindex]) {
  color: $color;
}

a:not([href]):not([tabindex]):focus,
a:not([href]):not([tabindex]):hover {
  color: $color;
}

a:hover,
a:focus {
  text-decoration: none;
}

a:hover {
  color: $hoverBg;
}

.cfx::after {
  clear: both;
  content: "+";
  display: block;
  height: 0;
  visibility: hidden;
}

.mobile {
  display: none;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 43px;
}

#mob_menu.show_menu {
  left: 0px;
}

.mobileBtn {
  display: none;
}

#mob_menu {
  position: fixed;
  background-color: #ffffffe3;
  height: 100%;
  z-index: 999999;
  width: 280px;
  color: #000;
  top: 0;
  -webkit-transition: all 0.3s ease;
  transition: all 0.4s ease;
  opacity: 1;
  left: -320px;
  overflow: auto;

  .menu {
    padding: 10px 20px;
    display: block;
  }

  .closeBox {
    text-align: right;
    padding: 10px 20px;
  }
}

.btn-check:focus+.btn,
.btn:focus {
  box-shadow: none;
}

.close {
  position: absolute;
  right: -20px;
  top: -4px;
  opacity: 1;
}

.modal {

  .modal-content {
    height: max-content;
  }

  .modal-header {
    border: none;
    justify-content: flex-end;
    padding: 16px 25px;
  }

  .modal-dialog {
    max-width: initial;
    width: 390px;
    margin: 0 auto;
  }

  .modal-footer {
    border: none;
    justify-content: center;
  }

  .modal-body {
    padding: 0px 25px 10px 25px;
  }

  .modal-footer {
    padding: 10px 40px 40px 40px;
  }

  .subscribe {
    padding: 20px 30px;
    text-align: center;
  }

}

input[type=number] {
  -moz-appearance: textfield;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.swiper-pagination-bullet {
  background-color: #D9D9D9;
}

.swiper-pagination-bullet-active {
  background-color: #fff;
}

.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal>.swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: 30px;
}

.swiper-button-prev {
  background-image: url(assets/images/arrow-left.svg);
  width: 51px;
  height: 50px;
  left: 90px;
  background-repeat: no-repeat;
  position: unset;
  margin: 0 15px;

  &::after {
    display: none;
  }

  &:hover {
    background-image: url(assets/images/arrow-left-gold.svg);
  }
}

.swiper-button-next {
  background-image: url(assets/images/arrow-right.svg);
  width: 51px;
  height: 50px;
  right: 90px;
  background-repeat: no-repeat;
  position: unset;
  margin: 0 15px;

  &::after {
    display: none;
  }

  &:hover {
    background-image: url(assets/images/arrow-right-gold.svg);
  }
}

.title {
  text-align: center;
  padding-bottom: 25px;
  max-width: 660px;
  margin: 0 auto;

  h2 {
    margin-bottom: 25px;
  }

  p {
    margin-bottom: 25px;
  }
}

.bannerSection {

  .swiper-button-next,
  .swiper-button-prev {
    position: absolute;
  }
}

.trendingSection,
.brandsLove,
.hotCategory,
.clientSay {
  .swiper {
    padding-bottom: 20px;
  }

  .swiper-slide {
    height: auto;
  }

  .swiper-button-next {
    background-image: url(assets/images/arrow-right-gold.svg);
    top: auto;
    bottom: 0;
    right: auto;
    left: 53%;
    background-size: contain;
  }

  .swiper-button-prev {
    background-image: url(assets/images/arrow-left-gold.svg);
    top: auto;
    bottom: 0;
    left: 47%;
    right: auto;
    background-size: contain;
  }
}

.policySection {
  ul {
    list-style: none;

    li {
      position: relative;
    }

    li::before {
      content: '\2022';
      color: #000000;
      font-weight: bold;
      display: inline-block;
      width: 1.5em;
      margin-left: -1em;

      position: absolute;
      top: 0;
      left: 0;
    }
  }
}

// .hotCategory,
// .clientSay {
//   .swiper-button-next {
//     background-image: url(assets/images/arrow-right.svg);
//   }

//   .swiper-button-prev {
//     background-image: url(assets/images/arrow-left.svg);
//   }
// }

.clientSay {
  .swiper-slide {
    padding: 35px 30px;
    border-left: 1px solid #CCCCCC;
    pointer-events: none;
  }

  .quote {
    display: none;
    width: 47px;
    margin: 25px auto;
  }

  .swiper-slide-active .quote {
    display: block;
  }

}

.featured {
  padding: 100px 0;
  background: #FFFBF3;
  margin-top: 100px;

  .swiper {
    padding-right: 40px;
  }

  .swiper-button-next {
    position: unset;
    margin: 0 10px;
  }

  .swiper-button-prev {
    position: unset;
    margin: 0 10px;
  }

  .slide {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
  }

  .swiper-button-next {
    background-image: url(assets/images/arrow-right-gold.svg);
  }

  .swiper-button-prev {
    background-image: url(assets/images/arrow-left-gold.svg);
  }
}

.breadCrumb {
  display: flex;
  flex-wrap: wrap;
  font-size: 12px;
  padding-top: 10px;

  a {
    padding-right: 20px;
    margin-right: 20px;
    position: relative;
    margin-top: 10px;

    &:before {
      background-image: url(/assets/images/angle-right.svg);
      content: "";
      position: absolute;
      right: -10px;
      top: 50%;
      width: 18px;
      height: 18px;
      transform: translateY(-50%);
    }
  }

  div {
    color: #D2AB66;
    margin-top: 10px;
  }
}

.offSection {
  display: block;
  position: relative;
  margin-bottom: 100px; 

  .image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: left;
  }

  h2 {
    color: #fff;
  }

  p {
    margin-bottom: 20px;
  }

  .content {
    position: absolute;
    left: 50%;
    top: 50%;
    width: 50%;
    transform: translateY(-50%);
    padding-right: 12%;
    color: #fff;
  }

  .explore {
    display: flex;

    span {
      padding-right: 10px;
    }
  }
}

.twoLine {
  height: 41px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.form input {
  border: 1px solid #c8c4c4;
}

.form .datepicker-container.datepicker-default {
  width: 100%;
}

.timepicker__header,
.clock-face__number>span.active,
.clock-face__clock-hand {
  background-color: #000 !important;
}

.timepicker-button {
  color: #000 !important;
}

.clock-face__clock-hand_minute:before {
  border-color: #000 !important;
}

.timepicker-dial__time {
  font-size: 20px !important;
}

.timepicker-dial__control {
  font-size: 25px !important;
  width: 35px !important;
}

.clock-face__number--outer {
  height: calc(220px / 2 - 20px) !important;
}

.clock-face {
  width: 220px !important;
  height: 220px !important;
}

.clock-face__clock-hand {
  height: 63px !important;
  top: calc(50% - 63px) !important;
}

ng-otp-input {
  width: 100%;
  padding-bottom: 20px;
}

.ng-otp-input-wrapper {
  display: flex;
  justify-content: space-between;
}

.otp-input {
  width: 60px !important;
  border-radius: 0 !important;
  margin-bottom: 0 !important;
}


.detailSection {

  .lib-ngx-image-zoom {
    height: 436px !important;
  }

  .ngxImageZoomContainer {
    width: 100% !important;
    height: 100% !important;
  }

  .productSlide .swiper-slide {
    height: 100px;

    img {
      max-width: 100%;
      height: 100%;
      width: 100%;
      object-fit: cover;
      object-position: top;
      border: 1px solid #D5DBDE;
    }
  }
}

.blogbanner {
  .swiper-button-prev {
    left: -130px;
  }

  .swiper-button-next {
    right: -130px;
  }

  .swiper-slide.swiper-slide-next {
    opacity: .5;
  }

  .swiper-slide.swiper-slide-prev {
    opacity: .5;
  }

  .swiper-slide-active {
    .head {
      top: 90px !important;
      transition: ease .9s;
      opacity: 1 !important;
    }

    .content {
      top: 60% !important;
      transition: ease .9s;
      opacity: 1 !important;
    }
  }
}

.policySection {
  padding: 40px 0;

  h2 {
    margin-bottom: 30px;
  }

  p {
    margin-bottom: 20px;
  }

  h6 {
    font-family: 'Gotham';
    font-size: 20px;
    margin-bottom: 20px;
    font-weight: 600;
  }
}

.ng-dropdown-panel {
  border: 1px solid #ddd;
  left: 0;
  top: 48px;
  padding: 3px 0px;
  background: #fff;
}


.ng-dropdown-panel .ng-dropdown-panel-items .ng-option {
  padding: 4px 10px 4px;
  font-size: 14px;
}

.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value {
  font-size: 14px;
}

.ng-dropdown-panel .ng-dropdown-panel-items .ng-option:hover {
  background: #D2AB66;
  color: #fff;
}

.ng-select-container::before {
  content: "";
  position: absolute;
  right: 10px;
  top: 14px;
  background-image: url(assets/images/angle-down.svg);
  width: 20px;
  height: 19px;
}

.ng-select {
  padding: 0 !important;
  margin-bottom: 15px;
}

.ng-select .ng-select-container {
  padding: 12px 20px;
}

.field.drop .text-danger {
  bottom: -17px !important;
}

.field.time .text-danger {
  bottom: -3px;
}

.bookAppointment {

  .selectOption .ng-select-container,
  .ng-select .ng-select-container,
  .form input {
    border: 1px solid #BBBBBB;
  }
}

.field {
  position: relative;

  .text-danger {
    position: absolute;
    bottom: -17px;
    font-size: 13px;
    width: 100%;
    left: 0;
  }
}

.priceRange {
  .ngx-slider .ngx-slider-pointer {
    background-color: #CB9F52 !important;
  }

  .ngx-slider .ngx-slider-pointer.ngx-slider-active:after {
    background-color: #996917;
  }

  .ngx-slider .ngx-slider-selection {
    background: #CB9F52 !important;
  }

}

.schemeCalculator {
  .rate ng-select.filtername .ng-select-container::before {
    display: none;
  }

  .ng-arrow-wrapper {
    display: none;
  }

  .ng-select .ng-select-container {
    padding: 12px 20px;
    height: 100%;
  }
}



.ngx-pagination {
  text-align: center;
  padding-left: 0;
  padding-top: 20px;

  .current {
    background: #D2AB66 !important;
  }
}



.detailSection .section .productDetail .priceBreakUp .priceBreaks.active .swiper-slide {
  height: auto;
}

.loader {
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  height: 100%;
  z-index: 99;
  background: #000000de;
  text-align: center;
  transition: ease-out .4s;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 200px;
  }
}

._10b4 {
  max-height: 100% !important;
}

._2lqg {
  height: 100% !important;
}

@media screen and (min-width: 1919px) {
  .container {
    max-width: 1600px;
  }
}

@media screen and (max-width: 1200px) {

  .detailSection .productSlide .swiper-slide {
    height: 130px;

    img {
      object-position: center;
    }
  }
}

@media screen and (max-width: 992px) {
  body {
    font-size: 16px;
  }

  .container {
    padding: 0 16px;
  }

  .bannerSection .swiper-button-next,
  .bannerSection .swiper-button-prev {
    display: none;
  }



  .title {
    padding-bottom: 15px;

    h2 {
      margin-bottom: 10px;
    }
  }

  .breadCrumb {
    padding-top: 15px;
  }

  // .swiper-button-prev,
  // .swiper-button-next {
  //   display: none;
  // }
  .swiper-pagination-fraction,
  .swiper-pagination-custom,
  .swiper-horizontal>.swiper-pagination-bullets,
  .swiper-pagination-bullets.swiper-pagination-horizontal {
    bottom: 0px;
  }

  input[type=button],
  input[type=submit],
  input[type=reset],
  .primary-button,
  .btn {
    font-size: 14px;
    padding: 10px 8px;
  }

  h1 {
    font-size: 35px;
  }

  h2 {
    font-size: 30px;
  }

  h3 {
    font-size: 28px;
  }

  h4 {
    font-size: 25px;
  }

  h5 {
    font-size: 20px;
  }

  h6 {
    font-size: 16px;
  }

  .offSection {
    margin-bottom: 40px;
  }
}

@media screen and (max-width: 640px) {
  body {
    font-size: 14px;
  }

  .swiper-button-prev {
    left: 0;
    width: 31px;
    background-size: contain;
    height: 30px;
    margin: 0 5px;
  }

  .swiper-button-next {
    right: 0;
    width: 31px;
    background-size: contain;
    height: 30px;
    margin: 0 5px;
  }

  .offSection {
    .content {
      width: auto;
      left: 10%;
    }
  }

  .blogbanner .swiper-slide-active .head {
    top: 30px !important;
  }
}

@media screen and (max-width: 480px) {

  h1 {
    font-size: 30px;
  }

  h2 {
    font-size: 25px;
  }

  h3 {
    font-size: 22px;
  }

  h4 {
    font-size: 20px;
  }

  h5 {
    font-size: 18px;
  }

  h6 {
    font-size: 14px;
  }

  .clientSay .swiper-slide-active {
    margin-top: 10px;
  }
}


input[type="radio"] {

  &:focus,
  &:hover {
    outline: 0 !important;
    border: none !important;
  }
}