import { ApiService } from 'src/app/Services/api.service';
import { Component, NgZone, OnInit } from '@angular/core';
import firebase from 'firebase/app';
import 'firebase/auth';
import 'firebase/firestore';
import { NgOtpInputConfig } from 'ng-otp-input';
import { apiEndPoints } from '../ApiEndPoints';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
declare var Razorpay: any;

@Component({
  selector: 'app-my-gold-schemes',
  templateUrl: './my-gold-schemes.component.html',
  styleUrls: ['./my-gold-schemes.component.scss'],
})
export class MyGoldSchemesComponent implements OnInit {
  schemeList: boolean = true;
  PopupPaymentOtp: boolean = false;
  schemeOtp: any;
  windowRef: any;

  otpInputConfig: NgOtpInputConfig = {
    length: 6,
  };

  addAccount: any = false;
  viewDetails: any = false;
  mySchemes: any;
  schemeDetails: any;
  schemeId: any;
  existingScheme: any;
  showOtp: boolean = false;
  verificationId: any;
  schemeUpi: any;
  redeemOtp: any;
  ID: any;
  PopupUpi: boolean = false;
  upiId: any;
  validation: any;
  upiIntentUrl: any;
  statusCheckInterval: any;
  private rzp: any;
  isProcessingPayment: boolean = false;
  paymentMessage: string = '';

  constructor(
    private ApiService: ApiService,
    private toast: ToastrService,
    private router: Router,
    private ngZone: NgZone,
    private spinner: NgxSpinnerService
  ) {}

  ngOnInit(): void {
    if (localStorage.getItem('schemePopup') == '0') {
      this.schemeList = false;
      this.addAccount = true;
    }
    this.getMySchemes();
  }

  getMySchemes() {
    this.ApiService.getData(apiEndPoints.my_gold_schemes).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.mySchemes = res?.Data;
        }
      }
    );
  }

  getExisting() {
    if (!this.schemeId) {
      this.validation = 'Scheme Id is required';
      return;
    }

    this.ApiService.postData(apiEndPoints.existing_scheme, {
      scheme_id: this.schemeId,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.existingScheme = res?.Data;
        const mobile =
          this.existingScheme?.country_code + this.existingScheme?.mobile;

        this.windowRef = window;
        this.windowRef.reCaptchaVerifier = new firebase.auth.RecaptchaVerifier(
          'recaptcha-container',
          {
            size: 'invisible',
            callback: (response: any) => {},
          }
        );
        this.windowRef.reCaptchaVerifier.render().then((widget: any) => {
          this.windowRef.recaptchaWidgetId = widget;
        });
        const appVerifier = this.windowRef.reCaptchaVerifier;
        // firebase.auth().signInWithPhoneNumber(mobile, appVerifier).then((confirmationResult) => {
        //   this.verificationId = confirmationResult.verificationId;
        //   let lastThreeDigits = mobile.slice(-3);
        //   let maskedMobile = '*'.repeat(mobile.length - 3) + lastThreeDigits;
        //   this.toast.success(`OTP successfully sent to ${maskedMobile}`)
        //   this.showOtp = true;
        // }).catch((error: any) => {
        //   this.windowRef.reCaptchaVerifier = null; // remove the reCAPTCHA container
        //   document.getElementById('recaptcha-container')?.remove();
        // })
        const data = {
          mobile: this.existingScheme?.mobile,
          country_code: this.existingScheme?.country_code,
        };
        this.ApiService.requestOTP(
          apiEndPoints.request_scheme_otp,
          data
        ).subscribe(
          (res) => {
            if (res.ErrorCode === 0) {
              let lastThreeDigits = mobile.slice(-3);
              let maskedMobile =
                '*'.repeat(mobile.length - 3) + lastThreeDigits;
              this.toast.success(`OTP successfully sent to ${maskedMobile}`);
              this.showOtp = true;
            } else {
              this.toast.error('Something went wrong', 'Error');
              this.windowRef.reCaptchaVerifier = null;
              document.getElementById('recaptcha-container')?.remove();
            }
          },
          (error) => {
            this.toast.error(error, 'Error');
            this.windowRef.reCaptchaVerifier = null;
            document.getElementById('recaptcha-container')?.remove();
          }
        );
      } else this.toast.error(res?.Message);
    });
  }

  verify() {
    //const credentials = firebase.auth.PhoneAuthProvider.credential(this.verificationId, this.schemeOtp);
    // firebase.auth().signInWithCredential(credentials).then((res) => {
    //   const userId = res.user?.uid
    //   this.ApiService.postData(apiEndPoints.existing_scheme_save, { uuid: userId, scheme_id: this.existingScheme?.scheme_id }).subscribe((res: any) => {
    //     if (res?.ErrorCode == 0) {
    //       this.addAccount = false;
    //       this.getMySchemes();
    //       this.schemeList = true;
    //     } else this.toast.error(res?.Message)
    //   })
    // }).catch((error: any) => {
    //   // this.toast.error(error.message)
    // })
    this.ApiService.postData(apiEndPoints.existing_scheme_save, {
      scheme_id: this.existingScheme?.scheme_id,
      otp: this.schemeOtp,
      mobile: this.existingScheme?.mobile,
      country_code: this.existingScheme?.country_code,
    }).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.addAccount = false;
          this.getMySchemes();
          this.schemeList = true;
        } else this.toast.error(res?.Message);
      },
      (error) => {
        this.toast.error(error, 'Error');
      }
    );
  }

  payNow(id: any) {
    this.ID = id;
    // if (this.upiId) {
    //   this.ApiService.postData(apiEndPoints.verify_vpa, {
    //     vpa: this.upiId,
    //   }).subscribe((res: any) => {
    //     if (res?.ErrorCode == 0) {

    //Razorpay Payment
    this.isProcessingPayment = true;
    this.spinner.show();
    this.paymentMessage = 'Initializing payment...';

    this.ApiService.postData(apiEndPoints.schemePayNowRzp, {
      scheme_id: this.ID,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        const paymentDetails = res?.Data?.data;
        const options = {
          key: paymentDetails.api_key,
          order_id: paymentDetails.razorpay_order_id,
          amount: paymentDetails.amount, // amount in paise
          currency: 'INR',
          name: 'TT Devassy',
          description: 'Gold Scheme',
          handler: (response: any) => {
            // Handle success
            this.paymentMessage = 'Verifying payment...';

            this.ApiService.postData(apiEndPoints.goldSchemeSuccess, {
              order_number: paymentDetails.order_number,
              ...response,
            }).subscribe({
              next: (res: any) => {
                this.isProcessingPayment = false;
                this.spinner.hide();
                
                if (res?.ErrorCode == 0) {
                  this.toast.success('Payment Success');
                  this.router.navigate(['/my-gold-schemes']);
                } else {
                  this.toast.error('Payment Failed');
                  this.router.navigate(['/gold-account']);
                }
              },
              error: (error) => {
                this.handlePaymentError();
              }
            });
          },
          prefill: {
            name: null,
            email: null,
            contact: null,
          },
          // notes: {
          //   address: this.billingForm.get('address')?.value,
          // },
          theme: {
            color: '#F37254',
          },
          modal: {
            ondismiss: () => {
              this.ngZone.run(() => {
                this.handlePaymentFailure();
              });
            },
          },
        };

        this.rzp = new Razorpay(options);
        this.rzp.on('payment.failed', (response: any) => {
          this.ngZone.run(() => {
            this.handlePaymentFailure();
          });
        });
        this.rzp.open();
      }
    });

    //UPI Payment
    // this.ApiService.postData(apiEndPoints.newSchemePayNow, {
    //   scheme_id: this.ID,
    //   // payer_vpa: this.upiId,
    // }).subscribe((res: any) => {
    //   if (res?.ErrorCode == 0) {
    //     // this.spinner.show();
    //     this.schemeUpi = res?.Data;
    //     try {
    //       const paymentDetails = JSON.parse(res.Data.payment_details);
    //       this.upiIntentUrl = paymentDetails.Response.Body.intentUrl;
    //       this.PopupUpi = !this.PopupUpi;
    //     } catch (error) {
    //     }

    //     // Constants for timing
    //     const INTERVAL_TIME = 10000; // 10 seconds
    //     const MAX_DURATION = 300000; // 5 minutes in milliseconds
    //     const startTime = Date.now();

    //     let intervalId = setInterval(() => {
    //       if (Date.now() - startTime >= MAX_DURATION) {
    //         clearInterval(intervalId);
    //         this.spinner.hide();
    //         this.toast.error(
    //           'Payment verification timeout. Please check your payment status in My Gold Schemes.'
    //         );
    //         this.popUpClose('upi');
    //         return;
    //       }

    //       this.ApiService.postData(apiEndPoints.check_upi_status, {
    //         order_id: this.schemeUpi?.order_id,
    //       }).subscribe((res: any) => {
    //         if (res?.ErrorCode == 0) {
    //           if (res?.Data?.transaction_status == 'success') {
    //             // this.spinner.hide();
    //             this.toast.success(res?.Message);
    //             clearInterval(intervalId);
    //             this.popUpClose('upi');
    //           } else if (res?.Data?.transaction_status == 'failed') {
    //             // this.spinner.hide();
    //             this.toast.error(res?.Message);
    //             this.popUpClose('upi');
    //           }
    //         }
    //       });
    //     }, INTERVAL_TIME);
    //     this.statusCheckInterval = intervalId;
    //   } else this.toast.error(res?.Message);
    // });
    //     } else {
    //       this.toast.error(res?.Message);
    //     }
    //   });
    // } else this.toast.error('Enter UPI Id');
  }

  redeemScheme() {
    this.ApiService.postData(apiEndPoints.redeem_scheme, {
      scheme_id: this.schemeDetails?.id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.existingScheme = res?.Data;
        const mobile = '+919048837780';

        this.windowRef = window;
        this.windowRef.reCaptchaVerifier = new firebase.auth.RecaptchaVerifier(
          'recaptcha-container',
          {
            size: 'invisible',
            callback: (response: any) => {},
          }
        );
        this.windowRef.reCaptchaVerifier.render().then((widget: any) => {
          this.windowRef.recaptchaWidgetId = widget;
        });
        const appVerifier = this.windowRef.reCaptchaVerifier;
        // firebase.auth().signInWithPhoneNumber(mobile, appVerifier).then((confirmationResult) => {
        //   this.verificationId = confirmationResult.verificationId;
        //   this.PopupPaymentOtp = !this.PopupPaymentOtp;
        //   let lastThreeDigits = mobile.slice(-3);
        //   let maskedMobile = '*'.repeat(mobile.length - 3) + lastThreeDigits;
        //   this.toast.success(`OTP successfully sent to ${maskedMobile}`)
        // }).catch((error: any) => {
        //   this.windowRef.reCaptchaVerifier = null;
        //   document.getElementById('recaptcha-container')?.remove();
        //   this.toast.error(error.message);
        // })

        const data = {
          mobile: this.existingScheme?.mobile,
          country_code: this.existingScheme?.country_code,
        };
        this.ApiService.requestOTP(
          apiEndPoints.request_scheme_otp,
          data
        ).subscribe(
          (res) => {
            if (res.ErrorCode === 0) {
              let lastThreeDigits = mobile.slice(-3);
              let maskedMobile =
                '*'.repeat(mobile.length - 3) + lastThreeDigits;
              this.toast.success(`OTP successfully sent to ${maskedMobile}`);
              this.showOtp = true;
            } else {
              this.toast.error('Something went wrong', 'Error');
              this.windowRef.reCaptchaVerifier = null;
              document.getElementById('recaptcha-container')?.remove();
            }
          },
          (error) => {
            this.toast.error(error, 'Error');
            this.windowRef.reCaptchaVerifier = null;
            document.getElementById('recaptcha-container')?.remove();
          }
        );
      } else this.toast.error(res?.Message);
    });
  }

  schemeOtpVerify() {
    // const credentials = firebase.auth.PhoneAuthProvider.credential(this.verificationId, this.redeemOtp);
    // firebase.auth().signInWithCredential(credentials).then((res) => {
    //   const userId = res.user?.uid
    //   this.ApiService.postData(apiEndPoints.redeem_scheme_verify, { uuid: userId, scheme_id: this.existingScheme?.scheme_id }).subscribe((res: any) => {
    //     if (res?.ErrorCode == 0) {
    //       this.toast.success(res?.Message)
    //       this.router.navigateByUrl('/wallet')
    //     } else this.toast.error(res?.Message)
    //   })
    // }).catch((error: any) => {
    //   this.toast.error(error.message)
    // })
    this.ApiService.postData(apiEndPoints.existing_scheme_save, {
      scheme_id: this.existingScheme?.scheme_id,
      otp: this.schemeOtp,
      mobile: this.existingScheme?.mobile,
      country_code: this.existingScheme?.country_code,
    }).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success(res?.Message);
          this.router.navigateByUrl('/wallet');
        } else this.toast.error(res?.Message);
      },
      (error) => {
        this.toast.error(error, 'Error');
      }
    );
  }

  popUpClose(key: any) {
    switch (key) {
      case 'otp':
        this.PopupPaymentOtp = false;
        break;
      case 'upi':
        this.PopupUpi = false;
        this.upiId = '';
        break;
    }
  }

  onOtpChange(e: any) {
    this.redeemOtp = e;
  }

  showAddAccount() {
    this.schemeList = false;
    this.addAccount = !this.addAccount;
  }

  viewDetail(id: any) {
    this.schemeList = false;
    this.viewDetails = !this.viewDetails;
    this.ApiService.postData(apiEndPoints.my_gold_scheme_detail, {
      scheme_id: id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.schemeDetails = res?.Data;
      }
    });
  }

  upiModal(id: any) {
    this.ID = id;
    this.PopupUpi = !this.PopupUpi;
  }

  back() {
    if (this.viewDetails) this.viewDetails = false;
    else if (this.addAccount) this.addAccount = false;
    this.schemeList = true;
  }

  ngOnDestroy() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }
  }

  private handlePaymentFailure() {
    // Close the Razorpay modal if it's open
    if (this.rzp) {
      this.rzp.close();
      this.isProcessingPayment = false;
      this.spinner.hide();
    }
  }

  private handlePaymentError() {
    this.isProcessingPayment = false;
    this.spinner.hide();
    this.toast.error('Payment initialization failed. Please try again.');
  }
}
