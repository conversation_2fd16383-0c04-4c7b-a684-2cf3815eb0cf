.myAccount {
  padding: 40px 0 100px;
}
.sideMenu {
  width: 270px;
}
.contentSection {
  display: flex;

  .contents {
    width: calc(100% - 270px);
    padding-left: 50px;
  }
}

h1 {
  font-size: 44px;
  margin-bottom: 25px;
}

.head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  span {
    font-weight: 600;
  }
  .filtername {
    width: 200px;
  }
}

.filtername {
  padding: 12px 20px;
  border: 1px solid #d5dbde;
  // margin-right: 15px;
  font-size: 14px;
  cursor: pointer;
  width: 100%;
  height: 49px;
  position: relative;
  margin-bottom: 20px;

  img {
    padding-right: 10px;
  }
}
.selectOption {
  span {
    padding-right: 5px;
  }

  .selectMenu {
    font-size: 14px;
    color: #000;
    position: relative;
    padding-right: 25px;

    &.sort {
      &::before {
        content: "";
        background-image: url(/assets/images/angle-down.svg);
        position: absolute;
        right: 0px;
        top: 3px;
        width: 20px;
        height: 20px;
      }
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    position: absolute;
    opacity: 0;
    box-shadow: 0px 2px 2px #d5dbde;
    background: #fff;
    width: 100%;
    left: 0px;
    top: 49px;
    z-index: -1;

    &.active {
      z-index: 3;
      opacity: 1;
      transition: all linear 0.2s;
      z-index: 1;
    }

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: -12px;
      width: 100%;
      height: 20px;
    }

    li {
      padding: 0px 15px;

      &:hover {
        color: #d2ab66;
      }
    }
  }
}

.orders {
  display: flex;
  align-items: center;
  border: 1px solid #d5dbde;
  padding: 20px;
  margin-bottom: 25px;
  font-size: 14px;
  color: #000;
  .image {
    width: 108px;
    height: 108px;
    display: block;
    min-width: 108px;
  }

  .image img {
    width: 100%;
    height: 100%;
  }

  .orderDetail {
    padding: 0 20px;
    width: 40%;
    .orderId {
      font-size: 16px;
      font-weight: 600;
    }

    .item {
      padding: 5px 0 2px;
    }

    .price {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .quantity {
    padding: 0 40px;
    font-size: 16px;
    font-weight: 600;
    width: 160px;
  }

  .status {
    padding: 0 30px 0px 0;
    width: 40%;
    .statusIn {
      font-size: 16px;
      font-weight: 600;
      padding-bottom: 3px;

      &.red {
        color: #ed5050;
        .color {
          background: #ed5050;
          border: 1px solid #ed5050;
        }
      }
      &.green {
        color: #1ba820;
        .color {
          background: #1ba820;
          border: 1px solid #1ba820;
        }
      }
      &.white {
        color: #000;
        .color {
          background: transparent;
          border: 1px solid #000;
        }
      }
      &.orange {
        color: #ff9b3f;
        .color {
          background: #ff9b3f;
          border: 1px solid #ff9b3f;
        }
      }
      .color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
      }
    }
  }
}

.empty-state {
  text-align: center;
  padding: 48px 24px;
  border-radius: 8px;
  margin: 20px 0;

  .icon {
    width: 48px;
    height: 48px;
    margin: 0 auto;
    color: #9e9e9e;
  }

  h3 {
    margin-top: 16px;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  p {
    margin-top: 8px;
    font-size: 14px;
    color: #666;
  }

  .start-shopping-btn {
    margin-top: 24px;
    padding: 10px 24px;
    background-color: #007bff; // Use your primary color
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: background-color 0.3s;

    &:hover {
      background-color: darken(#007bff, 10%);
    }
  }
}

@media screen and (max-width: 1360px) {
  .orders {
    flex-wrap: wrap;
    position: relative;
    padding-left: 150px;

    .orderDetail {
      width: 100%;
    }

    .image {
      position: absolute;
      left: 20px;
      top: 20px;
    }

    .quantity {
      width: 100%;
      padding: 8px 20px;
    }

    .status {
      width: 100%;
      padding-left: 20px;
    }
  }
}

@media screen and (max-width: 992px) {
  .myAccount {
    padding: 20px 0 50px;
  }

  .sideMenu {
    width: 60px;
  }
  .contentSection .contents {
    width: calc(100% - 60px);
  }

  h1 {
    font-size: 30px;
    margin-bottom: 10px;
  }
}
@media screen and (max-width: 640px) {
  .contentSection {
    flex-direction: column;
    .contents {
      width: 100%;
      padding-left: 0;
      padding-top: 30px;
    }
  }
  .sideMenu {
    width: 100%;
  }

  .orders {
    padding: 15px;

    .image {
      position: unset;
      margin-bottom: 10px;
    }

    .orderDetail {
      padding: 0;
    }

    .quantity {
      padding: 10px 0;
    }

    .status {
      padding: 0;
    }

    .status .statusIn {
      font-size: 14px;
    }

    .orderDetail .orderId {
      font-size: 14px;
    }
  }
}

@media screen and (max-width: 480px) {
  h1 {
    font-size: 25px;
  }
}
