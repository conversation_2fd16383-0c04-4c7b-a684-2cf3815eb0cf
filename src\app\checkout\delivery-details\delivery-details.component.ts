import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/Services/api.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import {
  Component,
  ElementRef,
  NgZone,
  OnInit,
  ViewChild,
} from '@angular/core';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { environment } from 'src/environments/environment.prod';
declare var Razorpay: any;

@Component({
  selector: 'app-delivery-details',
  templateUrl: './delivery-details.component.html',
  styleUrls: ['./delivery-details.component.scss'],
})
export class DeliveryDetailsComponent implements OnInit {
  Popupredeem: boolean = false;
  deliveryShow: any = true;
  detailShow: any = false;
  editShow: any = false;
  billingForm!: FormGroup;
  addressForm!: FormGroup;
  editAddressForm!: FormGroup;
  countries: any;
  states: any;
  addressData: any;
  editAddressData: any;
  selectedAddress: any;
  defaultAddressId: any;
  cartData: any;
  sameAsDelivery: boolean = false;
  isPromoCodeApplied: any = false;
  isSchemeBalanceApplied: any = false;
  isWeightBalanceApplied: any = false;
  isMainWalletApplied: any = false;
  isRewardPointApplied: any = false;
  redeem_points: any;
  selectedScheme: any = '';
  selectedMainWallet: any;
  selectedRewardPoints: any;
  promocode: any;
  instruction: any;
  isLoading: boolean = false;
  accessCode: any;
  ccAvenueUrl: string = environment.ccAvenueProduction;
  ccAvenueBeta: string = environment.ccAvenueStaging;
  checkDomain: boolean = false;
  encRequest: any;
  @ViewChild('form') form!: ElementRef;
  private rzp: any;
  windowRef: any = window;

  constructor(
    private formbuilder: FormBuilder,
    private apiService: ApiService,
    private toast: ToastrService,
    private router: Router,
    private ngZone: NgZone
  ) { }

  ngOnInit(): void {
    this.checkDomain = window.location.hostname.includes('beta');
    this.initBillingForm();
    this.initAddressForm();
    this.initEditAddressForm();
    this.getCountries();
    this.getAddress();
    // this.getCart()
    if (localStorage.getItem('cashScheme')) {
      this.selectedScheme = 'cash';
      this.isSchemeBalanceApplied = true;
    } else if (localStorage.getItem('weigtScheme')) {
      this.selectedScheme = 'weight';
      this.isWeightBalanceApplied = true;
    } else {
      this.isSchemeBalanceApplied = false;
      this.isWeightBalanceApplied = false;
    }
    if (localStorage.getItem('mainWallet')) {
      this.selectedMainWallet = true;
      this.isMainWalletApplied = true;
    } else this.isMainWalletApplied = false;
    if (localStorage.getItem('rewardPoints')) {
      this.selectedRewardPoints = true;
      this.isRewardPointApplied = true;
      this.redeem_points = localStorage.getItem('redeemPoints');
    }
    if (localStorage.getItem('promoCode')) {
      this.isPromoCodeApplied = true;
      this.promocode = localStorage.getItem('promoCode');
    }
    if (localStorage.getItem('instruction')) {
      this.instruction = localStorage.getItem('instruction');
    }
    this.getCartDetails();
  }

  initBillingForm() {
    this.billingForm = this.formbuilder.group({
      name: ['', [Validators.required]],
      country: ['', [Validators.required]],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      state: ['', [Validators.required]],
      city: ['', Validators.required],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      code: ['91'],
      address: ['', [Validators.required]],
      gst: [''],
    });
  }

  get bf() {
    return this.billingForm.controls;
  }

  initAddressForm() {
    this.addressForm = this.formbuilder.group({
      name: ['', [Validators.required]],
      country: ['', [Validators.required]],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      state: [{ value: '', disabled: true }, [Validators.required]],
      city: ['', [Validators.required]],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      code: ['91'],
      address: ['', Validators.required],
    });
  }

  get af() {
    return this.addressForm.controls;
  }

  initEditAddressForm() {
    this.editAddressForm = this.formbuilder.group({
      name: [''],
      country: [''],
      pincode: [''],
      state: [],
      city: [''],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      code: [''],
      address: [''],
    });
  }

  get eaf() {
    return this.editAddressForm.controls;
  }

  getCountries() {
    this.apiService.getData(apiEndPoints.COUNTRY).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.countries = res?.Data;
      }
    });
  }

  selectedCountry(event: any, type: any) {
    const countryId = event.target.value;
    let data = { countryId: countryId };
    this.apiService.postData(apiEndPoints.STATE, data).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.states = res?.Data;
        if (type == 'add') this.addressForm?.get('state')?.enable();
      }
    });
  }

  getAddress() {
    this.apiService.getData(apiEndPoints.GET_ADDRESS).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.addressData = res?.Data;
        this.deliveryShow = this.addressData?.length ? true : false;
        this.detailShow = this.addressData?.length == 0 ? true : false;
        const filteredData = this.addressData.filter(
          (obj: any) => obj.is_default == 1
        );
        this.defaultAddressId = filteredData[0]?.id;
      }
    });
  }

  submitAddress() {
    if (this.addressForm.invalid) {
      this.addressForm.markAllAsTouched();
      return;
    }
    let data = {
      name: this.addressForm.get('name')?.value,
      countrycode: this.addressForm.get('code')?.value,
      mobile: this.addressForm.get('mobile')?.value,
      country_id: this.addressForm.get('country')?.value,
      state_id: this.addressForm.get('state')?.value,
      city: this.addressForm.get('city')?.value,
      pincode: this.addressForm.get('pincode')?.value,
      address: this.addressForm.get('address')?.value,
    };

    this.apiService
      .postData(apiEndPoints.ADD_ADDRESS, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address added');

          //Webengage Start
          let country = this.countries.find(
            (x: any) => x.id == data.country_id
          );
          let state = this.states.find((x: any) => x.id == data.state_id);
          this.windowRef.webengage.track('Delivery Address Added', {
            'Full Name': this.addressForm.get('name')?.value,
            country: country?.name,
            pincode: this.addressForm.get('pincode')?.value,
            state: state?.name,
            city: this.addressForm.get('city')?.value,
            Address: this.addressForm.get('address')?.value,
            'Mobile Number': this.addressForm.get('mobile')?.value,
          });
          //Webengage End

          window.location.reload();
        } else this.toast.error('failed');
      });
  }

  deleteAddres(id: any) {
    let data = { addressId: id };
    this.apiService
      .postData(apiEndPoints.DELETE_ADDRESS, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address Deleted');
          this.getAddress();
        } else this.toast.error('failed');
      });
  }

  setDefaultAddress(value: any) {
    let data = { addressId: value };
    this.apiService
      .postData(apiEndPoints.DEFAULT_ADDRESS, data)
      .subscribe((res: any) => {
        if (res.ErrorCode == 0) {
          this.getAddress();

          //Webengage Start
          let address = this.addressData.find((x: any) => x.id == value);

          this.windowRef.webengage.track('Delivery Address Selected', {
            'Full Name': address?.name,
            country: address?.country,
            pincode: address?.pincode,
            state: address?.state,
            city: address?.city,
            Address: address?.address,
            'Mobile Number': address?.mobile,
          });
          //Webengage End

          setTimeout(() => {
            this.setBillingAddress();
          }, 500);
        }
      });
  }

  setBillingAddress() {
    if (this.sameAsDelivery) {
      let data = { addressId: this.defaultAddressId };
      this.apiService
        .postData(apiEndPoints.GET_SINGLE_ADDRESS, data)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) {
            const setBillingData = res?.Data;
            let countryid = { countryId: setBillingData?.country_id };
            this.apiService
              .postData(apiEndPoints.STATE, countryid)
              .subscribe((res: any) => {
                if (res?.ErrorCode == 0) {
                  this.states = res?.Data;
                }
              });
            this.billingForm.get('name')?.setValue(setBillingData?.name),
              this.billingForm
                .get('code')
                ?.setValue(setBillingData?.countrycode),
              this.billingForm.get('mobile')?.setValue(setBillingData?.mobile),
              this.billingForm
                .get('country')
                ?.setValue(setBillingData?.country_id),
              this.billingForm.get('state')?.setValue(setBillingData?.state_id),
              this.billingForm.get('city')?.setValue(setBillingData?.city),
              this.billingForm
                .get('pincode')
                ?.setValue(setBillingData?.pincode),
              this.billingForm
                .get('address')
                ?.setValue(setBillingData?.address);
          }
        });
    } else {
      this.billingForm.reset();
    }
  }

  addBillingDetails() {
    if (this.billingForm.invalid) {
      this.billingForm.markAllAsTouched();
      return;
    }

    if (this.instruction) {
      localStorage.setItem('instruction', this.instruction);
    } else localStorage.removeItem('instruction');

    let data = {
      name: this.billingForm.get('name')?.value,
      countrycode: this.billingForm.get('code')?.value,
      mobile: this.billingForm.get('mobile')?.value,
      country_id: this.billingForm.get('country')?.value,
      state_id: this.billingForm.get('state')?.value,
      city: this.billingForm.get('city')?.value,
      pincode: this.billingForm.get('pincode')?.value,
      address: this.billingForm.get('address')?.value,
      gst: this.billingForm.get('gst')?.value,
    };
    this.apiService
      .postData(apiEndPoints.ADD_BILLING, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.checkout();
          // this.toast.success('Details Added')
          // setTimeout(() => {
          //   this.router.navigateByUrl('/cart/order-details')
          // }, 500);
        } else this.toast.warning('Fill out the details');
      });
  }

  getCartDetails(): Promise<any> {
    // this.isLoading = true;
    let data = {
      promo_code_applied: this.isPromoCodeApplied,
      schembalance_applied: this.isSchemeBalanceApplied,
      weightbalance_applied: this.isWeightBalanceApplied,
      mainwallet_applied: this.isMainWalletApplied,
      reward_points_applied: this.isRewardPointApplied,
      promo_code: this.promocode,
      reward_points: this.redeem_points,
    };
    return new Promise((resolve, reject) => {
      this.apiService.postData(apiEndPoints.get_cart_details, data).subscribe(
        (res: any) => {
          if (res.ErrorCode == 0) {
            this.cartData = res?.Data;
            resolve(this.cartData);
            reject('API call failed');
            // this.isLoading = false;
          } else {
            this.toast.error(res?.Message);
            // this.isLoading = false
            if (this.selectedRewardPoints) this.selectedRewardPoints = false;
          }
        },
        (err) => {
          reject(err);
        }
      );
    });
  }

  onScheme(event: any) {
    if (!event.target.checked) {
      this.selectedScheme = '';
      this.isSchemeBalanceApplied = false;
      this.isWeightBalanceApplied = false;
      this.getCartDetails();
      localStorage.removeItem('cashScheme');
      localStorage.removeItem('weigtScheme');
    }
  }

  onSchemeChange(event: any) {
    this.selectedScheme = event.target.value;
    if (this.selectedScheme == 'cash') {
      this.isSchemeBalanceApplied = true;
      localStorage.setItem('cashScheme', 'true');
    }
    if (this.selectedScheme == 'weight') {
      this.isWeightBalanceApplied = true;
      localStorage.setItem('weigtScheme', 'weight');
    }
    this.getCartDetails();
  }

  onMainBalanceChange() {
    if (this.selectedMainWallet) {
      this.isMainWalletApplied = true;
      localStorage.setItem('mainWallet', 'true');
    } else {
      this.isMainWalletApplied = false;
      localStorage.removeItem('mainWallet');
    }
    if (localStorage.getItem('redeemPoints'))
      this.redeem_points = localStorage.getItem('redeemPoints');
    this.getCartDetails();
  }

  async redeemRewardPoints() {
    if (this.redeem_points) {
      this.isRewardPointApplied = true;
      await this.getCartDetails();
      if (this.cartData?.possible_rewardpoints) {
        localStorage.setItem('redeemPoints', this.redeem_points);
        this.popUpClose();
        this.selectedRewardPoints = true;
        localStorage.setItem('rewardPoints', 'true');
      } else {
        this.isRewardPointApplied = false;
        this.redeem_points = '';
        this.getCartDetails();
        this.selectedRewardPoints = false;
        localStorage.removeItem('rewardPoints');
        localStorage.removeItem('redeemPoints');
        this.toast.error('Reward points Validation error');
      }
    } else this.isRewardPointApplied = false;
  }

  popUp() {
    if (this.cartData?.possible_rewardpoints && !this.selectedRewardPoints) {
      this.Popupredeem = !this.Popupredeem;
    } else {
      this.selectedRewardPoints = false;
      this.isRewardPointApplied = false;
      this.redeem_points = '';
      this.getCartDetails();
    }
  }

  popUpClose() {
    this.Popupredeem = false;
    this.selectedRewardPoints = false;
    this.redeem_points = '';
  }

  add() {
    this.detailShow = !this.detailShow;
    this.deliveryShow = false;
  }

  editAddress(id: any) {
    this.editShow = !this.editShow;
    this.deliveryShow = false;

    let data = { addressId: id };
    this.apiService
      .postData(apiEndPoints.GET_SINGLE_ADDRESS, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.editAddressData = res?.Data;
          let countryid = { countryId: this.editAddressData?.country_id };
          this.apiService
            .postData(apiEndPoints.STATE, countryid)
            .subscribe((res: any) => {
              if (res?.ErrorCode == 0) {
                this.states = res?.Data;
              }
            });
          this.editAddressForm
            .get('name')
            ?.setValue(this.editAddressData?.name),
            this.editAddressForm
              .get('code')
              ?.setValue(this.editAddressData?.countrycode),
            this.editAddressForm
              .get('mobile')
              ?.setValue(this.editAddressData?.mobile),
            this.editAddressForm
              .get('country')
              ?.setValue(this.editAddressData?.country_id),
            this.editAddressForm
              .get('state')
              ?.setValue(this.editAddressData?.state_id),
            this.editAddressForm
              .get('city')
              ?.setValue(this.editAddressData?.city),
            this.editAddressForm
              .get('pincode')
              ?.setValue(this.editAddressData?.pincode),
            this.editAddressForm
              .get('address')
              ?.setValue(this.editAddressData?.address);
        }
      });
  }

  updateAddress() {
    if (this.editAddressForm.invalid) {
      return;
    }
    let data = {
      addressId: this.editAddressData?.id,
      name: this.editAddressForm.get('name')?.value,
      countrycode: this.editAddressForm.get('code')?.value,
      mobile: this.editAddressForm.get('mobile')?.value,
      country_id: this.editAddressForm.get('country')?.value,
      state_id: this.editAddressForm.get('state')?.value,
      city: this.editAddressForm.get('city')?.value,
      pincode: this.editAddressForm.get('pincode')?.value,
      address: this.editAddressForm.get('address')?.value,
    };
    this.apiService
      .postData(apiEndPoints.EDIT_ADDRESS, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address updated');
          window.location.reload();
        } else this.toast.error('failed');
      });
  }

  checkPincode() {
    let pincode = this.addressForm.get('pincode')?.value;
    this.apiService
      .postData(apiEndPoints.DELIVERY_CHECK, { pincode: pincode })
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          // this.deliveryStatus = res?.Data
        } else this.toast.error();
      });
  }

  async applyPromoCode() {
    if (this.promocode) {
      this.isPromoCodeApplied = true;
      await this.getCartDetails();
      if (this.cartData?.valid_promocode) {
        this.toast.success(this.cartData?.promocode_message);
        localStorage.setItem('promoCode', this.promocode);

        //Webengage Start
        this.windowRef.webengage.track('Coupon Code Applied', {
          'Cart Value Before Discount': '',
          'Cart Value After Discount': Number(
            this.cartData?.grand_total
          ).toFixed(2),
          'Order Coupon Code': this.promocode,
          'Discount Amount': Number(
            this.cartData?.discount_lists.find(
              (x: any) => x.label == 'Promocode'
            )?.amount
          ).toFixed(2),
          Source: 'Website',
          'Page URL': window.location.href,
        });
        //Webengage End
      } else {
        //Webengage Start
        this.windowRef.webengage.track('Coupon Code Failed', {
          'Coupon Code': this.promocode,
          Source: 'Website',
          'Page URL': window.location.href,
        });
        //Webengage End

        this.promocode = '';
        this.toast.error(this.cartData?.promocode_message);
        this.isPromoCodeApplied = false;
      }
    }
  }

  onInstruction() {
    if (this.instruction) this.instruction = '';
  }

  checkout() {
    let data = {
      promo_code_applied: this.isPromoCodeApplied,
      schembalance_applied: this.isSchemeBalanceApplied,
      weightbalance_applied: this.isWeightBalanceApplied,
      mainwallet_applied: this.isMainWalletApplied,
      reward_points_applied: this.isRewardPointApplied,
      promo_code: this.promocode,
      reward_points: this.redeem_points,
      special_instruction: this.instruction,
    };

    this.apiService
      .postData(apiEndPoints.checkout, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          const data = res?.Data.data;

          const options = {
            key: data.api_key,
            order_id: data.razorpay_order_id,
            amount: data.amount, // amount in paise
            currency: 'INR',
            name: 'TT Devassy',
            description: 'Payment for order',
            handler: (response: any) => {
              this.isLoading = true;
              // Handle success
              this.apiService
                .postData('order-payment-success', {
                  order_id: data.order_id,
                  ...response,
                })
                .subscribe((res: any) => {
                  this.isLoading = false;
                  if (res?.ErrorCode == 0) {
                    //Webengage Start
                    this.windowRef.webengage.track('Payment Completed', {
                      'Payment Mode': 'Online',
                      'Transaction ID': response.razorpay_payment_id,
                      'Order ID': data.order_number,
                      'Product ID': this.cartData?.products
                        ?.map((x: any) => x.id)
                        .join(','),
                      'Product Name': this.cartData?.products?.map(
                        (x: any) => x.productName
                      ),
                      'No. Of Products': this.cartData?.products?.length,
                      'Total Amount': Number(this.cartData?.grand_total),
                      Currency: 'INR',
                      Quantity: this.cartData?.products?.length,
                      'Product Details': this.cartData?.products?.map(
                        (x: any) => ({
                          'Product ID': x.slug,
                          'Product Name': x.productName,
                          Availability: x.outOfStock == false ? true : false,
                          isBundle: false,
                          isSample: false,
                          Quantity: 1,
                          MRP: Number(x.price).toFixed(2),
                          'L1 Category ID': x?.maincategory_id,
                          'L1 Category Name': x?.maincategory_name,
                          'L2 Category ID': x?.subcategory_id,
                          'L2 Category Name': x?.subcategory_name,
                        })
                      ),
                      'Discount Amount':
                        this.cartData?.discount_lists?.length > 0 ? parseFloat(this.cartData?.discount_lists[0]?.amount) : 0,
                      'Coupon Code': this.promocode ? this.promocode : '',
                      'Sub Total': Number(this.cartData?.sub_total),
                      'Shipping Charges': Number(
                        this.cartData?.shipping_charges
                      ),
                      Source: 'Website',
                      'Page URL': window.location.href,
                    });

                    this.windowRef.webengage.track('Order Created', {
                      'Transaction ID': response.razorpay_payment_id,
                      'Order ID': data.order_number,
                      Status: '',
                      'Payment Mode': 'Online',
                      'Product ID': this.cartData?.products
                        ?.map((x: any) => x.slug)
                        .join(','),
                      'Product Name': this.cartData?.products
                        ?.map((x: any) => x.productName)
                        .join(','),
                      'No. Of Products': this.cartData?.products?.length,
                      'Total Amount': Number(this.cartData?.grand_total),
                      Currency: 'INR',
                      'Product Details': this.cartData?.products?.map(
                        (x: any) => ({
                          'Product ID': x.slug,
                          'Product Name': x.productName,
                          Availability: x.outOfStock == false ? true : false,
                          isBundle: false,
                          isSample: false,
                          Quantity: 1,
                          MRP: Number(x.price).toFixed(2),
                          'L1 Category ID': x?.maincategory_id,
                          'L1 Category Name': x?.maincategory_name,
                          'L2 Category ID': x?.subcategory_id,
                          'L2 Category Name': x?.subcategory_name,
                        })
                      ),
                      'Total Coupon Discount Amount': '',
                      'Total List Discount Amount': '',
                      'Starting Points Balance': '',
                      'Points Balance Left': '',
                      'Points Earned': '',
                      'Points Spent': '',
                      'Order Coupon Code(s)': this.promocode
                        ? this.promocode
                        : '',
                      'Sub Total': Number(this.cartData?.sub_total),
                      Quantity: this.cartData?.products?.length,
                      Taxes: '',
                      'Shipping Charges': Number(
                        this.cartData?.shipping_charge
                      ),
                      'Logistics Partner ID': '',
                      'Logistics Partner Name': '',
                      'Estimated Delivery Date': '',
                    });
                    //Webengage End

                    this.router.navigate(['/cart/order-placed'], {
                      queryParams: {
                        amount: data.actual_amount,
                        order_id: data.order_number,
                      },
                    });
                  } else {
                    this.handlePaymentFailure(
                      data.order_id,
                      data.order_number,
                      response.razorpay_payment_id
                    );
                    this.router.navigate(['/cart/order-failed']);
                  }
                });
            },
            prefill: {
              name: this.billingForm.get('name')?.value,
              email: null,
              contact: this.billingForm.get('mobile')?.value,
            },
            notes: {
              address: this.billingForm.get('address')?.value,
            },
            theme: {
              color: '#F37254',
            },
            modal: {
              ondismiss: () => {
                this.ngZone.run(() => {
                  this.handlePaymentFailure(
                    data.order_id,
                    data.order_number,
                    '',
                    'payment_cancelled'
                  );
                });
              },
            },
          };

          this.rzp = new Razorpay(options);
          this.rzp.on('payment.failed', (response: any) => {
            this.ngZone.run(() => {
              this.handlePaymentFailure(
                data.order_id,
                data.order_number,
                response.error.metadata.payment_id,
                response.error.description || 'payment_failed'
              );
            });
          });

          //Webengage Start
          this.windowRef.webengage.track('Payment Initiated', {
            'Payment Mode': 'Online',
            'Transaction ID': '',
            'Order ID': data?.order_id,
            'Product Name': this.cartData?.products
              ?.map((x: any) => x.productName)
              .join(','),
            'No. Of Products': String(this.cartData?.products?.length),
            'Total Amount': parseFloat(this.cartData?.grand_total),
            Currency: 'INR',
            Quantity: this.cartData?.products?.length,
            'Product Details': this.cartData.products.map((x: any) => ({
              'Product ID': x.slug,
              'Product Name': x.productName,
              Availability: x.outOfStock == false ? true : false,
              isBundle: false,
              Quantity: 1,
              MRP: Number(x.price).toFixed(2),
              'L1 Category ID': x?.maincategory_id,
              'L1 Category Name': x?.maincategory_name,
              'L2 Category ID': x.subcategory_id,
              'L2 Category Name': x.subcategory_name,
            })),
            'Discount Amount': this.cartData.discount_lists?.length > 0 ? parseFloat(this.cartData?.discount_lists[0]?.amount) : 0,
            'Coupon Code': this.promocode ? this.promocode : '',
            'Sub Total': Number(this.cartData?.sub_total),
            'Shipping Charges': Number(this.cartData?.shipping_charge),
            Source: 'Website',
            'Page URL': window.location.href,
          });
          //Webengage End

          this.rzp.open();
        } else this.toast.error(res?.Message);
      });
  }

  private handlePaymentFailure(
    orderId: string,
    orderNo?: string,
    transactionId?: string,
    reason: string = 'payment_failed'
  ) {
    // Close the Razorpay modal if it's open
    if (this.rzp) {
      this.rzp.close();
    }

    //Webengage Start
    if (orderNo) {
      this.windowRef.webengage.track('Payment Failed', {
        'Order ID': orderId,
        Reason: reason,
        'Payment Mode': 'Online',
        'Transaction ID': transactionId,
        'Total Amount': parseFloat(this.cartData?.grand_total),
        Source: 'Website',
        'Page URL': window.location.href,
      });
    }
    //Webengage End

    this.router.navigateByUrl('/cart/order-failed');
  }
}
