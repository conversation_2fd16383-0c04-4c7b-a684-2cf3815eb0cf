import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './header/header.component';
import { FooterComponent } from './footer/footer.component';
import { SwiperModule } from 'swiper/angular';
import { NgOtpInputModule } from 'ng-otp-input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxImageZoomModule } from 'ngx-image-zoom';
import { SidebarComponent } from './sidebar/sidebar.component';
import { RouterModule } from '@angular/router';
import { TransparentHeaderComponent } from './transparent-header/transparent-header.component';
import { VideoPlayerComponent } from './video-player/video-player.component';
import { NgxSpinnerModule } from 'ngx-spinner';
import { InfiniteScrollModule } from 'ngx-infinite-scroll';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { NgChartsModule } from 'ng2-charts';
import { QRCodeModule } from 'angularx-qrcode';

@NgModule({
  declarations: [
    HeaderComponent,
    FooterComponent,
    SidebarComponent,
    TransparentHeaderComponent,
    VideoPlayerComponent,
  ],
  imports: [
    CommonModule,
    SwiperModule,
    FormsModule,
    NgOtpInputModule,
    NgxImageZoomModule,
    ReactiveFormsModule,
    RouterModule,
    NgxSpinnerModule,
    InfiniteScrollModule,
    NgxPaginationModule,
    NgSelectModule,
    NgxSliderModule,
    NgChartsModule,
    QRCodeModule,
  ],
  exports: [
    HeaderComponent,
    FooterComponent,
    SidebarComponent,
    TransparentHeaderComponent,
    VideoPlayerComponent,
  ],
})
export class SharedModule {}
