<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif">
</div>

<div class="cartPage">
  <div class="container">
    <div class="topPagination">
      <h1>Delivery Details</h1>
      <div class="paging">
        <div class="numbering">
          <span class="number active">1</span>Login
        </div>
        <div class="numbering">
          <span class="number active">2</span>Delivery Details
        </div>
        <!-- <div class="numbering">
          <span class="number">3</span>Order Details
        </div> -->
      </div>
    </div>
    <div class="contentSection">
      <div class="leftSide">
        <div class="deliveryDetail" [ngClass]="deliveryShow?'show':'hide'">
          <div class="head">Delivery Address <div class="addNew" (click)="add()"> <img
                src="\assets\images\my-account\add.svg" alt="" style="padding-right: 4px;"> Add New</div>
          </div>
          <ng-container *ngFor="let address of addressData">
            <div class="box">
              <div class="address">
                <input type="radio" id="{{address.id}}" name="radio-group" [checked]="address?.is_default == 1"
                  (change)="setDefaultAddress(address.id)">
                <label for="{{address.id}}" class="name">{{address?.name}}</label>
                <div>{{address?.address}}</div>
                <!-- <div>Vengoor</div> -->
                <div>{{address?.city}}-{{address?.pincode}}</div>
                <div>{{address?.state}}, {{address?.country}} </div>
                <div>Mobile Number : {{address?.mobile}}</div>
              </div>
              <div class="edit" (click)="editAddress(address?.id)"> <img src="\assets\images\my-account\edit.svg" alt=""
                  style="padding-right: 4px;">Edit Address </div>
              <div class="delete" (click)="deleteAddres(address?.id)"> <img src="\assets\images\my-account\delete.svg"
                  alt=""></div>
            </div>
          </ng-container>
          <div class="head">Billing Address
            <div class="sameAs">
              <input type="checkbox" id="instruction" [(ngModel)]="sameAsDelivery"
                (ngModelChange)="setBillingAddress()">
              <label for="instruction">
                <div class="add">Same As Delivery Address </div>
              </label>
            </div>
          </div>
          <div class="box">
            <div class="form">
              <form [formGroup]="billingForm">
                <div class="twoSection">
                  <div class="field">
                    <span>Full Name</span>
                    <input type="text" formControlName="name" />
                    <span *ngIf="bf.name.invalid && bf.name.touched">
                      <span class="text-danger" *ngIf="bf.name.errors?.required"> Name is required </span>
                    </span>
                  </div>
                  <div class="field">
                    <span>country</span>
                    <select name="" id="" formControlName="country" (change)="selectedCountry($event,'add')">
                      <option [value]="country.id" *ngFor="let country of countries">
                        {{country.name}}
                      </option>
                    </select>
                    <span *ngIf="bf.country.invalid && bf.country.touched">
                      <span class="text-danger" *ngIf="bf.country.errors?.required"> Country is required </span>
                    </span>
                  </div>
                </div>
                <div class="twoSection">
                  <div class="field">
                    <span>pincode</span>
                    <input type="text" formControlName="pincode" />
                    <span *ngIf="bf.pincode.invalid && bf.pincode.touched">
                      <span class="text-danger" *ngIf="bf.pincode.errors?.required"> Pincode is required </span>
                    </span>
                    <span class="text-danger" *ngIf="bf.pincode.errors?.pattern">Please Enter Valid Pincode </span>
                  </div>
                  <div class="field">
                    <span>state</span>
                    <select name="" id="" formControlName="state">
                      <option [value]="state.id" *ngFor="let state of states">
                        {{state.name}}
                      </option>
                    </select>
                    <span *ngIf="bf.state.invalid && bf.state.touched">
                      <span class="text-danger" *ngIf="bf.state.errors?.required"> State is required </span>
                    </span>
                  </div>
                </div>

                <div class="twoSection">
                  <div class="field">
                    <div style="position: relative;">
                      <span>city</span>
                      <input type="text" formControlName="city" />
                      <span *ngIf="bf.city.invalid && bf.city.touched">
                        <span class="text-danger" *ngIf="bf.city.errors?.required"> City is required </span>
                      </span>
                    </div>
                    <div style="position: relative;height: 99px;">
                      <span>Mobile Number</span>
                      <div class="country">
                        <select name="" id="" formControlName="code">
                          <option [value]="country.phonecode" *ngFor="let country of countries">
                            +{{country.phonecode}}
                          </option>
                        </select>
                        <input type="tel" formControlName="mobile" />
                      </div>
                      <span *ngIf="bf.mobile.invalid && bf.mobile.touched">
                        <span class="text-danger" *ngIf="bf.mobile.errors?.required"> Mobile Number is required </span>
                      </span>
                      <span class="text-danger" *ngIf="bf.mobile.errors?.pattern">Please Enter Valid Mobile Number
                      </span>
                    </div>
                  </div>
                  <div class="field">
                    <span>Address</span>
                    <textarea name="" id="" cols="30" rows="6" formControlName="address"
                      style="height: 146px;"></textarea>
                    <span *ngIf="bf.address.invalid && bf.address.touched">
                      <span class="text-danger" *ngIf="bf.address.errors?.required"> Address is required </span>
                    </span>
                  </div>
                </div>
                <div class="field">
                  <span>GST Number</span>
                  <input type="text" formControlName="gst" />
                </div>
              </form>
            </div>
          </div>

        </div>
        <div class="addDetail hide" [ngClass]="detailShow?'show':'hide'">
          <div class="head">Delivery Address </div>
          <div class="box">
            <div class="form">
              <form [formGroup]="addressForm">
                <div class="twoSection">
                  <div class="field">
                    <span>Full Name</span>
                    <input type="text" formControlName="name" />
                    <span *ngIf="af.name.invalid && af.name.touched">
                      <span class="text-danger" *ngIf="af.name.errors?.required"> Name is required </span>
                    </span>
                  </div>
                  <div class="field">
                    <span>country</span>
                    <select name="" id="" formControlName="country" (change)="selectedCountry($event,'add')">
                      <option [value]="country.id" *ngFor="let country of countries">
                        {{country.name}}
                      </option>
                    </select>
                    <span *ngIf="af.country.invalid && af.country.touched">
                      <span class="text-danger" *ngIf="af.country.errors?.required"> Country is required </span>
                    </span>
                  </div>
                </div>
                <div class="twoSection">
                  <div class="field">
                    <span>pincode</span>
                    <input type="text" formControlName="pincode" (change)="checkPincode()" />
                    <span *ngIf="af.pincode.invalid && af.pincode.touched">
                      <span class="text-danger" *ngIf="af.pincode.errors?.required"> Pincode is required </span>
                    </span>
                    <span class="text-danger" *ngIf="af.pincode.errors?.pattern">Please Enter Valid Pincode </span>
                  </div>
                  <div class="field">
                    <span>state</span>
                    <select name="" id="" formControlName="state">
                      <option [value]="state.id" *ngFor="let state of states">
                        {{state.name}}
                      </option>
                    </select>
                    <span *ngIf="af.state.invalid && af.state.touched">
                      <span class="text-danger" *ngIf="af.state.errors?.required"> State is required </span>
                    </span>
                  </div>
                </div>
                <div class="twoSection">
                  <div class="field">
                    <div style="position: relative;">
                      <span>city</span>
                      <input type="text" formControlName="city" />
                      <span *ngIf="af.city.invalid && af.city.touched">
                        <span class="text-danger" *ngIf="af.city.errors?.required"> City is required </span>
                      </span>
                    </div>
                    <div style="position: relative; height: 99px;">
                      <span>Mobile Number</span>
                      <div class="country">
                        <select name="" id="" formControlName="code">
                          <option [value]="country.phonecode" *ngFor="let country of countries">
                            +{{country.phonecode}}
                          </option>
                        </select>

                        <input type="tel" formControlName="mobile" />
                      </div>
                      <span *ngIf="af.mobile.invalid && af.mobile.touched">
                        <span class="text-danger" *ngIf="af.mobile.errors?.required"> Mobile Number is required </span>
                      </span>
                      <span class="text-danger" *ngIf="af.mobile.errors?.pattern">Please Enter Valid Mobile Number
                      </span>
                    </div>
                  </div>
                  <div class="field">
                    <span>Address</span>
                    <textarea name="" id="" cols="30" rows="6" formControlName="address"
                      style="height: 146px;"></textarea>
                    <span *ngIf="af.address.invalid && af.address.touched">
                      <span class="text-danger" *ngIf="af.address.errors?.required"> Address is required </span>
                    </span>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <div class="buttons">
            <div class="primary-button golden" (click)="submitAddress()">Save</div>
          </div>
        </div>
        <div class="addDetail hide" [ngClass]="editShow?'show':'hide'">
          <div class="head">Edit Address
          </div>
          <div class="box">
            <div class="form">
              <form [formGroup]="editAddressForm">
                <div class="twoSection">
                  <div class="field">
                    <span>Full Name</span>
                    <input type="text" formControlName="name" />
                  </div>
                  <div class="field">
                    <span>country</span>
                    <select name="" id="" formControlName="country" (change)="selectedCountry($event,'add')">
                      <option [value]="country.id" *ngFor="let country of countries">
                        {{country.name}}
                      </option>
                    </select>
                  </div>
                </div>
                <div class="twoSection">
                  <div class="field">
                    <span>pincode</span>
                    <input type="text" formControlName="pincode" />
                    <span *ngIf="af.pincode.invalid && eaf.pincode.touched">
                      <span class="text-danger" *ngIf="eaf.pincode.errors?.required"> Pincode is required </span>
                    </span>
                    <span class="text-danger" *ngIf="eaf.pincode.errors?.pattern">Please Enter Valid Pincode </span>
                  </div>
                  <div class="field">
                    <span>state</span>
                    <select name="" id="" formControlName="state">
                      <option [value]="state.id" *ngFor="let state of states">
                        {{state.name}}
                      </option>
                    </select>
                  </div>
                </div>
                <div class="twoSection">
                  <div class="field">
                    <span>city</span>
                    <input type="text" formControlName="city" />
                    <span>Mobile Number</span>
                    <div class="country">
                      <select name="" id="" formControlName="code">
                        <option [value]="country.phonecode" *ngFor="let country of countries">
                          +{{country.phonecode}}
                        </option>
                      </select>
                      <input type="tel" formControlName="mobile" />
                    </div>
                    <span *ngIf="eaf.mobile.invalid && eaf.mobile.touched">
                      <span class="text-danger" *ngIf="eaf.mobile.errors?.required"> Mobile Number is required </span>
                    </span>
                    <span class="text-danger" *ngIf="eaf.mobile.errors?.pattern">Please Enter Valid Mobile Number
                    </span>
                  </div>
                  <div class="field">
                    <span>Address</span>
                    <textarea name="" id="" cols="30" rows="6" formControlName="address"></textarea>
                  </div>
                </div>
              </form>
            </div>
          </div>
          <div class="buttons">
            <div class="primary-button golden" (click)="updateAddress()">Save</div>
          </div>
        </div>
      </div>
      <div class="rightSide">
        <div class="box">
          <div class="promo">
            <div class="head">Promo Code</div>
            <div class="enter">
              <span class="code"><input type="text" placeholder="Enter Promo Code" [(ngModel)]="promocode"></span>
              <span class="primary-button" (click)="applyPromoCode()">Apply</span>
            </div>
          </div>
          <div class="addSpecial">
            <div class="instruction">
              <div class="form-group">
                <input type="checkbox" id="special" [checked]="instruction" (change)="onInstruction()">
                <label for="special">
                  <div class="add">Add Special Instruction </div>
                  <div class="type"><textarea name="instruction" id="special" cols="30" rows="4" placeholder="Type Here"
                      [(ngModel)]="instruction"></textarea></div>
                </label>
              </div>

            </div>
          </div>
          <div class="amounts">
            <div class="name">Subtotal</div>
            <div class="rate">₹{{cartData?.sub_total}}</div>
          </div>
          <div class="amounts">
            <div class="name">Shipping Charge</div>
            <div class="rate">₹{{cartData?.shipping_charge}}</div>
          </div>
          <div class="amounts discount" *ngFor="let item of cartData?.discount_lists">
            <div class="name">{{item?.label}}</div>
            <div class="rate">-₹{{item?.amount}}</div>
          </div>
          <div class="amounts grand">
            <div class="name">Grand Total</div>
            <div class="rate">₹{{cartData?.grand_total}}</div>
          </div>
          <div class="redeem">
            <div class="form-group">
              <input type="checkbox" id="redeem" [checked]="selectedScheme == 'cash' || selectedScheme == 'weight'"
                [disabled]="!cartData?.possible_scheme_balance&&!cartData?.possible_scheme_weight_balance"
                (change)="onScheme($event)">
              <label for="redeem">
                <div class="add">
                  <div class="text">Redeem Using Scheme Balance
                    <div class="info">
                      <img src="\assets\images\wallet\Vector.svg" alt="">
                      <div class="infoTxt">
                        <ul>
                          <li>The scheme balance must not exceed the total amount.</li>
                          <li>To redeem the weight balance, the total purchased weight must be greater than or equal to
                            the weight balance.</li>
                          <li>The scheme balance must be used in its entirety.</li>
                          <li>Other wallets or points cannot be used with this.</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </label>
              <div class="balance">
                <input type="radio" name="scheme" id="1" [disabled]="!cartData?.possible_scheme_balance" value="cash"
                  (change)="onSchemeChange($event)" [checked]="selectedScheme == 'cash'">
                <label for="1">Cash Scheme Balance: ₹{{cartData?.scheme_balance}}</label>
              </div>
              <div class="balance">
                <input type="radio" name="scheme" id="2" [disabled]="!cartData?.possible_scheme_weight_balance"
                  value="weight" (change)="onSchemeChange($event)" [checked]="selectedScheme == 'weight'">
                <label for="2">Weight Scheme Balance : {{cartData?.scheme_weight_balance}} gm</label>
              </div>
            </div>
            <div class="form-group">
              <input type="checkbox" id="main" [disabled]="!cartData?.possible_mainwallet_balance"
                [(ngModel)]="selectedMainWallet" (change)="onMainBalanceChange()">
              <label for="main">
                <div class="add">
                  <div class="text">Main Wallet
                    <div class="info">
                      <img src="\assets\images\wallet\Vector.svg" alt="">
                      <div class="infoTxt">
                        <ul>
                          <li>The main wallet must be used in its entirety.</li>
                          <li>Cannot be used with scheme balance </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="balance">Balance : ₹{{cartData?.wallet_balance}}</div>
                </div>
              </label>
            </div>
            <div class="form-group redeemReward">
              <input type="checkbox" id="reward" [(ngModel)]="selectedRewardPoints" (click)="popUp()"
                [disabled]="!cartData?.possible_rewardpoints">
              <label for="reward">
                <div class="add">
                  <div class="text">Redeem Using Reward Points
                    <div class="info">
                      <img src="\assets\images\wallet\Vector.svg" alt="">
                      <div class="infoTxt">
                        <ul>
                          <li>Minimum 10,000 Points Required for redemption</li>
                          <li>10000 Points = Rs 100/-</li>
                          <li>Cannot be used with scheme balance.</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="balance">Balance : {{cartData?.reward_points_balance}}pts</div>
                </div>
              </label>
            </div>
            <div class="primary-button golden" [ngClass]="detailShow?'hide':''" [ngClass]="editShow?'hide':''"
              (click)="addBillingDetails()">Continue to Payment </div>
          </div>
          <!-- <form *ngIf="!checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
            action="{{ccAvenueUrl}}?command=initiateTransaction&enc_request={{encRequest}}&access_code={{accessCode}}&request_type=XML&response_type=XML&version=1.1">
            <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
            <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
          </form> -->
          <!-- <form *ngIf="checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
          action="{{ccAvenueBeta}}?command=initiateTransaction">
          <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
          <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
        </form> -->
        </div>
      </div>
    </div>
  </div>
</div>




<div class="referPopup" [ngClass]="Popupredeem?'show':'hide'">
  <div class="box">
    <div class="close" (click)="popUpClose()"><img src="\assets\images\close-white.svg" alt=""></div>
    <div><input type="text" placeholder="Enter reward points to redeem" [(ngModel)]="redeem_points"></div>
    <div class="button">
      <div class="primary-button white" (click)="popUpClose()">Cancel</div>
      <div class="primary-button golden" (click)="redeemRewardPoints()">Redeem</div>
    </div>
  </div>
</div>
