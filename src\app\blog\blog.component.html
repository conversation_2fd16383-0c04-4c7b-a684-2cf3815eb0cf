<!-- <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="square-jelly-box" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner>

<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div> -->

<div class="top"><img src="\assets\images\blog\Rectangle 305.svg" alt="top banner"></div>
<div class="bannerSection blogbanner">
  <swiper [config]="bannerMain">
    <ng-template swiperSlide *ngFor="let data of blogData">
      <div class="slideItems">
        <div class="productImage"><img src="{{data?.blog_image}}" alt="banner images" class="img-fluid" /></div>
          <div class="head">
            <span *ngFor="let item of data?.category">{{item}}</span>
          </div>
        <div class="content">
          <h1>{{data?.blog_title}}</h1>
          <a [routerLink]="['/blog-details',data?.slug]" class="primary-button ">Read more</a>
        </div>
      </div>
    </ng-template>
  </swiper>
</div>
<div class="container">
  <div class="blogContent">
    <div class="leftSide">
      <a [routerLink]="['/blog-details',featuredBlog?.slug]" class="topSection">
        <div class="topImage"><img class="blogImage" src="{{featuredBlog?.blog_image}}" alt="blog image">
          <span class="icon" *ngIf="featuredBlog?.is_featured"><img src="\assets\images\blog\Group 21596.svg"
              alt="featured"></span>
        </div>
        <ng-container *ngFor="let category of featuredBlog?.category">
          <div class="subHead">{{category}} </div>
        </ng-container>
        <h6>{{featuredBlog?.blog_title}}</h6>
        <p>{{featuredBlog?.blog_description}}</p>
        <div class="bottom">
          <div class="tt">{{featuredBlog?.author}}</div>
          <div class="date">{{featuredBlog?.date}} </div>
          <div class="comment"><img src="\assets\images\blog\Group 193.svg" alt="comment"> <span>{{featuredBlog?.comments}}
              COMMENT</span></div>
        </div>
      </a>
      <div class="boxSection">
        <ng-container *ngFor="let data of blogData">
          <a [routerLink]="['/blog-details',data?.slug]" class="box">
            <div class="topImage"><img class="blogImage" src="{{data?.blog_image}}" alt="blog image">
              <span class="icon" *ngIf="data?.is_featured"><img src="\assets\images\blog\Group 21596.svg" alt="featured"></span>
            </div>
            <ng-container *ngFor="let category of data?.category">
              <div class="subHead">{{category}}</div>
            </ng-container>
            <h6>{{data?.blog_title}}</h6>
            <p>{{data?.blog_description}}</p>
            <div class="bottom">
              <div class="tt">{{data?.author}}</div>
              <div class="date">{{data?.date}}</div>
              <div class="comment"><img src="\assets\images\blog\Group 193.svg" alt="comment"> <span>{{data?.comments}}
                  COMMENT</span></div>
            </div>
          </a>
        </ng-container>
      </div>
    </div>
    <div class="rightSide">
      <!-- <span class="search"><input type="text" placeholder="Search for your favorite product"><span class="searching"><i
            class="fa fa-search"></i></span></span> -->
      <div class="recent">
        <h6>Recent Posts</h6>
        <ng-container *ngFor="let item of recentBlogs">
          <a [routerLink]="['/blog-details',item?.slug]" class="post">
            <div class="postname">{{item?.title}}</div>
            <div class="date">{{item?.date}}</div>
          </a>
        </ng-container>
      </div>
      <div class="category">
        <h6>Categories</h6>
        <ng-container *ngFor="let item of blogCategories">
          <span class="cate">
            <div class="name">{{item?.title}}</div>
            <div class="count">{{item?.count}}</div>
          </span>
        </ng-container>
      </div>
    </div>
  </div>
</div>
