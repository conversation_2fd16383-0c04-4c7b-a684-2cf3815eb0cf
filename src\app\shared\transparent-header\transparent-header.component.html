<ng-container>
  <div class="header header-home">
    <div class="container">
      <div class="menuSection">
        <div class="leftSection">
          <div class="leftTop">
            <input type="text" placeholder="Search for your favorite product" [(ngModel)]="searchProduct"
              (ngModelChange)="serachProducts()" />
            <ng-container *ngIf="isSearchResults">
              <div class="dropSerach">
                <ng-container *ngFor="let product of searchResults?.items">
                  <div class="searchItem" (click)="searchRedirection(product?.slug)">
                    <span class="image">
                      <img src="{{ imageBase }}/{{ product?.image }}" alt="" class="img-fluid" /></span>
                    <span class="content">
                      <div class="name">{{ product?.name }}</div>
                      <div class="price">₹{{ product?.price }}</div>
                    </span>
                  </div>
                </ng-container>
              </div>
            </ng-container>
            <div class="photo" (click)="imageInput.click()">
              <img src="\assets\images\camera.svg" alt="" />
              <input type="file" #imageInput style="display: none" (change)="searchImage($event)" />
            </div>
            <div class="mike" (click)="startListening()">
              <img src="\assets\images\mike.svg" alt="" />
            </div>
            <div class="search">
              <img src="\assets\images\search.svg" alt="" />
            </div>
          </div>

          <div class="menus">
            <ng-container *ngFor="let category of categories?.slice(0, 3)">
              <a class="menu">{{ category?.name }}

                <div class="subMenus">
                  <div class="container">
                    <div class="menuSub">
                      <div class="leftLinks">
                        <ul>
                          <li *ngFor="let item of category?.items">
                            <a (click)="
                              categoryRedirection(
                                item?.type,
                                item?.slug,
                                item,
                                'L2',
                                category
                              )
                            ">{{ item?.name }}</a>
                          </li>
                          <li>
                            <a class="view" (click)="
                              categoryRedirection(
                                category?.type,
                                category?.slug,
                                category,
                                'L1'
                              )
                            ">View All</a>
                          </li>
                          <!-- <li><a href="#">Ring</a></li>
                      <li><a href="#">Pendants</a></li>
                      <li><a href="#">Necklace</a></li> -->
                        </ul>

                        <!-- <ul>
                      <li><a href="#">Earring</a></li>
                      <li><a href="#">Ring</a></li>
                      <li><a href="#">Pendants</a></li>
                      <li><a href="#">Necklace</a></li>
                    </ul> -->
                        <!-- <ul> -->
                        <!-- <li><a href="#">Earring</a></li> -->
                        <!-- <li><a class="view" href="#">View All</a></li> -->
                        <!-- </ul> -->
                      </div>
                      <!-- <div class="rightImage">
                      <a href="#"><img src="\assets\images\Group 38.png" alt=""></a>
                      <a href="#"><img src="\assets\images\Group 39.png" alt=""></a>
                    </div> -->
                    </div>
                  </div>
                </div>
              </a>
              <!-- <a href="#" class="menu">DIAMOND</a>
          <a href="#" class="menu">PLATINUM</a> -->
              <!-- <a href="#" class="menu">WATCHES
            <div class="subMenus">
              <div class="container">
                <div class="watchesSub">
                  <swiper [config]="menuSlide">
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\rolex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\timex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\g-shock.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\rolex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\timex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\g-shock.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\rolex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\timex.png" alt=""></a></ng-template>
                  </swiper>
                </div>
              </div>
            </div>
          </a> -->
            </ng-container>
            <a (click)="silverRedirection()" class="menu">Silver</a>
          </div>
        </div>
        <a routerLink="" class="logo"><img src="\assets\images\logo-white.svg" alt="logo" /></a>
        <div class="rightSection">
          <div class="rightTop">
            <div class="digitalWear">
              <!-- <img src="\assets\images\digital-wear.svg" alt=""> -->
              <!-- <span>Digital Wear</span> -->
            </div>
            <a class="loginAccount" [ngClass]="{ active: logged }" (click)="popUp()"><img src="\assets\images\login.svg"
                alt="" /><span class="dot"></span></a>
            <a class="wishlist" [ngClass]="{ active: wishlistCount > 0 }" (click)="redirect('wishlist')"><img
                src="\assets\images\wishlist.svg" alt="" /><span class="dot"></span></a>
            <a class="cart" [ngClass]="{ active: cartCount > 0 }" (click)="redirect('cart')"><img
                src="\assets\images\cart.webp" alt="" /><span class="dot"></span></a>
          </div>
          <div class="menus">
            <a class="menu">Watches
              <div class="subMenus">
                <div class="container">
                  <div class="watchesSub">
                    <swiper [config]="menuSlide" showsPagination="{false}">
                      <ng-template swiperSlide *ngFor="let brand of watchBrands">
                        <a (click)="
                          categoryRedirection(brand?.type, brand?.slug, '', '')
                        "><img src="{{ imageBase }}/{{ brand?.image }}" alt="" /></a>
                      </ng-template>
                    </swiper>
                  </div>
                </div>
              </div>
            </a>
            <!-- <a href="#" class="menu">SILVER</a> -->
            <a class="menu">TT Brands
              <div class="subMenus">
                <div class="container">
                  <div class="watchesSub">
                    <swiper [config]="menuSlide" showsPagination="{false}">
                      <ng-template swiperSlide *ngFor="let brand of brands">
                        <a (click)="
                          brandRedirection(
                            brand?.redirection_type,
                            brand?.redirection_id,
                            brand?.title
                          )
                        ">
                          <img src="{{ imageBase }}/{{ brand?.image }}" alt="" /></a>
                      </ng-template>
                      <!-- <ng-template swiperSlide><a href="#"><img src="\assets\images\timex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\g-shock.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\rolex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\timex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\g-shock.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\rolex.png" alt=""></a></ng-template>
                    <ng-template swiperSlide><a href="#"><img src="\assets\images\timex.png" alt=""></a></ng-template> -->
                    </swiper>
                  </div>
                </div>
              </div>
            </a>
            <!-- <a routerLink="/advance-booking" class="menu">Advance Booking</a> -->
            <a routerLink="/gold-schemes" class="menu">Gold Schemes</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</ng-container>


<ng-container>
  <div class="headerMobile headerMobilehome">
    <div class="container">
      <div class="mobileHeader">
        <div class="mobileMenu" (click)="mobileShow()">
          <img src="\assets\images\menu-bar.svg" alt="menu">
        </div>
        <a href="" class="logo"><img src="\assets\images\logo-white.svg" alt="logo" /></a>
        <a href="/cart" class="cart"><img src="\assets\images\cart.webp" alt="icon" /></a>
      </div>
    </div>
    <!-- <div class="price">
    <div class="container">
      <div class="priceToday">
        <img class="flag" src="\assets\images\flag.svg" alt="" />
        <span class="carrot">22KT (916)</span>
        <span class="rate">Rs.4685/g</span>
      </div>
    </div>
  </div> -->
    <!-- <div class="goldSwipe" *ngIf="isMobile">
      <swiper [config]="goldSlide">
        <ng-container *ngFor="let item of rates">
          <ng-template swiperSlide>
            <img class="flag" src="\assets\images\flag.svg" alt="flag" />
            <span class="carrot">{{ item?.name }}</span>
            <span class="rate me-2">{{ item?.rate }}</span>
          </ng-template>
        </ng-container>
      </swiper>
    </div> -->
    <!-- <div class="container">
      <div class="searchSection">
        <input type="text" placeholder="Search for your favorite product" [(ngModel)]="searchProduct"
          (ngModelChange)="serachProducts()" />
        <ng-container *ngIf="isSearchResults">
          <div class="dropSerach">
            <ng-container *ngFor="let product of searchResults?.items">
              <div class="searchItem" (click)="searchRedirection(product?.id)">
                <span class="image">
                  <img src="{{ imageBase }}/{{ product?.image }}" alt="" class="img-fluid" /></span>
                <span class="content">
                  <div class="name">{{ product?.name }}</div>
                  <div class="price">₹{{ product?.price }}</div>
                </span>
              </div>
            </ng-container>
          </div>
        </ng-container>

        <div class="photo" (click)="imageInput.click()">
          <img src="\assets\images\camera.svg" alt="" />
          <input type="file" #imageInput style="display: none" (change)="searchImage($event)" />
        </div>
        <div class="mike" (click)="startListening()">
          <img src="\assets\images\mike.svg" alt="" />
        </div>
        <div class="search"><img src="\assets\images\search.svg" alt="" /></div>
      </div>
    </div> -->
  </div>
</ng-container>

<div class="mobMenu" [ngClass]="mobilemenu ? 'active' : ''">
  <div class="menu">
    <a (click)="popUp()" class="account">
      <div class="image">
        <span>{{ logged ? userData?.name.charAt(0) : "G" }}</span>
        <!-- <img src="\assets\images\mobile\Ellipse 118.svg" alt="" /> -->
      </div>
      <div class="detail">
        <div class="name">{{ logged ? userData?.name : "Guest" }}</div>
        <div class="mobile">+91 1234 456 789</div>
      </div>
    </a>
    <div class="menus">
      <div class="drops" *ngFor="let category of categories; let i = index">
        <button class="faq-accordion" (click)="showAccordion(i)" [ngClass]="{ active: selectedIndex == i }">
          {{ category?.name }}
        </button>
        <div class="course-panel" [ngClass]="{ 'open-accordion': selectedIndex == i }"
          *ngFor="let item of category?.items">
          <a (click)="
              categoryRedirection(item?.type, item?.slug, item, 'L2', '', true)
            ">{{ item?.name }}</a>
        </div>
      </div>
    </div>
    <a href="#" class="link digital">
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="21" viewBox="0 0 19 21" fill="none">
          <path
            d="M6.32217 16.026L6.32218 16.026C6.39923 16.1025 6.55097 16.2193 6.74114 16.2193C6.86288 16.2193 6.97218 16.1642 7.02381 16.1382C7.0276 16.1363 7.03108 16.1345 7.03424 16.133L7.05922 16.1206L7.08086 16.103C7.72508 15.5792 8.14762 14.8833 8.22292 14.1308C8.60808 14.3162 9.01486 14.4103 9.44135 14.4103C9.87331 14.4103 10.2796 14.2965 10.658 14.119C10.7251 14.8689 11.1198 15.5687 11.796 16.0985C11.9269 16.2197 12.0703 16.2194 12.1355 16.2193C12.1376 16.2193 12.1396 16.2193 12.1416 16.2193C12.2881 16.2193 12.4679 16.1679 12.588 15.9936C12.6701 15.8818 12.7046 15.742 12.6985 15.6143C12.6923 15.4855 12.6416 15.3304 12.5055 15.2277C12.01 14.8442 11.7545 14.3402 11.7545 13.8587V13.2677C12.9179 12.0626 13.6962 10.0818 13.6962 8.25057H13.6963L13.6961 8.24464C13.6646 6.91559 13.2543 5.8172 12.5064 5.04966C11.7572 4.28067 10.6945 3.86987 9.41103 3.86987H9.411C8.12652 3.86987 7.06326 4.2889 6.32173 5.06245C5.5814 5.83476 5.18652 6.93452 5.18652 8.25057C5.18652 10.0488 5.93363 12.0603 7.12825 13.2694V13.8587C7.12825 14.3351 6.8776 14.8117 6.37329 15.2307C6.25137 15.3246 6.18672 15.4666 6.1737 15.6024C6.1602 15.7431 6.19978 15.9043 6.32217 16.026ZM9.411 13.2469C8.60299 13.2469 7.82688 12.6254 7.23627 11.6385C6.65216 10.6626 6.29331 9.39528 6.29331 8.25057C6.29331 7.18022 6.59112 6.36709 7.11147 5.82276C7.63037 5.27996 8.40073 4.97289 9.411 4.97289C10.4196 4.97289 11.1903 5.28654 11.71 5.83354C12.2313 6.38227 12.5287 7.19612 12.5287 8.25057C12.5287 9.37984 12.17 10.6474 11.5855 11.6276C10.9942 12.6192 10.218 13.2469 9.411 13.2469Z"
            fill="#000" stroke="#000" stroke-width="0.5" />
          <path
            d="M1.30339 4.50641C1.45076 4.50641 1.59319 4.45653 1.69956 4.35082C1.80606 4.24497 1.85676 4.10266 1.85676 3.95493V1.88324H3.94285C4.09022 1.88324 4.23264 1.83336 4.33901 1.72765C4.44552 1.6218 4.49622 1.47949 4.49622 1.33176C4.49622 1.18402 4.44551 1.04171 4.33901 0.935864C4.23264 0.830156 4.09021 0.780273 3.94285 0.780273H1.30337C1.156 0.780273 1.01358 0.830154 0.907205 0.935866C0.800698 1.04171 0.75 1.18403 0.75 1.33176V3.95493C0.75 4.10266 0.800702 4.24498 0.907212 4.35082C1.01358 4.45653 1.15601 4.50641 1.30337 4.50641H1.30339Z"
            fill="#000" stroke="#000" stroke-width="0.5" />
          <path
            d="M17.1434 16.6615V18.7331H15.0573C14.9099 18.7331 14.7675 18.783 14.6611 18.8887C14.5546 18.9945 14.5039 19.1369 14.5039 19.2846C14.5039 19.4323 14.5546 19.5746 14.6611 19.6805C14.7675 19.7862 14.9099 19.8361 15.0573 19.8361H17.6968C17.8442 19.8361 17.9866 19.7862 18.093 19.6805C18.1995 19.5746 18.2502 19.4323 18.2502 19.2846V16.6614V16.6613C18.2501 16.5136 18.1994 16.3714 18.0929 16.2656C17.9865 16.1599 17.8441 16.11 17.6967 16.11C17.5494 16.11 17.4069 16.1599 17.3006 16.2656C17.1941 16.3715 17.1434 16.5138 17.1434 16.6615Z"
            fill="#000" stroke="#000" stroke-width="0.5" />
          <path
            d="M3.94293 18.703H1.85674V16.6314C1.85674 16.4837 1.80603 16.3414 1.69952 16.2355C1.59315 16.1298 1.45073 16.08 1.30337 16.08C1.156 16.08 1.01358 16.1298 0.90721 16.2355C0.800703 16.3414 0.75 16.4837 0.75 16.6314V19.2546C0.75 19.4023 0.800702 19.5447 0.907212 19.6505C1.01358 19.7562 1.15601 19.8061 1.30337 19.8061H3.94293H3.9431C4.09965 19.806 4.23565 19.7306 4.32773 19.6391C4.4197 19.5476 4.4963 19.4117 4.4963 19.2545C4.4963 19.1068 4.4456 18.9644 4.33909 18.8586C4.23271 18.7529 4.09029 18.703 3.94293 18.703Z"
            fill="#000" stroke="#000" stroke-width="0.5" />
          <path
            d="M17.4309 0.75L17.4309 0.750027H15.0573C14.9099 0.750027 14.7675 0.799908 14.6611 0.905619C14.5546 1.01147 14.5039 1.15378 14.5039 1.30151C14.5039 1.44925 14.5546 1.59156 14.6611 1.6974C14.7675 1.80311 14.9099 1.85299 15.0573 1.85299H17.1434V3.92457C17.1434 4.07231 17.1941 4.21462 17.3006 4.32047C17.4069 4.42618 17.5494 4.47605 17.6967 4.47605C17.8441 4.47605 17.9865 4.42617 18.0929 4.32046C18.1994 4.21462 18.2501 4.07231 18.2501 3.92458C18.2501 3.92458 18.2501 3.92457 18.2501 3.92457L18.2502 1.30149V1.30148C18.2502 1.15375 18.1995 1.01143 18.093 0.905586C17.9866 0.799877 17.8442 0.75 17.6968 0.75H17.4309Z"
            fill="#000" stroke="#000" stroke-width="0.5" />
        </svg>
      </span>
      <span class="menuLink">Digital Wear</span>
    </a>
    <a routerLink="/gift-cards" class="link" (click)="closeMenu()">
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path d="M16.6668 10V18.3333H3.3335V10" stroke="#1D1D1D" stroke-width="1.25" stroke-linecap="round"
            stroke-linejoin="round" />
          <path d="M18.3332 5.83331H1.6665V9.99998H18.3332V5.83331Z" stroke="#1D1D1D" stroke-width="1.25"
            stroke-linecap="round" stroke-linejoin="round" />
          <path d="M10 18.3333V5.83331" stroke="#1D1D1D" stroke-width="1.25" stroke-linecap="round"
            stroke-linejoin="round" />
          <path
            d="M10 5.83335H13.75C14.3025 5.83335 14.8324 5.61386 15.2231 5.22316C15.6138 4.83246 15.8333 4.30255 15.8333 3.75002C15.8333 3.19749 15.6138 2.66758 15.2231 2.27688C14.8324 1.88618 14.3025 1.66669 13.75 1.66669C10.8333 1.66669 10 5.83335 10 5.83335Z"
            stroke="#1D1D1D" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
          <path
            d="M9.99984 5.83335H6.24984C5.6973 5.83335 5.1674 5.61386 4.7767 5.22316C4.386 4.83246 4.1665 4.30255 4.1665 3.75002C4.1665 3.19749 4.386 2.66758 4.7767 2.27688C5.1674 1.88618 5.6973 1.66669 6.24984 1.66669C9.1665 1.66669 9.99984 5.83335 9.99984 5.83335Z"
            stroke="#1D1D1D" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </span>
      <span class="menuLink">Gift Cards</span>
    </a>
    <!-- <a routerLink="/advance-booking" class="link" (click)="closeMenu()">
      <span>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M11.6663 1.66669H4.99967C4.55765 1.66669 4.13372 1.84228 3.82116 2.15484C3.5086 2.4674 3.33301 2.89133 3.33301 3.33335V16.6667C3.33301 17.1087 3.5086 17.5326 3.82116 17.8452C4.13372 18.1578 4.55765 18.3334 4.99967 18.3334H14.9997C15.4417 18.3334 15.8656 18.1578 16.1782 17.8452C16.4907 17.5326 16.6663 17.1087 16.6663 16.6667V6.66669L11.6663 1.66669Z"
            stroke="#1D1D1D"
            stroke-width="1.25"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M13.3337 14.1667H6.66699"
            stroke="#1D1D1D"
            stroke-width="1.25"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M13.3337 10.8333H6.66699"
            stroke="#1D1D1D"
            stroke-width="1.25"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M8.33366 7.5H7.50033H6.66699"
            stroke="#1D1D1D"
            stroke-width="1.25"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          <path
            d="M11.667 1.66669V6.66669H16.667"
            stroke="#1D1D1D"
            stroke-width="1.25"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </span>
      <span class="menuLink">Advance Booking</span>
    </a> -->
    <a routerLink="/gold-schemes" class="link" (click)="closeMenu()">
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <g clip-path="url(#clip0_1014_1965)">
            <path d="M10 15V18.3333" stroke="#1D1D1D" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M13.5332 13.5333L15.8915 15.8917" stroke="#1D1D1D" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M4.1084 15.8917L6.46673 13.5333" stroke="#1D1D1D" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M15 10H18.3333" stroke="#1D1D1D" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M1.66699 10H5.00033" stroke="#1D1D1D" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M13.5332 6.46667L15.8915 4.10834" stroke="#1D1D1D" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M4.1084 4.10834L6.46673 6.46667" stroke="#1D1D1D" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
            <path d="M10 1.66669V5.00002" stroke="#1D1D1D" stroke-width="1.5" stroke-linecap="round"
              stroke-linejoin="round" />
          </g>
          <defs>
            <clipPath id="clip0_1014_1965">
              <rect width="20" height="20" fill="white" />
            </clipPath>
          </defs>
        </svg>
      </span>
      <span class="menuLink">Gold Scheme</span>
    </a>
    <a routerLink="/design-your-jewellery" class="link design" (click)="closeMenu()">
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <g clip-path="url(#clip0_1256_2244)">
            <path
              d="M0.597047 0H2.37836C2.70825 0 2.97559 0.267334 2.97559 0.597229C2.97559 0.927124 2.70825 1.19446 2.37836 1.19446H1.19428V2.37854C1.19428 2.70843 0.926789 2.97577 0.597047 2.97577C0.267152 2.97577 -0.000182152 2.70843 -0.000182152 2.37854V0.597229C-0.000182152 0.267334 0.267152 0 0.597047 0V0Z"
              fill="#1D1D1D" />
            <path
              d="M0.597047 10.7672C0.926789 10.7672 1.19428 11.0347 1.19428 11.3644V12.5485H2.37836C2.70825 12.5485 2.97559 12.816 2.97559 13.1458C2.97559 13.4756 2.70825 13.743 2.37836 13.743H0.597047C0.267152 13.743 -0.000182152 13.4756 -0.000182152 13.1458V11.3644C-0.000182152 11.0347 0.267152 10.7672 0.597047 10.7672Z"
              fill="#1D1D1D" />
            <path
              d="M13.1459 2.97577C12.8162 2.97577 12.5487 2.70843 12.5487 2.37854V1.19446H11.3646C11.0349 1.19446 10.7674 0.927124 10.7674 0.597229C10.7674 0.267334 11.0349 0 11.3646 0H13.1459C13.4758 0 13.7432 0.267334 13.7432 0.597229V2.37854C13.7432 2.70843 13.4758 2.97577 13.1459 2.97577Z"
              fill="#1D1D1D" />
            <path
              d="M2.05413 10.0004L3.93263 7.09075L3.04152 3.7709C2.92204 3.3258 3.33083 2.92037 3.77333 3.03908L7.09303 3.9302L10.0032 2.05169C10.3902 1.8019 10.9032 2.0674 10.9238 2.527L11.0754 5.95916L13.7848 8.13979C14.1453 8.43077 14.052 9.00313 13.6192 9.16503L12.0408 9.7531L19.5334 17.2455C20.1544 17.8665 20.1567 18.8737 19.5334 19.4971L19.5 19.5305C19.4995 19.5305 19.4995 19.5305 19.4995 19.5309C18.8789 20.1515 17.8694 20.1515 17.2479 19.5309L9.75554 12.0386L9.16747 13.6163C9.00511 14.0507 8.4323 14.1427 8.14269 13.7825L5.9616 11.0725L2.52944 10.9209C2.07 10.9012 1.80449 10.388 2.05413 10.0004ZM18.0928 18.686C18.2477 18.8412 18.4997 18.8412 18.6551 18.686L18.6885 18.6526C18.8444 18.4965 18.8438 18.2454 18.6885 18.09L10.8102 10.2118L10.3763 10.3734L10.2142 10.8078L18.0928 18.686ZM6.28401 9.89134C6.45567 9.89882 6.61528 9.98 6.72316 10.1135L8.4146 12.2157C8.8101 11.1542 9.35225 9.70061 9.35225 9.70061C9.41267 9.5381 9.541 9.41023 9.70305 9.34981C9.70305 9.34981 11.086 8.83391 12.2182 8.41216L10.1158 6.72042C9.98244 6.61284 9.90126 6.45324 9.89363 6.28157L9.77629 3.61999L7.51799 5.07766C7.37624 5.16921 7.20213 5.19622 7.03932 5.15242L4.46364 4.46135L5.15486 7.03688C5.19866 7.19985 5.17165 7.3738 5.0801 7.51555L3.62242 9.77385L6.28401 9.89134Z"
              fill="#1D1D1D" />
          </g>
          <defs>
            <clipPath id="clip0_1256_2244">
              <rect width="20" height="20" fill="white" transform="matrix(-1 0 0 1 20 0)" />
            </clipPath>
          </defs>
        </svg>
      </span>
      <span class="menuLink">Design Your Jewellery</span>
    </a>
    <a routerLink="/contact" class="link" (click)="closeMenu()">
      <span>
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
          <path
            d="M18.3332 14.1V16.6C18.3341 16.8321 18.2866 17.0618 18.1936 17.2744C18.1006 17.4871 17.9643 17.678 17.7933 17.8349C17.6222 17.9918 17.4203 18.1112 17.2005 18.1856C16.9806 18.2599 16.7477 18.2875 16.5165 18.2666C13.9522 17.988 11.489 17.1118 9.32486 15.7083C7.31139 14.4289 5.60431 12.7218 4.32486 10.7083C2.91651 8.53432 2.04007 6.05914 1.76653 3.48331C1.7457 3.25287 1.77309 3.02061 1.84695 2.80133C1.9208 2.58205 2.03951 2.38055 2.1955 2.20966C2.3515 2.03877 2.54137 1.90224 2.75302 1.80875C2.96468 1.71526 3.19348 1.66686 3.42486 1.66665H5.92486C6.32928 1.66267 6.72136 1.80588 7.028 2.06959C7.33464 2.3333 7.53493 2.69952 7.59153 3.09998C7.69705 3.90003 7.89274 4.68558 8.17486 5.44165C8.28698 5.73992 8.31125 6.06407 8.24478 6.37571C8.17832 6.68735 8.02392 6.9734 7.79986 7.19998L6.74153 8.25831C7.92783 10.3446 9.65524 12.072 11.7415 13.2583L12.7999 12.2C13.0264 11.9759 13.3125 11.8215 13.6241 11.7551C13.9358 11.6886 14.2599 11.7129 14.5582 11.825C15.3143 12.1071 16.0998 12.3028 16.8999 12.4083C17.3047 12.4654 17.6744 12.6693 17.9386 12.9812C18.2029 13.2931 18.3433 13.6913 18.3332 14.1Z"
            stroke="#1D1D1D" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
      </span>
      <span class="menuLink">Contact Us</span>
    </a>
    <div class="bottom">
      <a href=""><span (click)="openWhatsapp()"><img src="\assets\images\mobile\wtsp.svg" alt="" /></span></a>
      <a routerLink="/virtual-shopping"><img src="\assets\images\mobile\video.svg" alt="" /></a>
      <a routerLink="/booking"><img src="\assets\images\mobile\book.svg" /></a>
    </div>
  </div>
  <div class="shade" (click)="closeMenu()"></div>
</div>

<!-- Modal -->

<div class="registerPopup login" [ngClass]="loginModal ? 'show' : 'hide'">
  <div class="box">
    <div class="close" (click)="popUpClose('login')">
      <img src="\assets\images\close-white.svg" alt="" />
    </div>
    <div class="leftSide">
      <img src="\assets\images\register-image.png" alt="" />
    </div>
    <div class="rightSide">
      <form [formGroup]="loginForm">
        <div class="head">Login</div>
        <p>Sign in to explore the exquisite world of Jewellery</p>
        <!-- <div class="field"><input type="email" placeholder="Email Id"></div> -->
        <div class="field country">
          <select name="" id="" formControlName="code">
            <option [value]="country.phonecode" *ngFor="let country of countries">
              +{{ country.phonecode }}
            </option>
          </select>
          <input type="tel" placeholder="Mobile Number" formControlName="mobile" />
        </div>
        <div class="field">
          <input type="submit" [attr.disabled]="loading ? true : null" [value]="loading ? '' : 'Get OTP'"
            (click)="login()" />
          <div class="jumping-dots-loader" *ngIf="loading">
            <span></span> <span></span> <span></span>
          </div>
          <div class="moving-gradient" *ngIf="loading"></div>
        </div>
        <div class="check">
          <span>By continuing you agree to our
            <a (click)="redirection('tnc')">Terms & Conditions</a> and
            <a (click)="redirection('privacy')">Privacy Policy</a>
          </span>
        </div>
      </form>
      <div #recaptchaWrapperRef>
        <div id="recaptcha-container"></div>
      </div>
    </div>
  </div>
</div>

<div class="registerPopup otp" [ngClass]="otpModal ? 'show' : 'hide'">
  <div class="box">
    <div class="close" (click)="popUpClose('otp')">
      <img src="\assets\images\close-white.svg" alt="" />
    </div>
    <div class="leftSide">
      <img src="\assets\images\register-image.png" alt="" />
    </div>
    <div class="rightSide">
      <div class="head">OTP</div>
      <p>
        We have send an OTP to verify your Mobile Number <br />
        {{ countryCode }} {{ mobile }}
      </p>
      <ng-otp-input (onInputChange)="onOtpChange($event)" [config]="otpInputConfig"></ng-otp-input>
      <div class="field">
        <input type="submit" [attr.disabled]="loading ? true : null" [value]="loading ? '' : 'Verify OTP'"
          (click)="verifyOtp()" />
        <div class="jumping-dots-loader" *ngIf="loading">
          <span></span> <span></span> <span></span>
        </div>
        <div class="moving-gradient" *ngIf="loading"></div>
      </div>
      <div class="bottom">
        <div *ngIf="timeLeft > 0" class="timer">{{ transform(timeLeft) }}</div>
        <div *ngIf="timeLeft == 0" class="resend" (click)="resendOtp()">
          Resend OTP
        </div>
      </div>
    </div>
    <div id="recaptcha-container"></div>
  </div>
</div>

<div class="registerPopup" [ngClass]="registerModal ? 'show' : 'hide'">
  <div class="box">
    <div class="close" (click)="popUpClose('register')">
      <img src="\assets\images\close-white.svg" alt="" />
    </div>
    <div class="leftSide">
      <img src="\assets\images\register-image.png" alt="" />
    </div>
    <div class="rightSide">
      <form [formGroup]="registerForm">
        <div class="head">Register</div>
        <p>Sign up to explore the exquisite world of Jewellery</p>
        <div class="field">
          <input type="text" placeholder="Enter Full Name" formControlName="name" />
        </div>
        <div class="field">
          <input type="email" placeholder="Email Id" formControlName="email" />
        </div>
        <div class="field">
          <input type="submit" value="Register" (click)="register()" />
        </div>
        <div class="check">
          <span>By continuing you agree to our
            <a href="#">Terms & Conditions</a> and
            <a href="#">Privacy Policy</a>
          </span>
        </div>
      </form>
    </div>
  </div>
</div>
<!--Auth Modal-->
<!-- <div class="registerPopup login" [ngClass]="authorizationModal ? 'show' : 'hide'">
  <div class="box">
    <div class="leftSide">
      <img src="\assets\images\register-image.png" alt="" />
    </div>
    <div class="rightSide">
      <div class="head">Authorize</div>

      <div class="field country">
        <input type="text" placeholder="Password" [(ngModel)]="auth" />
      </div>
      <div class="field">
        <input type="button" value="Authenticate" (click)="authenticate()" />
      </div>
    </div>
  </div>
</div> -->