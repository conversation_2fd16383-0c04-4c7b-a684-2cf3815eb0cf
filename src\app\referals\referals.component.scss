.myAccount {
    padding: 40px 0 100px;
}
.sideMenu {
    width: 270px;
}
.contentSection {
    display: flex;

    .contents {
        width: calc(100% - 270px);
        padding-left: 50px;
    }
}

h1 {
  font-size: 44px;
  margin-bottom: 25px;
}

.head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
}


.referSection {
    padding: 30px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    margin: 1px;
    .heading {
        text-align: center;
        font-size: 24px;
        padding-bottom: 20px;
        font-weight: 600;
    }

    p {
        text-align: center;
        max-width: 60%;
        margin: 0 auto 30px;
    }

    .info {
        display: flex;
        justify-content: center;

        .section {
            padding: 0px  22px;
            display: flex;
            font-size: 14px;
            align-items: center;
        }

        .number {
            background: #D0A962;
            color: #fff;
            width: 26px;
            height: 26px;
            border-radius: 50%;
            text-align: center;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-right: 10px;
        }
        .text {
            width: calc(100% - 36px);
        }

    }
    .select {
        display: flex;
        justify-content: center;
        padding: 40px 0 30px;
    }
    .mainBox {
      text-align: center;
      .text{
        margin: 10px 10px;
        font-weight: 500;
      }
    }

    .box {
        margin: 0px 10px;
        padding: 20px 10px;
        width: 175px;
        height: 170px;
        position: relative;
        cursor: pointer;
        text-align: center;
        display: flex;
        align-items: center;
        justify-content: center;

        &.selected{
          border: 2px solid #cb9f52;
        }
    }



    input.radio {
        appearance: none;
        border: 1px solid #EBEBEB;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        margin-top: 0;
        border-radius: 0;
    }
    .button {
        width: 174px;
        margin: 0 auto;
    }

}



.referPopup {
    position: fixed;
    width: 100%;
    height: 100%;
    background: #00000040;
    top: 0;
    left: 0;
    z-index: 99;

    .box {
      display: flex;
      flex-direction: column;
      width: 695px;
      margin: 0 auto;
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
      position: fixed;
      top: 50%;
      padding: 30px;
      left: 50%;
      z-index: 9;
      background: #fff;
      transform: translate(-50%, -50%);
      input{
        border: 1px solid #E0E0E0;
        margin-bottom: 20px;
      }
    }

    &.show {
      display: flex !important;
    }

    &.hide {
      display: none;
    }
    .button {
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
        .primary-button {
            width: 200px;
            margin: 0 0 0 20px;
        }
    }

}


@media screen and (max-width: 1260px) {
    .referSection  {
        p{
            max-width: 80%;

        }
        .info {
            flex-direction: column;
            align-items: center;
        }

        .info .section {
            width: 330px;
            padding: 5px 10px;
        }

        .box {
            width: 100%;
            margin: 10px;
        }
        .mainBox{
          width: 45%;
          margin: 0 10px;
        }

        .select {
            display: flex;
            flex-wrap: wrap;
        }
    }

}
@media screen and (max-width: 992px) {
    .myAccount {
        padding: 20px 0 50px;
      }
    .sideMenu {
        width: 60px;
    }
    .contentSection .contents {
        width: calc(100% - 60px);
    }
    h1 {
        font-size: 30px;
        margin-bottom: 10px;
    }
}
@media screen and (max-width: 640px) {
    .contentSection {
      flex-direction: column;
      .contents {
        width: 100%;
        padding-left: 0;
        padding-top: 30px;
      }

    }
    .sideMenu {
        width: 100%;
    }

  }
@media screen and (max-width: 480px) {
    .referSection{
        padding: 20px;
        .info .section{
            width: auto;
        }
        .mainBox {
            width: 100%;
        }
    }

    h1 {
       font-size: 25px;
    }
}
