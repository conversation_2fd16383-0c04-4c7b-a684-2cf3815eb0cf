.orderDetail {
    padding: 40px 0;
}

.head {
    font-weight: 600;
}
.contentSec {
    display: flex;
    align-items: flex-start;
    padding-top: 15px;
    .sideBar {
        box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
        padding: 25px;
        width: 344px;
        font-size: 16px;
        .field {
            font-weight: 600;
            padding-bottom: 15px;
        }

        .address div {
            padding-bottom: 5px;
        }
        .line {
            border: 1px dashed #C9C9C9;
            margin: 20px 0px;
        }
        .amount {
            padding-bottom: 4px;
            div{
                justify-content: space-between;
            }
            .red {
                color: #ED5A5A;
            }

        }
    }

    .content {
        width: calc(100% - 344px);
        padding-left: 50px;
        .orders {
            display: flex;
            align-items: center;
            border: 1px solid #D5DBDE;
            padding: 20px;
        }

        span.image {
            width: 108px;
            height: 108px;
        }

        span.image img {
            width: 100%;
            height: 100%;
        }

        .orderDetails {
            width: calc(100% - 308px);
            padding-left: 30px;
            font-size: 14px;
            padding-right: 30px;

            .orderId {
                font-size: 16px;
                font-weight: 600;
            }

            .item {
                padding: 5px 0 2px;
            }

            .price {
                font-size: 16px;
                font-weight: 600;
            }

        }
        .button {
            border: 1px solid #ED5A5A;
            padding: 15px 50px;
            width: 200px;
            font-size: 14px;
            text-align: center;
            color: #ED5A5A;
            cursor: pointer;
        }


    }

    .orderedStatus {
        padding: 45px;
        background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
        margin-top: 20px;

            .statusBar {
                display: flex;
                font-size: 14px;
            }

            .line {
                position: relative;
                height: 12px;
                border-top: 1.5px dashed #C9C9C9;
                margin: 20px 0px 10px;
            }
            .sections {
                width: 100%;

                &.active{
                    .line{
                        border-top: 1.5px solid #1BA820;
                        &:before{
                            background:#1BA820;
                        }
                    }
                }
                &:last-child{
                    width: 120px;
                    .line{
                        border-top:none;
                    }
                }
            }
            .text {
                font-weight: 600;
            }
            .return {
                display: flex;
                color: #ED5A5A;
                font-size: 16px;
                padding-bottom: 15px;
                cursor: pointer;
                img{
                    margin-right: 5px;
                }
            }
            .line::before {
                position: absolute;
                content: "";
                left: 0;
                top: -7px;
                background: #BBBBBB;
                width: 12px;
                height: 12px;
                border-radius: 50%;
            }
            .bottom {
                display: flex;
                padding-top: 40px;
                justify-content: space-between;

                .statusAs {
                    position: relative;
                    width: 30%;
                }
                .date{
                    font-weight: 600;
                    padding-bottom: 5px;
                }
                .stat{
                    display: flex;
                    justify-content: flex-end;
                }

                .color {
                    width: 12px;
                    height: 12px;
                    border-radius: 50%;
                    background: #BBBBBB;
                    margin-right: 15px;
                    margin-top: 6px;
                }
                .rightSide {
                    width: calc(100% - 12px);
                    font-size: 16px;

                div {
                    span{
                        font-size: 12px;
                    }
                }
                }
            }

        &.cancelled{
            .sections {
                &:last-child{
                    .line{
                        border: none;
                        &:before{
                            background:#ED5A5A;
                        }
                    }
                }
            }

            .bottom{
                .color{
                    background:#ED5A5A;
                }
            }
        }
        .sections.cancelled {
          &:last-child{
              .line{
                  border: none;
                  &:before{
                      background:#ED5A5A;
                  }
              }
          }
      }
      .bottom.cancelled {
        .color{
            background:#ED5A5A;
        }
    }

    }

}

.referPopup {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  .box {
    display: flex;
    flex-direction: column;
    width: 695px;
    margin: 0 auto;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    position: fixed;
    top: 50%;
    padding: 30px;
    left: 50%;
    z-index: 9;
    background: #fff;
    transform: translate(-50%, -50%);
    border-radius: 12px;
    input{
      border: 1px solid #E0E0E0;
      margin-bottom: 20px;
    }
  }

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }
  .button {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
      .primary-button {
          width: 200px;
          margin: 0 0 0 20px;
      }
  }

}



@media screen and (max-width: 1260px) {
    .contentSec {
      display: flex;
      flex-direction: column-reverse;

      .content {
        width: 100%;
        padding-left: 0;
      }

      .sideBar {
        width: 100%;
        margin-top: 30px;
      }
    }
  }

  @media screen and (max-width: 768px) {
      .orderDetail {
          padding: 30px 0;
      }
    .contentSec {
      .content {
        .orders {
          flex-direction: column;
          align-items: flex-start;
          position: relative;

        }

        .orderDetails {
          width: 100%;
          padding: 20px 0px;
        }

      }

      .orderedStatus{
          padding: 20px;
          .statusBar{
              flex-direction: column;

              .text {
                  width: 120px;
              }
              .date {
                  width: 120px;
              }
          }
          .sections {
              display: flex;
              justify-content: space-between;
              &:last-child {
                  width: auto;
                  .line {
                      border-left: none;
                  }
              }
              &.active:last-child .line {
                  border-left: none;
              }
              &.active .line {
                  width: 1px;
                  border-left: 1.5px solid #1BA820;
                  border-top: none;
                  height: 80px;
                  top: 0;
                  margin: 0;

              }
              .line {
                  height: 80px;
                  border-left: 1.5px dashed #C9C9C9;
                  border-top: none;
                  width: 1px;
                  top: 0;
                  margin: 0;
                  padding: 0 10px;
                  &::before {
                      top: -2px;
                      left: -6px;
                  }
              }
          }

          .bottom {
              flex-direction: column;
              .statusAs {
                  width: 100%;
                  padding-top: 20px;
              }
          }


      }

    }



  }
