.sidebar {
    max-width: 270px;
    display: flex;
    flex-direction: column;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    padding: 20px 0;
}

.sidebar a {
    padding: 15px 20px;
    display: flex;
    align-items: center;
    &.active, &:hover{
        color: #CCA154;
        font-weight: 600;
        transition: ease .5s;
        path{
            stroke: #CCA154;
        }
        &.fill path{
            stroke: white;
            fill:#CCA154;
        }
    }
}

.sidebar a span {
    padding-left: 12px;
    font-size: 14px;
}

@media screen and (max-width: 992px) {
    
     .sidebar {
        max-width: 60px;
        a {
            position: relative;
            span {
                display: none;
                position: absolute;
                left: 40px;
                width: max-content;
                background: #fff;
                padding: 10px;
                box-shadow: 2px 2px 22px rgb(0 0 0 / 6%);
                z-index: 2;
            }
            &:hover span{
                display: block;
            }
        }
    }


}



@media screen and (max-width: 640px) {
    .sidebar {
        width: 100%;
        max-width: max-content;
        display: flex;
        flex-direction: row;
        overflow: scroll;
        padding: 10px 0;
    }
}