import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-order-placed',
  templateUrl: './order-placed.component.html',
  styleUrls: ['./order-placed.component.scss'],
})
export class OrderPlacedComponent implements OnInit {
  amount!: string;
  orderId!: string;
  constructor(private activatedRoute: ActivatedRoute) {}

  ngOnInit(): void {
    this.getQueryParams();
  }

  getQueryParams() {
    this.activatedRoute.queryParams.subscribe((params) => {
      this.amount = params.amount;
      this.orderId = params.order_id;
      //window.history.replaceState({}, '','cart/order-placed');
    });
  }
}
