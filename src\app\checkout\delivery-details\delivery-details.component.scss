.cartPage {
    padding: 40px 0;

    h1{
      display: none;
    }

    .contentSection {
      display: flex;
      flex-wrap: wrap;
      .leftSide {
        width: calc(100% - 460px);
        padding-right: 25px;
      }

      .rightSide {
        width: 460px;
      }
    }
}

.cartItems {
display: flex;
align-items: center;
position: relative;
padding: 12px 100px 12px 20px;
border: 1px solid #D5DBDE;
margin-bottom: 25px;

.image {
    width: 92px;
    height: 92px;
}

.details {
    width: 310px;
    padding-left: 25px;
}

.delete {
    position: absolute;
    right: 20px;
    top: 5px;
    cursor: pointer;
    padding: 10px;
}

.fav {
    position: absolute;
    right: 55px;
    top: 6px;
    cursor: pointer;
    padding: 10px;
}

.name {
    font-size: 14px;
}

.rate {
    font-size: 16px;
    font-weight: 600;
}
}

.country {
  position: relative;
  select {
    position: absolute;
    left: 0;
    width: 80px;
    margin-bottom: 0;
    height: 82%;
    top: 0;
    border: none !important;
    padding: 0 10px !important;
  }
}

input[type=tel] {
  padding-left: 90px !important;
}

.box {
border: 1px solid #D5DBDE;
padding: 25px;

    .promo {
        font-size: 14px;
        margin-bottom: 25px;
        .head {
        font-weight: 600;
        padding-bottom: 15px;
        }

        .enter {
            display: flex;
            justify-content: space-between;
        }
        .code {
            width: calc(100% - 110px);
            border: 1px solid #D5DBDE;
        }

        .primary-button {
            width: 100px;
        }

        input[type="text"] {
            margin-bottom: 0;
            border: none;
            height: 100%;
        }
    }

    .form-group {
        display: block;
        margin-bottom: 15px;


        input {
            padding: 0;
            height: initial;
            width: initial;
            margin-bottom: 0;
            display: none;
            cursor: pointer;
        }

        label {
            position: relative;
            cursor: pointer;
            font-weight: 400;
            font-size: 13px;
            display: flex;
            flex-direction: column;
        }

        label:before {
            content:'';
            -webkit-appearance: none;
            background-color: transparent;
            border: 1px solid #D5DBDE;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
            padding: 10px;
            display: inline-block;
            position: absolute;
            vertical-align: middle;
            cursor: pointer;
            margin-right: 10px;
        }
        input:checked + label:before {
            background: #D2AB66;
            border: 1px solid #D2AB66;
        }
        input:checked + label:after {
            content: '';
            display: block;
            position: absolute;
            top: 2px;
            left: 8px;
            width: 6px;
            height: 14px;
            border: solid #fff;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        input + label .type{
            display: none;
        }
        input:checked + label .type{
            display: block;

        }
        textarea{
            border: 1px solid #D5DBDE;
            margin-top: 20px;
        }
        .add{
            padding-left: 35px;
            padding-top: 2px;

        }
    }

    .amounts {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-size: 14px;
        &.discount{
            .name{
                color: #10347F;
            }
            .rate{
                color: #ED5A5A;
            }
        }
        &.grand{
            padding-top: 3px;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 25px;
        }
    }

    // .redeem {
    //     .form-group{
    //         border: 1px solid #D5DBDE;
    //         padding: 14px 20px;
    //     }
    //     .text {
    //         font-weight: 600;
    //         font-size: 14px;
    //     }
    //     label::before {
    //         top: 12px;
    //     }

    //     input:checked + label:after {
    //         top: 14px;
    //     }

    // }
    .redeem {
      .form-group{
          border: 1px solid #D5DBDE;
          padding: 14px 20px;
      }
      .text {
          font-weight: 600;
          font-size: 14px;
          display: flex;
      }
      label::before {
          top: 12px;
      }

      input:checked + label:after {
          top: 14px;
      }




      .balance {
        font-size: 13px;
        position: relative;
      }

      [type="radio"]:checked + label,
      [type="radio"]:not(:checked) + label
      {
        position: relative;
        padding-left: 38px;
        padding-top: 2px;
        cursor: pointer;
        line-height: 20px;
        display: block;
        color: #666;
        margin-left: 0;
      }
      [type="radio"]:checked + label:before,
      [type="radio"]:not(:checked) + label:before {
        content: '';
        position: absolute;
        right: 0;
        left: auto;
        top: 0;
        width: 17px;
        height: 17px;
        padding: 0;
        border: 2px solid #D2AB66;
        border-radius: 100%;
        background: #fff;
      }
      [type="radio"]:checked + label:after,
      [type="radio"]:not(:checked) + label:after {
        content: '';
        width: 13px;
        height: 13px;
        background: #D2AB66;
        position: absolute;
        top: 6px;
        right: 6px;
        border-radius: 100%;
        -webkit-transition: all 0.2s ease;
        transition: all 0.2s ease;
      }
      [type="radio"]:not(:checked) + label:after {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0);
      }
      [type="radio"]:checked + label:after {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1);
      }
      .form-group input[type="radio"]:checked + label:after {
        width: 9px;
        height: 9px;
        right: 14px;
        left: auto;
        border: none;
        transform: none;
        top: 4px;
      }

      .form-group input[type="radio"]:checked + label:before {background: #fff;width: 17px;border: 2px solid #D2AB66;height: 17px;padding: 0;}

  }

}
.paging {
display: flex;
justify-content: center;
margin-bottom: 40px;

.number {
    background: #D6D6D6;
    width: 26px;
    height: 26px;
    min-width: 26px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    margin-right: 13px;
    color: #fff;
    justify-content: center;
}

.numbering {
    display: flex;
    font-size: 14px;
    position: relative;
    padding-right: 130px;
}

.number.active {
    background: #D0A962;
}

.numbering:last-child {
    padding-right: 0;
}

.numbering::before {
    content: "";
    position: absolute;
    right: 10px;
    top: 13px;
    border-top: 1.5px dashed #E1E1E1;
    width: 40%;
    height: 1px;
}
.numbering:last-child::before {
    display: none;
}
}

.contentSection {
    // width: calc(100% - 270px);
    // padding-left: 50px;
    .leftSide {

        .box {
            border: none;
            background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        }
        .edit{
            position: absolute;
            right: 10px;
            top: 10px;
            display: flex;
            font-size: 14px;
            font-weight: 600;
            padding: 10px;
            cursor: pointer;
        }
        .head {
        font-weight: 600;
        padding-bottom: 20px;
        position: relative;
        }
        .pInfo {
            display: flex;

            .field {
            width: 290px;
            margin-bottom: 30px;
            font-size: 16px;
            }

            .text {
            font-weight: 600;
            padding-bottom: 5px;
            }
        }
        .addNew {
            display: flex;
            position: absolute;
            right: 0;
            top: 0;
            font-size: 14px;
            cursor: pointer;
        }
        .address {
            padding-left: 30px;
            .name {
                font-size: 16px;
            }

            div {
                font-size: 14px;
                padding-bottom: 6px;
            }
        }
        .delete{
            position: absolute;
            right: 10px;
            bottom: 10px;
            padding: 10px;
            cursor: pointer;
        }
    }
}

.box .redeem .form-group{

    input:checked + label:before {
        background: #D2AB66;
        border: 1px solid #D2AB66;
    }
}
.sameAs {
    display: flex;
    position: absolute;
    right: 0;
    top: 0;
    font-size: 14px;
    cursor: pointer;
    input {
        padding: 0;
        height: initial;
        width: initial;
        margin-bottom: 0;
        display: none;
        cursor: pointer;
    }

    label {
        position: relative;
        cursor: pointer;
        font-weight: 400;
        font-size: 13px;
        display: flex;
        flex-direction: column;
    }

    label:before {
        content:'';
        -webkit-appearance: none;
        background-color: transparent;
        border: 1px solid #D5DBDE;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
        padding: 10px;
        display: inline-block;
        position: absolute;
        vertical-align: middle;
        cursor: pointer;
        margin-right: 10px;
    }
    input:checked + label:before {
        background: #D0A962;
        border: 1px solid #D0A962;
    }
    input:checked + label:after {
        content: '';
        display: block;
        position: absolute;
        top: 2px;
        left: 8px;
        width: 6px;
        height: 14px;
        border: solid #fff;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
    input + label .type{
        display: none;
    }
    input:checked + label .type{
        display: block;

    }
    textarea{
        border: 1px solid #D5DBDE;
        margin-top: 20px;
    }
    .add{
        padding-left: 35px;
        padding-top: 2px;

    }
}

.form {
    .twoSection{
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .field {
            width: 49%;
        }
    }
    .threeSection {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .field {
            width: 32%;
        }
    }


    input,
    select,
    textarea {
      border: 1px solid #BBBBBB;
    }

    select {
      -webkit-appearance: none;
      -moz-appearance: none;
      background-image: url("/assets/images/angle-down.svg");
      background-repeat: no-repeat;
      background-position: 98%;

    }

    .field span {
      font-size: 14px;
      color: #787878;
      font-weight: 500;
      margin-bottom: 10px;
      text-transform: capitalize;
    }

    .field.time {
      .timePicker {
        position: relative;
        &::after {
          content: "";
          position: absolute;
          width: 24px;
          height: 24px;
          z-index: -1;
          right: 10px;
          top: 10px;
          background-image: url(/assets/images/clock.svg);
          background-repeat: no-repeat;
        }
      }


      input {
        cursor: pointer;
        display: block;
        width: 100%;
        padding: 10px;
        background: transparent;
        font-size: 12px;
        &:focus-visible {
          outline: none;
        }
      }
    }
    .field.gender {
        display: flex;
        align-items: center;
        span {
            padding-right: 50px;
            margin-bottom: 0;
        }

        .radioBtn {
            padding-bottom: 0;
        }

    }

    .leftSide img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }
.buttons {
    display: flex;
    justify-content: flex-end;
    .primary-button {
        width: 220px;
        margin-left: 20px;
    }
}

.filtername {
    padding: 12px 20px;
    border: 1px solid #D5DBDE;
    margin-right: 15px;
    font-size: 14px;
    cursor: pointer;
    width: 100%;
    height: 49px;
    position: relative;
    margin-bottom: 20px;

    img {
      padding-right: 10px;
    }
  }

  .drop {
    display: none;
  }

.selectOption {

  span {
    padding-right: 5px;
  }

  .selectMenu {
    font-size: 14px;
    color: #000;
    position: relative;
    padding-right: 25px;

    &.sort {
      &::before {
        content: "";
        background-image: url(/assets/images/angle-down.svg);
        position: absolute;
        right: 0px;
        top: 3px;
        width: 20px;
        height: 20px;
      }
    }
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    position: absolute;
    opacity: 0;
    box-shadow: 0px 2px 2px #D5DBDE;
    background: #fff;
    width: 100%;
    left: 0px;
    top: 49px;
    z-index: -1;

    &.active {
      z-index: 3;
      opacity: 1;
      transition: all linear .2s;
      z-index: 1;
    }

    &:before {
      content: "";
      position: absolute;
      left: 0;
      top: -12px;
      width: 100%;
      height: 20px;

    }

    li {
      padding: 0px 15px;

      &:hover {
        color: #D2AB66;
      }
    }
  }

}

  .redeemReward{
      .add::before {
          content: "";
          position: absolute;
          right: 0;
          background-image: url(/assets/images/checkout/Vector1.svg);
          height: 14px;
          width: 7px;
          top: 15px;
          background-size: contain;
          background-repeat: no-repeat;
      }
  }

  .referPopup {
      position: fixed;
      width: 100%;
      height: 100%;
      background: #00000040;
      top: 0;
      left: 0;
      z-index: 99;

      .box {
        display: flex;
        flex-direction: column;
        width: 695px;
        margin: 0 auto;
        box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
        position: fixed;
        top: 50%;
        padding: 30px;
        left: 50%;
        z-index: 9;
        background: #fff;
        transform: translate(-50%, -50%);
        input{
          border: 1px solid #E0E0E0;
          margin-bottom: 20px;
        }
      }

      &.show {
        display: flex !important;
      }

      &.hide {
        display: none;
      }
      .button {
          display: flex;
          justify-content: flex-end;
          margin-top: 10px;
          .primary-button {
              width: 200px;
              margin: 0 0 0 20px;
          }
      }

  }




[type="radio"]:checked,
[type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}
[type="radio"]:checked + label,
[type="radio"]:not(:checked) + label
{
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    line-height: 20px;
    display: inline-block;
    color: #666;
    margin-left: -27px;
}
[type="radio"]:checked + label:before,
[type="radio"]:not(:checked) + label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 3px;
    width: 17px;
    height: 17px;
    border: 2px solid #D2AB66;
    border-radius: 100%;
    background: #fff;
}
[type="radio"]:checked + label:after,
[type="radio"]:not(:checked) + label:after {
    content: '';
    width: 9px;
    height: 9px;
    background: #D2AB66;
    position: absolute;
    top: 7px;
    left: 4px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
[type="radio"]:not(:checked) + label:after {
    background: #C9C9C9;
}
[type="radio"]:not(:checked) + label:before {
    border-color: #C9C9C9;
}
[type="radio"]:checked + label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
.radioBtn {
    display: flex;
    padding-bottom: 12px;
    align-items: center;

    .checkbox+.checkbox, .radio {
        margin-top: 10px;
    }
    .radio {
        margin: 0 50px 0 0;
        color: #000;
        font-size: 14px;
    }

    label.radio-label {
        color: #000;
    }
}

.info {
  cursor: pointer;
  padding-left: 5px;

  .infoTxt {
    display: none;
    position: absolute;
    width: max-content;
    right: -16px;
    top: -42px;
    background: #FFFFFF;
    box-shadow: 0px 0px 13px rgba(0, 0, 0, 0.11);
    padding: 10px;
    font-size: 10px;
    z-index: 1;
    font-weight: 400;
    ul {
      li{
        line-height: 20px;
      }
    }
    // &::before {
    //   content: "";
    //   position: absolute;
    //   top: -7px;
    //   right: 10px;
    //   border: 15px solid #fff;
    //   transform: rotate(45deg);
    //   z-index: -1;
    // }
  }

  &:hover {
    .infoTxt {
      display: block;
    }

  }
}




@media screen and (max-width:1200px) {

    .cartPage .contentSection .leftSide {
        width: 100%;
        padding-right: 0;
    }

    .cartPage .contentSection .rightSide {
        width: 100%;
    }
}
@media screen and (max-width:640px) {
    .paging  {
        flex-direction: column;
        max-width: 250px;
        margin: 0 auto;
        .numbering{
          padding-right: 0;
          padding-bottom: 50px;
        }

        .numbering::before {
          border-left: 1.5px dashed #E1E1E1;
          width: 1px;
          height: 30px;
          left: 13px;
          top: 35px;
        }
    }
    .contentSection .leftSide .box {
        padding: 50px 20px;
    }

    .sameAs {
        position: relative;
        padding-top: 20px;
    }
    .form{
        .threeSection .field  {
            width: 100%;
        }

        .twoSection .field {
            width: 100%;
        }
    }
    .box{
        padding: 20px;

        .form-group {
            padding: 10px;
        }
    }
}
