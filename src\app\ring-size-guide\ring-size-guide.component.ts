import { Component, OnInit } from '@angular/core';
import { ApiService } from '../Services/api.service';
import { apiEndPoints } from '../ApiEndPoints';

@Component({
  selector: 'app-ring-size-guide',
  templateUrl: './ring-size-guide.component.html',
  styleUrls: ['./ring-size-guide.component.scss']
})
export class RingSizeGuideComponent implements OnInit {
  data:any

  constructor(private apiservice: ApiService) { }

  ngOnInit(): void {
    this.apiservice.getData(apiEndPoints.ringSizeGuide).subscribe((res: any) => {
      if(res?.errorcode == 0){
        this.data = res?.data
      }
    })
  }

}
