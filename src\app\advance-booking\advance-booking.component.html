<div
  class="adBanner"
  [ngStyle]="{ 'background-image': 'url(' + firstBanner + ')' }"
>
  <div class="content">
    <h1>{{ advanceBookingData.advance_title }}</h1>
    <p>{{ advanceBookingData.advance_description }}</p>
    <span (click)="loginCheck()" class="primary-button golden">Book Now</span>
  </div>
</div>

<div class="container">
  <div class="howItWorks">
    <div class="title">
      <h2>How it works</h2>
    </div>
    <div class="contents">
      <div class="box">
        <img src="\assets\images\advance-booking\Group 21754.svg" alt="Description for step 1" />
        <div class="step">Step 1</div>
        <p>Enter your mobile number and verify with OTP</p>
      </div>
      <div class="box">
        <img src="\assets\images\advance-booking\Group 21755.svg" alt="Description for step 2" />
        <div class="step">Step 2</div>
        <p>Specify quantity and period of advance</p>
      </div>
      <div class="box">
        <img src="\assets\images\advance-booking\Group 21756.svg" alt="Description for step 3" />
        <div class="step">Step 3</div>
        <p>Add personal Information</p>
      </div>
      <div class="box">
        <img src="\assets\images\advance-booking\Group 21757.svg" alt="Description for step 4" />
        <div class="step">Step 4</div>
        <p>Make your payment</p>
      </div>
    </div>
  </div>
</div>

<div
  class="container"
  (click)="bannerRedirections(banner?.redirection_type, banner?.redirection_id)"
>
  <div class="bannerAd">
    <img src="{{ imageBase }}/{{ banner?.image }}" alt="banner image" />
  </div>
</div>
<div class="justIn">
  <div class="container" [innerHTML]="advanceBookingData.advance_content">
    <!-- <h2>Book in Advance Save in Abundance</h2>
    <div class="section">
      <h6>Advance Booking Scheme</h6>
      <p>Insure yourself and your family against gold price hike! Yes, that is exactly what we are offering through our Advance Booking scheme.</p>
      <p>At TT, we understand your love for exceptional jewellery that reflects your style without creating a dent in your purse. That's why we have come up with our innovative Advance Booking scheme, designed to empower you to own exquisite gold jewellery at a lower rate even in the face of rising gold prices.</p>
    </div>
    <div class="section">
      <h6>Pay Now To Save More!</h6>
      <p>How does it work? It's simple. By joining this scheme, you can pay an advance amount and book the amount of gold you want to buy after a period. For example, there is a wedding in the family after six months, and you do not want to wait and buy gold as the gold price is always fluctuating. Or you want to buy gold as an investment but you do not have the full amount to pay for it now. Advance Booking is the solution for all these situations and needs.</p>
      <p>The booking period could be days, weeks or months and can go upto one year. Once this period comes to close, you can purchase the gold. If the price has increased from the day you joined the scheme, you can buy jewellery at the rate you blocked it.</p>
      <p>What if the price is lower than what you paid? No worries, pay the current rate and take home your precious jewellery.</p>
    </div> -->
  </div>
</div>
<div class="container">
  <div class="terms">
    <div class="title">
      <h2>Terms & Conditions</h2>
    </div>
    <div class="contents">
      <ul *ngFor="let item of terms">
        <li>{{ item.terms }}</li>
      </ul>
      <div class="bottom">
        <span (click)="loginCheck()" class="primary-button golden"
          >Book Now</span
        >
      </div>
    </div>
  </div>
</div>
<div class="helpiline">
  <div class="container">
    <div class="content">
      <span>Help Line Number</span>
      <a href="callto:+91 9497 916 000"
        ><img src="\assets\images\advance-booking\Vector.svg" alt="phone icon" />+91 9497
        916 000</a
      >
    </div>
  </div>
</div>
