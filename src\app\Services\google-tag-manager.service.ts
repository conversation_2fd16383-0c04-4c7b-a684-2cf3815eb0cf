import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class GoogleTagManagerService {
  public renderer: Renderer2;
  domain: boolean = false;
  constructor(private _renderer: RendererFactory2) {
    this.renderer = _renderer.createRenderer(null, null);
  }

  loadTags() {
    const domain = window.location.hostname.includes('beta');
    if (!domain) {
      const div = this.renderer.createElement('div');
      div.innerHTML = ` <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "G-27TKJ4KJ50");
    </script>
    <script type="text/javascript">
    (function (c, l, a, r, i, t, y) {
      c[a] =
        c[a] ||
        function () {
          (c[a].q = c[a].q || []).push(arguments);
        };
      t = l.createElement(r);
      t.async = 1;
      t.src = "https://www.clarity.ms/tag/" + i;
      y = l.getElementsByTagName(r)[0];
      y.parentNode.insertBefore(t, y);
    })(window, document, "clarity", "script", "cn08ia2134");
  </script>`;
      //document.head.appendChild(div)
      const script = this.renderer.createElement('script');
      script.src = 'https://www.googletagmanager.com/gtag/js?id=G-27TKJ4KJ50';
      this.renderer.appendChild(document.head, div);
      this.renderer.appendChild(document.head, script);
    }
  }
}
