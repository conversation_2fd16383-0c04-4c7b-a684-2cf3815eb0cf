import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute, Router } from '@angular/router';
import { brand } from '../shared/models/brands.model';
import { environment } from 'src/environments/environment.prod';
import { Meta } from '@angular/platform-browser';

@Component({
  selector: 'app-zora-collection',
  templateUrl: './zora-collection.component.html',
  styleUrls: ['./zora-collection.component.scss'],
})
export class ZoraCollectionComponent implements OnInit {
  data: any;
  contactForm!: FormGroup;
  id!: number;
  brand!: brand;
  firstBanner!: string;
  secondBanner!: string;
  thirdBanner!: string;
  imageBase: string = environment.imageBase;
  constructor(
    private ApiService: ApiService,
    private fb: FormBuilder,
    private toast: ToastrService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private meta : Meta
  ) {}

  ngOnInit(): void {
    this.getIdFromRoute();
    this.ApiService.getData(apiEndPoints.ZORA_COLLECTION).subscribe(
      (res: any) => {
        if (res?.errorcode == 0) {
          this.data = res?.data;
        }
      }
    );

    this.initContactForm();
  }
  getIdFromRoute() {
    this.activatedRoute.data.subscribe((data) => {
      this.id = data.id;
      this.getBrandDetails();
    });
  }
  initContactForm() {
    this.contactForm = this.fb.group({
      name: ['', Validators.required],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern('[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}$'),
        ],
      ],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      message: ['', Validators.required],
      checkbox: [false, Validators.required],
    });
  }

  getBrandDetails() {
    this.ApiService.getBrandDetails(apiEndPoints.brand_page, this.id).subscribe(
      (res) => {
        if (res.ErrorCode === 0) {
          this.brand = res.Data;
          this.firstBanner = this.imageBase + '/' + this.brand?.main_banner;
          this.secondBanner =
            this.imageBase + '/' + this.brand?.mi_gold_banner?.image;
          this.thirdBanner =
            this.imageBase + '/' + this.brand?.advance_booking_banner?.image;
            this.updateMeta()
        } else {
          this.toast.error('Something went wrong', 'Error');
        }
      },
      (error) => {
        this.toast.error(error, 'Error');
      }
    );
  }

  get cf() {
    return this.contactForm?.controls;
  }

  bannerRedirections(type: any, id: any, title: any) {
    if (type == 1) this.router.navigate(['/detail', id]);
    else if (type == 2 || 3 || 4) this.router.navigate(['/listing', type, id]);
    if (title == 'Zora') this.router.navigateByUrl('/zora-collection');

    if (type == 5 && id == 3) {
      this.router.navigateByUrl('/virtual-shopping');
    } else if (type == 5 && id == 4) {
      this.router.navigateByUrl('/zora-collection');
    } else if (type == 5 && id == 5) {
      this.router.navigateByUrl('/divo-collection');
    } else if (type == 5 && id == 6) {
      this.router.navigateByUrl('/poyem-collection');
    } else if (type == 5 && id == 7) {
      this.router.navigateByUrl('/orlin-collection');
    } else if (type == 5 && id == 8) {
      this.router.navigateByUrl('/book-appointment');
    } else if (type == 5 && id == 2) {
      this.router.navigateByUrl('/gold-schemes');
    } else if (type == 5 && id == 1) {
      this.router.navigateByUrl('/advance-booking');
    }else if(type == 5 && id == 9){
      this.router.navigateByUrl('/gift-voucher');
    }else if (type == 5 && id == 10){
      this.router.navigateByUrl('/digital-gold');
    }else if (type == 5 && id == 11){
      this.router.navigateByUrl('/about');
    }
  }

  submitData() {
    if (this.contactForm.invalid) {
      this.contactForm.markAllAsTouched();
      this.toast.error('Invalid Form');
      return;
    } else if (this.contactForm.value.checkbox === false) {
      this.toast.error('Please accept the agreements');
      return;
    }

    this.ApiService.postData(
      apiEndPoints.CONTACT_US,
      this.contactForm.value
    ).subscribe(
      (res: any) => {
        if (res?.errorcode == 0) {
          this.toast.success('Submitted Successfully');
          this.contactForm.reset();
        } else this.toast.error(res?.message);
      },
      (error) => {
        this.toast.error(error, 'Error');
      }
    );
  }

  updateMeta () {
    this.meta.updateTag({name: 'title', content: this.brand?.main_title })
    this.meta.updateTag({name: 'description', content: this.brand?.main_subtitle })
    this.meta.updateTag({property: 'og:image', content: this.imageBase + '/' + this.brand?.main_banner })
    this.meta.updateTag({property: 'og:title', content: this.brand?.main_title })
    this.meta.updateTag({property: 'og:description', content: this.brand?.main_subtitle })
    this.meta.updateTag({rel : 'canonical', href : 'https://ttdevassyjewellery.com/'})
  }


}


