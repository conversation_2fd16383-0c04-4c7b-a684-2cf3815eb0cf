.cartPage {
    padding: 40px 0;
  
    .contentSection {
      display: flex;
      flex-wrap: wrap;
      .leftSide {
        width: calc(100% - 460px);
        padding-right: 25px;
      }
  
      .rightSide {
        width: 460px;
      }
    }
}
  
.contentSection {
    // width: calc(100% - 270px);
    // padding-left: 50px;
    .leftSide {

        .box {
            border: none;
            background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
        padding: 30px;
        margin-bottom: 30px;
        position: relative;
        }
        .edit{
            position: absolute;
            right: 10px;
            top: 10px;
            display: flex;
            font-size: 14px;
            font-weight: 600;
            padding: 10px;
            cursor: pointer;
        }
        .head {
        font-weight: 600;
        padding-bottom: 20px;
        position: relative;
        }
        .pInfo {
            display: flex;

            .field {
            width: 290px;
            margin-bottom: 30px;
            font-size: 16px;
            }

            .text {
            font-weight: 600;
            padding-bottom: 5px;
            }
        }  
        .addNew {
            display: flex;
            position: absolute;
            right: 0;
            top: 0;
            font-size: 14px;
            cursor: pointer;
        }
        .address {
            padding-left: 30px;
            .name {
                font-size: 16px;
            }
            
            div {
                font-size: 14px;
                padding-bottom: 6px;
            }
        }
        .delete{
            position: absolute;
            right: 10px;
            bottom: 10px;
            padding: 10px;
            cursor: pointer;
        }
    }
}

.head {
    font-weight: 600;
    padding-bottom: 15px;
    }
.box {
    border: 1px solid #D5DBDE;
    padding: 25px;
    
        .promo {
            font-size: 14px;
            margin-bottom: 25px;
    
            .enter {
                display: flex;
                justify-content: space-between;
            }
            .code {
                width: calc(100% - 110px);
                border: 1px solid #D5DBDE;
            }
            
            .primary-button {
                width: 100px;
            }
            
            input[type="text"] {
                margin-bottom: 0;
                border: none;
                height: 100%;
            }
        }
    
        .form-group {
            display: block;
            margin-bottom: 15px;
            
            
            input {
                padding: 0;
                height: initial;
                width: initial;
                margin-bottom: 0;
                display: none;
                cursor: pointer;
            }
            
            label {
                position: relative;
                cursor: pointer;
                font-weight: 400;
                font-size: 13px;
                display: flex;
                flex-direction: column;
            }
            
            label:before {
                content:'';
                -webkit-appearance: none;
                background-color: transparent;
                border: 1px solid #D5DBDE;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
                padding: 10px;
                display: inline-block;
                position: absolute;
                vertical-align: middle;
                cursor: pointer;
                margin-right: 10px;
            }
            input:checked + label:before {
                background: #000;
                border: 1px solid #000;
            }
            input:checked + label:after {
                content: '';
                display: block;
                position: absolute;
                top: 2px;
                left: 8px;
                width: 6px;
                height: 14px;
                border: solid #fff;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
            }
            input + label .type{
                display: none;
            }
            input:checked + label .type{
                display: block;
    
            }
            textarea{
                border: 1px solid #D5DBDE;
                margin-top: 20px;
            }
            .add{
                padding-left: 35px;
                padding-top: 2px;
    
            }
        }  
        
        .amounts {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
            &.discount{
                .name{
                    color: #10347F;
                }
                .rate{
                    color: #ED5A5A;
                }
            }
            &.grand{
                padding-top: 3px;
                font-weight: 600;
                font-size: 16px;
                margin-bottom: 25px;
            }
        }
            
        .redeem {
            .form-group{
                border: 1px solid #D5DBDE;
                padding: 14px 20px;
            }
            .text {
                font-weight: 600;
                font-size: 14px;
            }
            label::before {
                top: 12px;
            }
            
            input:checked + label:after {
                top: 14px;
            }
                
        } 
        
}


.form {
    .twoSection{
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .field {
            width: 49%;
        }
    }
    .threeSection {
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;
        .field {
            width: 32%;
        }
    }
    
    
    input,
    select,
    textarea {
      border: 1px solid #BBBBBB;
    }

    select {
      -webkit-appearance: none;
      -moz-appearance: none;
      background-image: url("/assets/images/angle-down.svg");
      background-repeat: no-repeat;
      background-position: 98%;

    }
    label {
        font-weight: 400;
        margin-bottom: 0;
        display: flex;
        align-items: flex-start;
    }
    .field span {
      font-size: 14px;
      color: #787878;
      font-weight: 500;
      text-transform: capitalize;
    }

    .field{
        .text-danger{
            bottom: -4px;
        }
    }
    .leftSide img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
    .field .need{
        color: rgba(29, 29, 29, 1);
    }
    .field .hereBy {
        font-size: 14px;
        display: flex;
        flex-wrap: wrap;
        margin-top: 14px;
        align-items: center;
        color: rgba(29, 29, 29, 1);
        input[type="checkbox"] {
            margin-right: 10px;
            margin-top: 2px;
        }
        a{
            color: #D0A962;
        }
        label span{
            padding-right: 4px;
        }

    }
  }


  
@media screen and (max-width:1200px) {

    .cartPage .contentSection .leftSide {
        width: 100%;
        padding-right: 0;
    }

    .cartPage .contentSection .rightSide {
        width: 100%;
    }
}
@media screen and (max-width:640px) {
    .paging  {
        flex-direction: column;
        max-width: 250px;
        margin: 0 auto;
        .numbering{
          padding-right: 0;
          padding-bottom: 50px;
        }
      
        .numbering::before {
          border-left: 1.5px dashed #E1E1E1;
          width: 1px;
          height: 30px;
          left: 13px;
          top: 35px;
        }
    }  
    .contentSection .leftSide .box {
        padding: 50px 20px;
    }
    
    .sameAs {
        position: relative;
        padding-top: 20px;
    }
    .form{
        .threeSection .field  {
            width: 100%;
        }
        
        .twoSection .field {
            width: 100%;
        }
    }
    .box{
        padding: 20px;
        
        .form-group {
            padding: 10px;
        }
    }
}