import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from 'src/app/Services/api.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import {
  Component,
  ElementRef,
  NgZone,
  OnInit,
  ViewChild,
} from '@angular/core';
import { environment } from 'src/environments/environment.prod';
import { Router } from '@angular/router';
declare var Razorpay: any;

@Component({
  selector: 'app-booking',
  templateUrl: './booking.component.html',
  styleUrls: ['./booking.component.scss'],
})
export class BookingComponent implements OnInit {
  @ViewChild('form') form!: ElementRef;
  stores: any;
  sectionHide: any = false;
  sectionTwoHide: any = false;
  sectionBack: any = false;
  bookingForm!: FormGroup;
  editSectionOne: boolean = false;
  editPersonalInfo: boolean = false;
  addPersonalInfo: boolean = false;
  addSectionOne: boolean = true;
  confirmSection: boolean = false;
  selectedStore: any;
  selectedPeriod: any;
  periods: any;
  paymentDetails: any;
  countries: any;
  selectedCountry: any;
  states: any;
  selectedState: any;
  bookingDetails: any;
  isChecked: boolean = false;
  // encRequest: any;
  // accessCode: any;
  countryValidation: any;
  stateValidation: any;
  isLoading: boolean = false;
  ccAvenueUrl: string = environment.ccAvenueProduction;
  ccAvenueBeta: string = environment.ccAvenueStaging;
  checkDomain: boolean = false;
  @ViewChild('myInput') myInput: any;
  bookingResponse: any;
  private rzp: any;
  windowRef = window;

  constructor(
    private formbuilder: FormBuilder,
    private ApiService: ApiService,
    private toast: ToastrService,
    private router: Router,
    private ngZone: NgZone
  ) { }

  ngOnInit(): void {
    this.checkDomain = window.location.hostname.includes('beta');
    this.initBookingForm();
    this.getStores();
    this.getPeriod();
    this.getCountries();
    this.getBookingDetails();
  }

  getStores() {
    this.ApiService.getData(apiEndPoints.GET_STORES).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.stores = res?.Data;
      }
    });
  }

  getPeriod() {
    this.ApiService.getData(apiEndPoints.GET_PERIOD).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.periods = res?.Data;
      }
    });
  }

  getPaymentDetails() {
    let gold_weight = this.bookingForm.get('weight')?.value;
    if (gold_weight && this.selectedPeriod) {
      this.ApiService.postData(apiEndPoints.ADVANCE_PERCENTAGE, {
        gold_weight: gold_weight,
        period_id: this.selectedPeriod,
      }).subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.paymentDetails = res?.Data;
        }
      });
    }
  }

  getCountries() {
    this.ApiService.getData(apiEndPoints.COUNTRY).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.countries = res?.Data;
      }
    });
  }

  selectCountry() {
    this.ApiService.postData(apiEndPoints.STATE, {
      countryId: this.selectedCountry,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.states = res?.Data;
        this.selectedState = this.states[0]['id'];
        this.bookingForm?.get('state')?.enable();
      }
    });
  }

  initBookingForm() {
    this.bookingForm = this.formbuilder.group({
      weight: [''],
      name: ['', Validators.required],
      mail: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      house_name: ['', [Validators.required]],
      street: ['', [Validators.required]],
      district: ['', [Validators.required]],
      city: ['', [Validators.required]],
      code: ['91'],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      pan: [
        '',
        [
          Validators.required,
          Validators.pattern(/^([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}?$/),
        ],
      ],
      aadhar: ['', [Validators.required, Validators.pattern(/^\d{12}$/)]],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      nominee_name: ['', Validators.required],
      nominee_relation: ['', Validators.required],
    });
  }

  get bf() {
    return this.bookingForm.controls;
  }

  nextOne() {
    if (this.bookingForm.get('weight')?.value == '0') {
      this.toast.error('Minimum weight is 1 gram');
      return;
    }

    if (
      this.selectedStore &&
      this.selectedPeriod &&
      this.bookingForm.get('weight')?.value
    ) {
      //Webengage Start
      this.windowRef.webengage.track('Quantity and period', {
        'Store selected': this.stores.find((x: any) => x.id == this.selectedStore),
        'Period (days)': this.selectedPeriod,
        'Gold weight (gm)': parseInt(this.bookingForm.get('weight')?.value),
      });

      this.addSectionOne = false;
      this.addPersonalInfo = true;
      this.sectionHide = true;
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth',
      });
    } else this.toast.error('Please fill the form');
  }

  confirmDetails() {
    if (!this.selectedCountry) {
      this.countryValidation = 'Country is required';
    }

    if (!this.selectedState) {
      this.stateValidation = 'State is required';
    }

    if (this.bookingForm?.invalid) {
      this.bookingForm.markAllAsTouched();
      return;
    }

    let data = {
      store_id: this.selectedStore,
      period_id: this.selectedPeriod,
      gold_weight: this.bookingForm.get('weight')?.value,
      name: this.bookingForm.get('name')?.value,
      country_code: this.bookingForm.get('code')?.value,
      mobile: this.bookingForm.get('mobile')?.value,
      email: this.bookingForm.get('mail')?.value,
      address_1: this.bookingForm.get('house_name')?.value,
      address_2: this.bookingForm.get('street')?.value,
      address_3: this.bookingForm.get('city')?.value,
      address_4: this.bookingForm.get('district')?.value,
      pan_no: this.bookingForm.get('pan')?.value.toUpperCase(),
      aadhar_no: this.bookingForm.get('aadhar')?.value,
      pincode: this.bookingForm.get('pincode')?.value,
      country_id: this.selectedCountry,
      state_id: this.selectedState,
      nominee_name: this.bookingForm.get('nominee_name')?.value,
      nominee_relation: this.bookingForm.get('nominee_relation')?.value,
    };

    // this.isLoading = true
    this.ApiService.postData(
      apiEndPoints.ADVANCE_BOOKING_STORE,
      data
    ).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.bookingResponse = res?.Data?.data;

        //Webengage Start
        let countryName = this.countries.find((c: any) => c?.id == this.selectedCountry)
        let stateName = this.states.find((s: any) => s?.id == this.selectedState)

        this.windowRef.webengage.track('Personal Details', {
          Name: this.bookingForm.get('name')?.value,
          'Phone Number': String(this.bookingForm.get('mobile')?.value),
          'Email Id': this.bookingForm.get('mail')?.value,
          PAN: this.bookingForm.get('pan')?.value ? true : false,
          Country: countryName?.name,
          State: stateName?.name,
          'House Name/Flat No': this.bookingForm.get('house_name')?.value,
          District: this.bookingForm.get('district')?.value,
          'Street/Location': this.bookingForm.get('street')?.value,
          'Aadhar Number': this.bookingForm.get('aadhar')?.value ? true : false,
          City: this.bookingForm.get('city')?.value,
          Pincode: String(this.bookingForm.get('pincode')?.value),
          'Nominee Name': this.bookingForm.get('nominee_name')?.value,
          'Nominee Relationship': this.bookingForm.get('nominee_relation')?.value,
        });
        //Webengage End

        // this.accessCode = res?.Data?.accesscode;
        // this.encRequest = res?.Data?.encrypted_data;
        this.addPersonalInfo = false;
        this.sectionHide = false;
        this.sectionTwoHide = true;
        this.confirmSection = true;
        this.getBookingDetails();
        window.scroll({
          top: 0,
          left: 0,
          behavior: 'smooth',
        });
        // this.isLoading = false
      } else {
        // this.isLoading = false
        this.toast.error(res?.Message);
      }
    });
  }

  getBookingDetails() {
    this.ApiService.postData(
      apiEndPoints.ADVANCE_BOOKING_DETAILS,
      {}
    ).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.bookingDetails = res?.Data;

        if (this.bookingDetails) {
          this.selectedStore = this.bookingDetails?.branch_id;
          this.selectedPeriod = this.bookingDetails?.period_id;
          this.bookingForm
            ?.get('weight')
            ?.setValue(this.bookingDetails?.booked_weight);
          this.bookingForm?.get('name')?.setValue(this.bookingDetails?.name);
          this.bookingForm?.get('mail')?.setValue(this.bookingDetails?.mail_id);
          this.bookingForm
            ?.get('house_name')
            ?.setValue(this.bookingDetails?.address_1);
          this.bookingForm
            ?.get('street')
            ?.setValue(this.bookingDetails?.address_2);
          this.selectedCountry = this.bookingDetails?.country_id;
          this.bookingForm
            ?.get('city')
            ?.setValue(this.bookingDetails?.address_3);
          this.bookingForm
            ?.get('code')
            ?.setValue(this.bookingDetails?.country_code);
          this.bookingForm
            ?.get('mobile')
            ?.setValue(this.bookingDetails?.mobile_no);
          this.bookingForm
            ?.get('district')
            ?.setValue(this.bookingDetails?.address_4);
          this.bookingForm?.get('pan')?.setValue(this.bookingDetails?.pan_no);
          this.bookingForm
            ?.get('aadhar')
            ?.setValue(this.bookingDetails?.aadhar_no);
          this.selectedState = this.bookingDetails?.state_id;
          this.bookingForm
            ?.get('pincode')
            ?.setValue(this.bookingDetails?.pincode);
          this.bookingForm
            ?.get('nominee_name')
            ?.setValue(this.bookingDetails?.nominee);
          this.bookingForm
            ?.get('nominee_relation')
            ?.setValue(this.bookingDetails?.nominee_relation);
        }
        if (this.bookingDetails?.country_id) {
          this.ApiService.postData(apiEndPoints.STATE, {
            countryId: this.selectedCountry,
          }).subscribe((res: any) => {
            if (res?.ErrorCode == 0) {
              this.states = res?.Data;
              this.bookingForm?.get('state')?.enable();
            }
          });
        }
      }
    });
  }

  showEdit() {
    this.confirmSection = false;
    this.sectionTwoHide = false;
    this.addSectionOne = true;
    this.getBookingDetails();
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });
  }

  bookNow() {
    if (this.isChecked) {
      // setTimeout(() => {
      //   this.form.nativeElement.submit()
      // }, 500)
      const options = {
        key: this.bookingResponse.api_key,
        order_id: this.bookingResponse.razorpay_order_id,
        amount: this.bookingResponse.amount, // amount in paise
        currency: 'INR',
        name: 'TT Devassy',
        description: 'Advance Booking',
        handler: (response: any) => {
          this.isLoading = true;
          // Handle success
          this.ApiService.postData('advance-booking-payment', {
            order_id: this.bookingResponse.order_id,
            ...response,
          }).subscribe((res: any) => {
            this.isLoading = false;
            if (res?.ErrorCode == 0) {
              this.router.navigate(['/advance-booking-placed']);
            } else {
              this.router.navigate(['/advance-booking-failed']);
            }
          });
        },
        prefill: {
          name: this.bookingResponse.user_name,
          email: this.bookingForm.get('mail')?.value,
          contact: this.bookingForm.get('mobile')?.value,
        },
        // notes: {
        //   address: this.billingForm.get('address')?.value,
        // },
        theme: {
          color: '#F37254',
        },
        modal: {
          ondismiss: () => {
            this.ngZone.run(() => {
              this.handlePaymentFailure(this.bookingResponse.order_id);
            });
          },
        },
      };

      this.rzp = new Razorpay(options);
      this.rzp.on('payment.failed', (response: any) => {
        this.ngZone.run(() => {
          this.handlePaymentFailure(this.bookingResponse.order_id);
        });
      });
      this.rzp.open();
    } else this.toast.error('Please accept terms and conditions');
  }

  private handlePaymentFailure(orderId: string) {
    // Close the Razorpay modal if it's open
    if (this.rzp) {
      this.rzp.close();
    }

    this.router.navigateByUrl('/advance-booking-failed');
  }

  onInputChange(event: any) {
    let inputValue = event.target.value.toUpperCase();
    this.myInput.nativeElement.value = inputValue;
  }

  onKeyDown(event: KeyboardEvent) {
    let blockedKeys = ['e', '.', '-', '+', 'E'];
    if (blockedKeys.includes(event.key)) {
      event.preventDefault();
    }
  }
}
