import { apiEndPoints } from '../ApiEndPoints';
import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-my-advance-bookings',
  templateUrl: './my-advance-bookings.component.html',
  styleUrls: ['./my-advance-bookings.component.scss']
})
export class MyAdvanceBookingsComponent implements OnInit {
  data: any;

  constructor(private ApiService: ApiService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.my_advance_booking).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.data = res?.Data
      }
    })
  }

}
