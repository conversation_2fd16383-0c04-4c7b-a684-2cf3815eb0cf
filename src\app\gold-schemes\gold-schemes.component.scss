.adBanner {
  background-repeat: no-repeat;
  background-size: cover;
  height: 504px;
  position: relative;

  .content {
    max-width: 660px;
    color: #fff;
    padding: 0 30px;
    position: absolute;
    right: 210px;
    top: 50%;
    transform: translateY(-50%);

    h1 {
      color: #CCA154;
    }

    p {
      margin: 30px 0 30px;
    }

    .buttons {
      display: flex;
      flex-wrap: wrap;

      .primary-button {
        width: auto;
        margin: 0 10px 10px;
        padding: 16px 50px;

        &.white {
          color: #fff;
          border-color: #fff;
        }
      }

    }
  }

}

.howItWorks {
  padding: 100px 0 10px;

  .title {
    padding-bottom: 40px;

    h2 {
      margin-bottom: 5px;
    }
  }

  .contents {
    display: flex;
    justify-content: space-around;
  }

  .box {
    text-align: center;
    width: 256px;
    padding: 0 15px;
  }

  .step {
    font-weight: 500;
    padding: 20px 0 10px;
  }

  p {
    font-size: 14px;
  }
}

.schemePlans {
  padding-top: 80px;

  .schemes {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 -20px;
  }

  .box {
    width: 31.33%;
    margin: 11px;
    border-radius: 6px;
    padding: 20px 54px 20px 20px;
    border: 1px solid rgba(210, 171, 102, 1);
  }

  h6 {
    font-size: 18px;
    color: rgba(210, 171, 102, 1);
  }

  p {
    font-size: 13px;
    line-height: 16px;
    margin-bottom: 20px;
  }

  ul {
    font-weight: 500;
    font-size: 14px;

    li {
      line-height: 20px;
      margin-bottom: 5px;
    }

    ::marker {
      font-size: 18px;
    }

  }
}

.bannerAd {
  margin-top: 100px;

  img {
    width: 100%;
  }
}


.schemeCalculator {
  padding-top: 100px;

  .rate ng-select.filtername {
    width: 100%;
    height: 49px;
    margin: 0;
  }

  .ratecheck ng-select.filtername {
    padding-right: 80px !important;

  }

  // .rate ng-select.filtername .ng-select-container::before {
  //   display: none;
  // }

  .calculator {
    display: flex;
    flex-wrap: wrap;
  }

  .leftSide {
    width: 50%;
  }

  .rightSide {
    width: 50%;
  }

  .title {
    padding-bottom: 70px;
  }

  .rate {
    display: flex;
    width: 100%;
    position: relative;
    margin-bottom: 35px;
    border: 1px solid #D5DBDE;
    &.plan::before {
      content: "";
      position: absolute;
      right: 10px;
      top: 14px;
      background-image: url("/assets/images/angle-down.svg");
      width: 20px;
      height: 19px;
    }
  }

  .rate input[type="text"] {
    border: 1px solid #F3F3F3;
    border-radius: 6px;
  }

  .check {
    position: absolute;
    background: transparent;
    right: 8px;
    top: 3px;
    color: #CCA154;
    cursor: pointer;
    font-size: 16px;
    padding: 8px;
  }

  .sections {
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;

    .color {
      width: 16px;
      height: 16px;
      border-radius: 5px;
      margin-right: 10px;
    }

    .text {
      font-size: 16px;
      display: flex;
      align-items: center;
    }

    .head {
      font-size: 14px;
    }

    .get {
      font-weight: 600;
    }

    &.total {
      padding-top: 10px;
    }
  }

  .rateBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    margin-top: 60px;
    justify-content: center;

    .box {
      width: 40%;
      border: 1px solid #D9D9D9;
      border-radius: 12px;
      overflow: hidden;
      padding-bottom: 20px;
      margin: 0 15px;
    }

    .heading {
      background: #D2AB66;
      padding: 20px;
      font-size: 16px;
      color: #fff;
    }

    .sections {
      margin: 20px 20px 0;
      padding-bottom: 0;

      &.total {
        border-top: 1px solid #EFEFEF;
      }
    }
  }
}

.helpiline {
  margin-top: 100px;
  background: #D2AB66;
  text-align: center;
  padding: 15px 0;
  font-size: 25px;
  color: #fff;


  .content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }

  a {
    display: flex;
    align-items: center;
    color: #fff;
    margin-left: 30px;

    img {
      padding-right: 12px;
    }
  }
}

.justDo {
  background: #FFFBF3;
  padding: 100px 0;
  margin-top: 100px;

  .section {
    margin-bottom: 50px;

    p {
      margin-bottom: 20px;
    }

    ul li{
      margin-bottom: 13px;
    }

    a {
      color: #CCA154;
      text-decoration: underline;
    }
  }

  .howTo {
    background: #D2AB66;
    padding: 40px 50px;
    ::ng-deep{
      h4 {
        color: #fff;
      }
      a {
        color: #fff;
        font-size: 22px;
      }
  
      span{
        text-decoration: underline;
      }
    }
    
  }

}

.faqSection {
  padding: 100px 0 100px;
  overflow: hidden;
  position: relative;

  .faq {
    padding: 0 0 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .circleBlue {
    position: absolute;
    left: -50px;
    top: 30px;
    z-index: -1;
  }

  .dots {
    position: absolute;
    right: 0;
    top: 100px;
  }

  // accordion
  button.faq-accordion {
    cursor: pointer;
    padding: 20px 70px 20px 20px;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    transition: 0.4s;
    background-color: transparent;
    font-size: 16px;
    position: relative;
    font-weight: 600;
  }

  button.faq-accordion::before {
    content: "";
    position: absolute;
    right: 20px;
    top: 0;
  }

  /*button not active*/
  button.faq-accordion:after {
    content: "";
    background-image: url(/assets/images/plus.svg);
    background-repeat: no-repeat;
    background-size: contain;
    width: 30px;
    height: 30px;
    position: absolute;
    right: 20px;
    top: 16px;
  }

  /* minus button */
  button.faq-accordion.active:after {
    background-image: url(/assets/images/minus.svg);
  }

  .option {
    display: block;
    padding-bottom: 15px;
  }

  .course-panel {
    padding: 0px 18px 0 0px;
    background-color: transparent;
    max-height: 0;
    overflow: hidden;
    width: 0;
    line-height: 1.6em;
    letter-spacing: 0.4px;
    font-weight: 400;
    font-style: normal;

    p {
      padding: 0 50px 30px 20px;
      font-size: 14px;
    }

    &.open-accordion {
      max-height: max-content;
      width: 100%;
    }
  }

  .drops {
    margin-bottom: 20px;
    border: 1px solid #DDDDDD;
    border-radius: 10px;
  }

  .drops:last-child .course-panel {
    border: none;
  }

  .secondSec {
    display: none;
    border-top: 1px solid #14264B26;
  }

  .secondSec.show {
    display: block;
  }

  .viewMore {
    width: 280px;
    margin: 40px auto 20px;
    display: block;

    img {
      transform: rotate(90deg);
      margin-left: 10px;
    }
  }

  .viewMore.hide {
    display: none;
  }
}




@media screen and (max-width:992px) {
  .adBanner {
    .content {
      max-width: 100%;
      right: 25px;

      .buttons .primary-button {
        padding: 12px 10px;
      }
    }

  }

  .howItWorks {
    padding: 50px 0 10px;

    .title {
      padding-bottom: 25px;
    }

  }

  .schemePlans {
    padding-top: 50px;

    .box {
      width: 46%;
    }

  }

  .bannerAd {
    margin-top: 50px;
  }

  .schemeCalculator {
    padding-top: 50px;
  }

  .justDo {
    padding: 50px 0;
    margin-top: 50px;

    .section {
      margin-bottom: 30px;

      p {
        margin-bottom: 10px;
      }
      ul li{
        margin-bottom: 10px;
      }
    }
    .howTo {
      padding: 30px;

      a {
        font-size: 16px;
      }
    }
  }





}

@media screen and (max-width:640px) {
  .adBanner {
    .content {
      p {
        margin: 15px 0 15px;
      }

      .buttons {
        flex-wrap: wrap;

        .primary-button {
          margin: 0 0px 10px;
        }
      }

    }

  }

  .schemePlans {
    .box {
      width: 100%;
      margin: 5px 0;
    }

    .schemes {
      margin: 0;
    }

  }

  .schemeCalculator {
    .title {
      padding-bottom: 20px;
    }

    .leftSide {
      width: 100%;
    }

    .rightSide {
      width: 100%;
      margin-top: 20px;
      .selectOption {

        span {
          padding-right: 5px;
        }

        .selectMenu {
          font-size: 14px;
          color: #000;
          position: relative;
          padding-right: 25px;

          &.sort {
            &::before {
              content: "";
              background-image: url(/assets/images/angle-down.svg);
              position: absolute;
              right: 0px;
              top: 3px;
              width: 20px;
              height: 20px;
            }
          }
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          position: absolute;
          opacity: 0;
          box-shadow: 0px 2px 2px #D5DBDE;
          background: #fff;
          width: 100%;
          left: 0px;
          top: 49px;
          z-index: -1;

          &.active {
            z-index: 3;
            opacity: 1;
            transition: all linear .2s;
            z-index: 1;
          }

          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: -12px;
            width: 100%;
            height: 20px;

          }

          li {
            padding: 0px 15px;

            &:hover {
              color: #D2AB66;
            }
          }
        }

      }

    }

    .rateBox {
      margin-top: 40px;

      .box {
        width: 100%;
        margin: 10px 0;
      }

      .heading {
        padding: 15px;
        font-size: 14px;
      }

      .sections {
        margin: 15px 15px 0;
      }
    }

  }

  .faqSection {
    padding: 50px 0 20px;
  }

  .howItWorks .contents {
    flex-wrap: wrap;

    .box {
      width: 100%;
      padding: 15px 0;
    }
  }
}
