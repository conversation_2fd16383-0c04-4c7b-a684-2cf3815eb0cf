<!-- <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="square-jelly-box" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner>

<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div> -->

<div class="myAccount">
  <div class="container">
    <div class="title">
      <h1>My Account</h1>
    </div>
    <div class="contentSection">
      <div class="sideMenu">
        <app-sidebar></app-sidebar>
      </div>
      <div class="contents">
        <div class="info ">
          <ng-container *ngIf="isProfile">
            <div class="head">Personal Information</div>
            <div class="box">
              <div class="detail">
                <div class="pInfo">
                  <div class="field">
                    <div class="text">First Name</div>
                    <div class="name">{{profileData?.name}}</div>
                  </div>
                  <div class="field">
                    <div class="text">Phone Number</div>
                    <div class="name">{{profileData?.country_code}} {{profileData?.mobile}}</div>
                  </div>
                </div>
                <div class="pInfo">
                  <div class="field">
                    <div class="text">Email</div>
                    <div class="name">{{profileData?.email}}</div>
                  </div>
                  <div class="field">
                    <div class="text">Gender</div>
                    <div class="name">{{profileData?.gender}}</div>
                  </div>
                </div>
                <div class="pInfo">
                  <div class="field">
                    <div class="text">Date of Birth </div>
                    <div class="name">{{profileData?.dob}}</div>
                  </div>
                  <div class="field">
                    <div class="text">Anniversary Date</div>
                    <div class="name">{{profileData?.anniversary_date}}</div>
                  </div>
                </div>
                <div class="pInfo">
                  <div class="field">
                    <div class="text">GST Number </div>
                    <div class="name">{{profileData?.gst_no}}</div>
                  </div>
                </div>
              </div>
              <div class="edit" (click)="showProfile()"> <img src="\assets\images\my-account\edit.svg" alt="edit icon"
                  style="padding-right: 4px;">Edit</div>
            </div>
          </ng-container>

          <ng-container *ngIf="isAddress">
            <div class="head">Delivery Address <div (click)="showAddAdress()" class="addNew"> <img
                  src="\assets\images\my-account\add.svg" alt="add icon" style="padding-right: 4px;"> Add New</div>
            </div>
            <ng-container *ngFor="let address of addressData">
              <div class="box">
                <div class="address">
                  <input type="radio" id="{{address.id}}" name="radio-group" [checked]="address?.is_default == 1"
                    (change)="setDefaultAddress(address.id)">
                  <label for="{{address.id}}" class="name">{{address?.name}}</label>
                  <!-- <div class="name">{{address?.name}}</div> -->
                  <div>{{address?.address}}</div>
                  <!-- <div>Vengoor</div> -->
                  <div>{{address?.city}}-{{address?.pincode}}</div>
                  <div>{{address?.state}}, {{address?.country}} </div>
                  <div>Mobile Number : {{address?.mobile}}</div>
                </div>
                <div class="edit" (click)="showEditAdress(address?.id)"> <img src="\assets\images\my-account\edit.svg"
                    alt="edit icon" style="padding-right: 4px;">Edit Address </div>
                <div class="delete" *ngIf="address?.is_default != 1" (click)="popupConfirm(address?.id)"> <img src="\assets\images\my-account\delete.svg"
                    alt="delete icon"></div>
              </div>
            </ng-container>
          </ng-container>
        </div>

        <ng-container *ngIf="isEditProfile">
          <div class="editPersonalInfo ">
            <div class="head">Personal Information</div>
            <div class="box">
              <div class="form">
                <form [formGroup]="profileForm">
                  <div class="twoSection">
                    <div class="field">
                      <span>Full Name</span>
                      <input type="text" formControlName="fname" />
                      <span *ngIf="pf.fname.invalid && pf.fname.touched">
                        <span class="text-danger" *ngIf="pf.fname.errors?.required"> Name is required </span>
                      </span>
                    </div>
                    <div class="field">
                      <span>Phone Number</span>
                      <input type="tel" formControlName="mobile" />
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field">
                      <span>Email</span>
                      <input type="email" formControlName="email" />
                      <span *ngIf="pf.email.invalid && pf.email.touched">
                        <span class="text-danger" *ngIf="pf.email.errors?.required"> Email is required </span>
                      </span>
                      <span class="text-danger" *ngIf="pf.email.errors?.pattern">Please Enter Valid Email </span>
                    </div>
                    <div class="field gender">
                      <span>Gender</span>
                      <div class="radioBtn">
                        <span class="radio">
                          <input id="radio-1" name="gender" type="radio" formControlName="gender" value="male">
                          <label for="radio-1" class="radio-label">Male</label>
                        </span>
                        <span class="radio">
                          <input id="radio-2" name="gender" type="radio" formControlName="gender" value="female">
                          <label for="radio-2" class="radio-label">Female</label>
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field form">
                      <span>Date of Birth </span>
                      <input type="date" formControlName="dob">
                      <!-- <ngx-datepicker formControlName="dob" [options]="options"></ngx-datepicker> -->
                    </div>
                    <div class="field form">
                      <span>Anniversary Date</span>
                      <input type="date" formControlName="anniversary">
                      <!-- <ngx-datepicker formControlName="anniversary" [options]="options"></ngx-datepicker> -->
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field">
                      <span>GST Number </span>
                      <input type="tel" formControlName="gst" />
                      <span class="text-danger" *ngIf="pf.gst.errors?.pattern">Please Enter Valid GST Number </span>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
          <div class="buttons">
            <div class="cancel primary-button white" (click)="cancel('profile')">Cancel</div>
            <div class="save primary-button golden" (click)=" updateProfile()">Save</div>
          </div>
        </ng-container>

        <ng-container *ngIf="addAddress">
          <div class="editPersonalInfo ">
            <form [formGroup]="addressForm">
              <div class="head">Add Delivery Address</div>
              <div class="box">
                <div class="form">
                  <div class="twoSection">
                    <div class="field">
                      <span>Full Name</span>
                      <input type="text" formControlName="name" />
                      <span *ngIf="af.name.invalid && af.name.touched">
                        <span class="text-danger" *ngIf="af.name.errors?.required"> Name is required </span>
                      </span>
                    </div>
                    <div class="field">
                      <span>country</span>
                      <select name="" id="" formControlName="country" (change)="selectedCountry($event,'add')">
                        <option [value]="country.id" *ngFor="let country of countries">
                          {{country.name}}
                        </option>
                      </select>
                      <span *ngIf="af.country.invalid && af.country.touched">
                        <span class="text-danger" *ngIf="af.country.errors?.required"> Country is required </span>
                      </span>
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field">
                      <span>pincode</span>
                      <input type="text" formControlName="pincode" (change)="checkPincode()" />
                      <span *ngIf="af.pincode.invalid && af.pincode.touched">
                        <span class="text-danger" *ngIf="af.pincode.errors?.required"> Pincode is required </span>
                      </span>
                      <span class="text-danger" *ngIf="af.pincode.errors?.pattern">Please Enter Valid Pincode </span>
                    </div>
                    <div class="field">
                      <span>state</span>
                      <select name="" id="" formControlName="state">
                        <option [value]="state.id" *ngFor="let state of states">
                          {{state.name}}
                        </option>
                      </select>
                      <span *ngIf="af.state.invalid && af.state.touched">
                        <span class="text-danger" *ngIf="af.state.errors?.required"> State is required </span>
                      </span>
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field">
                      <div style="position: relative;">
                        <span>city</span>
                        <input type="text" formControlName="city" />
                        <span *ngIf="af.city.invalid && af.city.touched">
                          <span class="text-danger" *ngIf="af.city.errors?.required"> City is required </span>
                        </span>
                      </div>
                      <div style="position: relative; height: 99px;">
                        <span>Mobile Number</span>
                        <div class="country">
                          <select name="" id="" formControlName="code">
                            <option [value]="country.phonecode" *ngFor="let country of countries">
                              +{{country.phonecode}}
                            </option>
                          </select>

                          <input type="tel" formControlName="mobile" />
                        </div>
                        <span *ngIf="af.mobile.invalid && af.mobile.touched">
                          <span class="text-danger" *ngIf="af.mobile.errors?.required"> Mobile Number is required
                          </span>
                        </span>
                        <span class="text-danger" *ngIf="af.mobile.errors?.pattern">Please Enter Valid Mobile Number
                        </span>
                      </div>
                    </div>
                    <div class="field">
                      <span>Address</span>
                      <textarea name="" id="" cols="30" rows="6" formControlName="address"
                        style="height: 146px;"></textarea>
                      <span *ngIf="af.address.invalid && af.address.touched">
                        <span class="text-danger" *ngIf="af.address.errors?.required"> Address is required </span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="buttons">
            <div class="cancel primary-button white" (click)="cancel('address')">Cancel</div>
            <div class="save primary-button golden" (click)="submitAddress()">Save</div>
          </div>
        </ng-container>

        <ng-container *ngIf="editAddress">
          <div class="editPersonalInfo">
            <form [formGroup]="editAddressForm">
              <div class="head">Edit Delivery Address</div>
              <div class="box">
                <div class="form">
                  <div class="twoSection">
                    <div class="field">
                      <span>Full Name</span>
                      <input type="text" formControlName="name" />
                      <span *ngIf="eaf.name.invalid && eaf.name.touched">
                        <span class="text-danger" *ngIf="eaf.name.errors?.required"> Name is required </span>
                      </span>
                    </div>
                    <div class="field">
                      <span>country</span>
                      <select name="" id="" formControlName="country" (change)="selectedCountry($event,'add')">
                        <option [value]="country.id" *ngFor="let country of countries">
                          {{country.name}}
                        </option>
                      </select>
                      <span *ngIf="eaf.country.invalid && eaf.country.touched">
                        <span class="text-danger" *ngIf="eaf.country.errors?.required"> Country is required </span>
                      </span>
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field">
                      <span>pincode</span>
                      <input type="text" formControlName="pincode" />
                      <span *ngIf="eaf.pincode.invalid && eaf.pincode.touched">
                        <span class="text-danger" *ngIf="eaf.pincode.errors?.required"> Pincode is required </span>
                      </span>
                      <span class="text-danger" *ngIf="eaf.pincode.errors?.pattern">Please Enter Valid Pincode </span>
                    </div>
                    <div class="field">
                      <span>state</span>
                      <select name="" id="" formControlName="state">
                        <option [value]="state.id" *ngFor="let state of states">
                          {{state.name}}
                        </option>
                      </select>
                      <span *ngIf="eaf.state.invalid && eaf.state.touched">
                        <span class="text-danger" *ngIf="eaf.state.errors?.required"> state is required </span>
                      </span>
                    </div>
                  </div>

                  <div class="twoSection">
                    <div class="field">
                      <div style="position: relative;">
                        <span>city</span>
                        <input type="text" formControlName="city" />
                        <span *ngIf="eaf.city.invalid && eaf.city.touched">
                          <span class="text-danger" *ngIf="eaf.city.errors?.required"> City is required </span>
                        </span>
                      </div>
                      <div style="position: relative; height: 99px;">
                        <span>Mobile Number</span>
                        <div class="country">
                          <select name="" id="" formControlName="code">
                            <option [value]="country.phonecode" *ngFor="let country of countries">
                              +{{country.phonecode}}
                            </option>
                          </select>
                          <input type="tel" formControlName="mobile" />
                        </div>
                        <span *ngIf="eaf.mobile.invalid && eaf.mobile.touched">
                          <span class="text-danger" *ngIf="eaf.mobile.errors?.required"> Mobile Number is required
                          </span>
                        </span>
                        <span class="text-danger" *ngIf="eaf.mobile.errors?.pattern">Please Enter Valid Mobile Number
                        </span>
                      </div>
                    </div>
                    <div class="field">
                      <span>Address</span>
                      <textarea name="" id="" cols="30" rows="6" formControlName="address"
                        style="height: 146px;"></textarea>
                      <span *ngIf="eaf.address.invalid && eaf.address.touched">
                        <span class="text-danger" *ngIf="eaf.address.errors?.required"> Address is required </span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
          <div class="buttons">
            <div class="cancel primary-button white" (click)="cancel('editAddress')">Cancel</div>
            <div class="save primary-button golden" (click)="updateAddress()">Save</div>
          </div>
        </ng-container>
      </div>
    </div>
  </div>
</div>

<div class="referPopup" [ngClass]="confirmPopup?'show':'hide'">
  <div class="box">
    <div class="close" (click)="confirmPopup = false"><img src="\assets\images\close-white.svg" alt="close icon"></div>
    <div><p>Are you sure?</p></div>
    <div class="button">
      <div class="primary-button white" (click)="confirmPopup = false">No</div>
      <div class="primary-button golden" (click)="deleteAddres()">Yes</div>
    </div>
  </div>
</div>
