import {
  Component,
  ElementRef,
  <PERSON><PERSON><PERSON>,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { apiEndPoints } from '../ApiEndPoints';
import { ToastrService } from 'ngx-toastr';
import { ApiService } from './../Services/api.service';
import { environment } from 'src/environments/environment.prod';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
declare var Razorpay: any;

@Component({
  selector: 'app-order-now-detail',
  templateUrl: './order-now-detail.component.html',
  styleUrls: ['./order-now-detail.component.scss'],
})
export class OrderNowDetailComponent implements OnInit {
  @ViewChild('form') form!: ElementRef;
  order_id: any;
  data: any;
  deliveryAddress: any;
  billingAddress: any;
  productdata: any;
  imageBase: any;
  isCancelled: boolean = false;

  deliveryShow: any = true;
  detailShow: any = false;
  editShow: any = false;
  billingForm!: FormGroup;
  addressForm!: FormGroup;
  editAddressForm!: FormGroup;
  countries: any;
  states: any;
  addressData: any;
  editAddressData: any;
  selectedAddress: any;
  defaultAddressId: any;
  cartData: any;
  sameAsDelivery: boolean = false;
  isAddress: boolean = false;
  OrderStatus: boolean = true;
  encRequest: any;
  accessCode: any;
  ccAvenueUrl: string = environment.ccAvenueProduction;
  ccAvenueBeta: string = environment.ccAvenueStaging;
  checkDomain: boolean = false;
  isLoading: boolean = false;
  private rzp: any;
  windowRef: any = window;

  constructor(
    private route: ActivatedRoute,
    private ApiService: ApiService,
    private toast: ToastrService,
    private formbuilder: FormBuilder,
    private router: Router,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    this.checkDomain = window.location.hostname.includes('beta');
    this.route.params.subscribe((params) => {
      this.order_id = params['id'];
      this.getOrderDetail();
    });
    this.initBillingForm();
    this.initAddressForm();
    this.initEditAddressForm();
    this.getCountries();
    this.getAddress();

    this.imageBase = environment.imageBase;
  }

  getOrderDetail() {
    this.ApiService.postData(apiEndPoints.order_now_details, {
      ordernow_id: this.order_id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.data = res?.Data;
        this.deliveryAddress = this.data?.delivery_address;
        this.billingAddress = this.data?.billing_address;
        this.productdata = this.data?.order_data;
        if (
          this.productdata?.status?.slug == 'work_completed' &&
          this.data?.address_available == 0
        ) {
          this.toast.warning('Add Address');
          this.OrderStatus = false;
          this.isAddress = true;
        }
        //Webengage Start
        if (
          this.data?.statuses.find((x: any) => x.name == 'Delivered')
            .is_active == 1
        ) {
          this.windowRef.webengage.track('Order Delivered', {
            'Order ID': this.productdata?.Order_id,
            'Order Amount': Number(this.productdata?.price),
            'Product Details': [
              {
                'Product ID': this.productdata?.slug,
                'Product Name': this.productdata?.name,
                Quantity: 1,
                MRP: Number(this.productdata?.price),
              },
            ],
          });
        }
        //Webengage End
      }
    });
  }

  cancelOrder() {
    this.isCancelled = true;
    this.ApiService.postData(apiEndPoints.cancel_order, {
      order_product_id: this.productdata?.order_product_id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.toast.success(res?.Message);
        this.getOrderDetail();
      } else this.toast.error(res?.Message);
    });
  }

  returnOrder() {
    this.isCancelled = true;
    this.ApiService.postData(apiEndPoints.return_order, {
      order_product_id: this.productdata?.order_product_id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.toast.success(res?.Message);
        this.getOrderDetail();
      } else this.toast.error(res?.Message);
    });
  }

  initBillingForm() {
    this.billingForm = this.formbuilder.group({
      name: ['', [Validators.required]],
      country: ['', [Validators.required]],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      state: ['', [Validators.required]],
      city: ['', Validators.required],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      code: ['91'],
      address: ['', [Validators.required]],
      gst: [''],
    });
  }

  get bf() {
    return this.billingForm.controls;
  }

  initAddressForm() {
    this.addressForm = this.formbuilder.group({
      name: ['', [Validators.required]],
      country: ['', [Validators.required]],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      state: [{ value: '', disabled: true }, [Validators.required]],
      city: ['', [Validators.required]],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      code: ['91'],
      address: ['', Validators.required],
    });
  }

  get af() {
    return this.addressForm.controls;
  }

  initEditAddressForm() {
    this.editAddressForm = this.formbuilder.group({
      name: [''],
      country: [''],
      pincode: [''],
      state: [],
      city: [''],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      code: [''],
      address: [''],
    });
  }

  get eaf() {
    return this.editAddressForm.controls;
  }

  getCountries() {
    this.ApiService.getData(apiEndPoints.COUNTRY).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.countries = res?.Data;
      }
    });
  }

  selectedCountry(event: any, type: any) {
    const countryId = event.target.value;
    let data = { countryId: countryId };
    this.ApiService.postData(apiEndPoints.STATE, data).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.states = res?.Data;
        if (type == 'add') this.addressForm?.get('state')?.enable();
      }
    });
  }

  getAddress() {
    this.ApiService.getData(apiEndPoints.GET_ADDRESS).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.addressData = res?.Data;
        const filteredData = this.addressData.filter(
          (obj: any) => obj.is_default == 1
        );
        this.defaultAddressId = filteredData[0]['id'];
      }
    });
  }

  submitAddress() {
    if (this.addressForm.invalid) {
      this.addressForm.markAllAsTouched();
      return;
    }
    let data = {
      name: this.addressForm.get('name')?.value,
      countrycode: this.addressForm.get('code')?.value,
      mobile: this.addressForm.get('mobile')?.value,
      country_id: this.addressForm.get('country')?.value,
      state_id: this.addressForm.get('state')?.value,
      city: this.addressForm.get('city')?.value,
      pincode: this.addressForm.get('pincode')?.value,
      address: this.addressForm.get('address')?.value,
    };

    this.ApiService.postData(apiEndPoints.ADD_ADDRESS, data).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address added');

          //Webengage Start
          let country = this.countries.find(
            (x: any) => x.id == data.country_id
          );
          let state = this.states.find((x: any) => x.id == data.state_id);
          this.windowRef.webengage.track('Delivery Address Added', {
            'Full Name': this.addressForm.get('name')?.value,
            country: country?.name,
            pincode: this.addressForm.get('pincode')?.value,
            state: state?.name,
            city: this.addressForm.get('city')?.value,
            Address: this.addressForm.get('address')?.value,
            'Mobile Number': this.addressForm.get('mobile')?.value,
          });
          //Webengage End

          window.location.reload();
        } else this.toast.error('failed');
      }
    );
  }

  deleteAddres(id: any) {
    let data = { addressId: id };
    this.ApiService.postData(apiEndPoints.DELETE_ADDRESS, data).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address Deleted');
          this.getAddress();
        } else this.toast.error('failed');
      }
    );
  }

  setDefaultAddress(value: any) {
    let data = { addressId: value };
    this.ApiService.postData(apiEndPoints.DEFAULT_ADDRESS, data).subscribe(
      (res: any) => {
        if (res.ErrorCode == 0) {
          this.getAddress();

          //Webengage Start
          let address = this.addressData.find((x: any) => x.id == value);

          this.windowRef.webengage.track('Delivery Address Selected', {
            'Full Name': address?.name,
            country: address?.country,
            pincode: address?.pincode,
            state: address?.state,
            city: address?.city,
            Address: address?.address,
            'Mobile Number': address?.mobile,
          });
          //Webengage End

          setTimeout(() => {
            this.setBillingAddress();
          }, 500);
        }
      }
    );
  }

  setBillingAddress() {
    if (this.sameAsDelivery) {
      let data = { addressId: this.defaultAddressId };
      this.ApiService.postData(apiEndPoints.GET_SINGLE_ADDRESS, data).subscribe(
        (res: any) => {
          if (res?.ErrorCode == 0) {
            const setBillingData = res?.Data;
            let countryid = { countryId: setBillingData?.country_id };
            this.ApiService.postData(apiEndPoints.STATE, countryid).subscribe(
              (res: any) => {
                if (res?.ErrorCode == 0) {
                  this.states = res?.Data;
                }
              }
            );
            this.billingForm.get('name')?.setValue(setBillingData?.name),
              this.billingForm
                .get('code')
                ?.setValue(setBillingData?.countrycode),
              this.billingForm.get('mobile')?.setValue(setBillingData?.mobile),
              this.billingForm
                .get('country')
                ?.setValue(setBillingData?.country_id),
              this.billingForm.get('state')?.setValue(setBillingData?.state_id),
              this.billingForm.get('city')?.setValue(setBillingData?.city),
              this.billingForm
                .get('pincode')
                ?.setValue(setBillingData?.pincode),
              this.billingForm
                .get('address')
                ?.setValue(setBillingData?.address);
          }
        }
      );
    } else {
      this.billingForm.reset();
    }
  }

  addBillingDetails() {
    if (this.billingForm.invalid) {
      this.billingForm.markAllAsTouched();
      return;
    }
    let data = {
      name: this.billingForm.get('name')?.value,
      countrycode: this.billingForm.get('code')?.value,
      mobile: this.billingForm.get('mobile')?.value,
      country_id: this.billingForm.get('country')?.value,
      state_id: this.billingForm.get('state')?.value,
      city: this.billingForm.get('city')?.value,
      pincode: this.billingForm.get('pincode')?.value,
      address: this.billingForm.get('address')?.value,
      gst: this.billingForm.get('name')?.value,
    };
    this.ApiService.postData(apiEndPoints.ADD_BILLING, data).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Billing Address Added');
          this.isAddress = false;
          this.OrderStatus = true;
        } else this.toast.warning('Fill out the details');
      }
    );
  }

  add() {
    this.detailShow = !this.detailShow;
    this.deliveryShow = false;
  }

  editAddress(id: any) {
    this.editShow = !this.editShow;
    this.deliveryShow = false;

    let data = { addressId: id };
    this.ApiService.postData(apiEndPoints.GET_SINGLE_ADDRESS, data).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.editAddressData = res?.Data;
          let countryid = { countryId: this.editAddressData?.country_id };
          this.ApiService.postData(apiEndPoints.STATE, countryid).subscribe(
            (res: any) => {
              if (res?.ErrorCode == 0) {
                this.states = res?.Data;
              }
            }
          );
          this.editAddressForm
            .get('name')
            ?.setValue(this.editAddressData?.name),
            this.editAddressForm
              .get('code')
              ?.setValue(this.editAddressData?.countrycode),
            this.editAddressForm
              .get('mobile')
              ?.setValue(this.editAddressData?.mobile),
            this.editAddressForm
              .get('country')
              ?.setValue(this.editAddressData?.country_id),
            this.editAddressForm
              .get('state')
              ?.setValue(this.editAddressData?.state_id),
            this.editAddressForm
              .get('city')
              ?.setValue(this.editAddressData?.city),
            this.editAddressForm
              .get('pincode')
              ?.setValue(this.editAddressData?.pincode),
            this.editAddressForm
              .get('address')
              ?.setValue(this.editAddressData?.address);
        }
      }
    );
  }

  updateAddress() {
    if (this.editAddressForm.invalid) {
      return;
    }
    let data = {
      addressId: this.editAddressData?.id,
      name: this.editAddressForm.get('name')?.value,
      countrycode: this.editAddressForm.get('code')?.value,
      mobile: this.editAddressForm.get('mobile')?.value,
      country_id: this.editAddressForm.get('country')?.value,
      state_id: this.editAddressForm.get('state')?.value,
      city: this.editAddressForm.get('city')?.value,
      pincode: this.editAddressForm.get('pincode')?.value,
      address: this.editAddressForm.get('address')?.value,
    };
    this.ApiService.postData(apiEndPoints.EDIT_ADDRESS, data).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address updated');
          window.location.reload();
        } else this.toast.error('failed');
      }
    );
  }

  checkPincode() {
    let pincode = this.addressForm.get('pincode')?.value;
    this.ApiService.postData(apiEndPoints.DELIVERY_CHECK, {
      pincode: pincode,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        // this.deliveryStatus = res?.Data
      } else this.toast.error();
    });
  }

  payBalance() {
    if (this.data?.address_available == 1) {
      this.ApiService.postData(apiEndPoints.order_now_pay_balance, {
        ordernow_id: this.order_id,
      }).subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          const data = res?.Data.data;
          // this.accessCode = res?.Data?.accesscode;
          // this.encRequest = res?.Data?.encrypted_data;
          // setTimeout(() => {
          //   this.form.nativeElement.submit();
          // }, 500);
          const options = {
            key: data.api_key,
            order_id: data.razorpay_order_id,
            amount: data.amount, // amount in paise
            currency: 'INR',
            name: 'TT Devassy',
            description: 'Payment for order',
            handler: (response: any) => {
              this.isLoading = true;
              // Handle success
              this.ApiService.postData('ordernow-balance-payment', {
                order_id: data.order_id,
                ...response,
              }).subscribe((res: any) => {
                this.isLoading = false;
                if (res?.ErrorCode == 0) {
                  this.router.navigate(['/order-now-success']);
                } else {
                  this.router.navigate(['/order-now-failed']);
                }
              });
            },
            prefill: {
              name: this.billingForm.get('name')?.value,
              email: null,
              contact: this.billingForm.get('mobile')?.value,
            },
            notes: {
              address: this.billingForm.get('address')?.value,
            },
            theme: {
              color: '#F37254',
            },
            modal: {
              ondismiss: () => {
                this.ngZone.run(() => {
                  this.handlePaymentFailure(data.order_id);
                });
              },
            },
          };

          this.rzp = new Razorpay(options);
          this.rzp.on('payment.failed', (response: any) => {
            this.ngZone.run(() => {
              this.handlePaymentFailure(data.order_id);
            });
          });
          this.rzp.open();
        } else this.toast.error(res?.Message);
      });
    }
  }

  private handlePaymentFailure(orderId: string) {
    // Close the Razorpay modal if it's open
    if (this.rzp) {
      this.rzp.close();
    }

    this.router.navigateByUrl('/order-now-failed');
  }
}
