import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';

@Component({
  selector: 'app-privacy-policy',
  templateUrl: './privacy-policy.component.html',
  styleUrls: ['./privacy-policy.component.scss']
})
export class PrivacyPolicyComponent implements OnInit {
  data: any;

  constructor(private ApiService: ApiService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.PRIVACY_POLICY).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.data = res?.data
      }
    })
  }

}
