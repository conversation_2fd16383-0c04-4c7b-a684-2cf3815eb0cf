.contactUs {
  display: flex;
  margin-top: 40px;
  padding-bottom: 100px;

  @media screen and (max-width: 992px) {
    flex-direction: column;
    padding-bottom: 50px;
    margin-top: 20px;
  }

  .weHelp {
    width: 40%;

    @media screen and (max-width: 992px) {
      width: 100%;
    }

    .rightSide {
      padding: 40px;
      position: relative;
      background-color: #fff;
      border: 1px solid #bbbbbb;

      @media screen and (max-width: 992px) {
        padding: 20px;
      }

      .head {
        font-size: 20px;
        padding-bottom: 30px;
        font-family: "Libre Bodoni";

        @media screen and (max-width: 768px) {
          font-size: 18px;
          padding-bottom: 20px;
        }
      }

      p {
        text-align: center;
        font-size: 14px;
        margin-bottom: 15px;

        @media screen and (max-width: 768px) {
          font-size: 12px;
        }
      }

      .field {
        width: 100%;
        padding-bottom: 15px;

        .text-danger {
          bottom: 0;
        }

        div {
          padding-bottom: 10px;
          font-size: 14px;
          color: #787878;
          font-weight: 500;
        }

        input[type="text"],
        input[type="email"],
        input[type="tel"],
        textarea {
          width: 100%;
          padding: 10px;
          border: 1px solid #d5dbde;
          border-radius: 4px;

          @media screen and (max-width: 768px) {
            padding: 8px;
          }
        }

        input[type="submit"] {
          width: 100%;
          padding: 12px;
          background-color: #000000;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;

          &:hover {
            background-color: darken(#cb9f52, 10%);
          }
        }
      }

      .check {
        display: flex;
        align-items: flex-start;
        margin: 20px 0;

        @media screen and (max-width: 992px) {
          margin: 15px 0;
        }

        input[type="checkbox"] {
          margin-top: 3px;
          margin-right: 10px;
        }

        span {
          font-size: 13px;
          line-height: 1.4;

          @media screen and (max-width: 768px) {
            font-size: 12px;
          }
        }
      }
    }

    .close {
      position: absolute;
      right: -24px;
      top: -8px;
      cursor: pointer;

      @media screen and (max-width: 992px) {
        right: 10px;
        top: 10px;
      }
    }
  }

  .contact {
    width: 60%;
    padding-left: 50px;

    @media screen and (max-width: 992px) {
      width: 100%;
      padding-left: 0;
      padding-top: 30px;
    }

    .tt {
      padding-bottom: 40px;

      @media screen and (max-width: 992px) {
        padding-bottom: 20px;
      }

      img {
        width: 100%;
        height: auto;
      }
    }

    .contacts {
      display: flex;
      padding-bottom: 60px;
      flex-wrap: wrap;

      @media screen and (max-width: 992px) {
        padding-bottom: 30px;
      }
    }

    .section {
      width: 30%;
      margin-right: 10%;

      @media screen and (max-width: 992px) {
        width: 100%;
        margin-right: 0;
        margin-bottom: 20px;
      }
    }

    .location {
      font-size: 20px;
      padding-bottom: 10px;
      font-weight: 500;

      @media screen and (max-width: 992px) {
        font-size: 18px;
        padding-bottom: 5px;
      }
    }

    p {
      font-size: 16px;
      line-height: 1.5;

      @media screen and (max-width: 992px) {
        font-size: 14px;
      }
    }

    .map {
      width: 100%;

      iframe {
        width: 100%;
        height: 450px;

        @media screen and (max-width: 768px) {
          height: 300px;
        }
      }
    }
  }
}
