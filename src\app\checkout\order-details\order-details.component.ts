import { ApiService } from 'src/app/Services/api.service';
import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { environment } from 'src/environments/environment.prod';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';

@Component({
  selector: 'app-order-details',
  templateUrl: './order-details.component.html',
  styleUrls: ['./order-details.component.scss']
})
export class OrderDetailsComponent implements OnInit {
  @ViewChild('form') form!: ElementRef;
  cartData: any;
  imageBase: any;
  isPromoCodeApplied: any = false
  isSchemeBalanceApplied: any = false
  isWeightBalanceApplied: any = false
  isMainWalletApplied: any = false
  isRewardPointApplied: any = false
  selectedScheme: any = ''
  selectedMainWallet: any
  redeem_points: any;
  selectedRewardPoints: any
  isDisabled: boolean = true
  paymentId: any;
  orderId: any;
  encRequest: any
  accessCode: any
  promocode: any;
  instruction: any;
  isLoading: boolean = false
  ccAvenueUrl:string=environment.ccAvenueProduction
  ccAvenueBeta:string=environment.ccAvenueStaging
  checkDomain:boolean=false
  constructor(
    private apiService: ApiService, private toast: ToastrService, private router: Router
  ) { }

  ngOnInit(): void {
    this.checkDomain=window.location.hostname.includes('beta')
    if (localStorage.getItem('cashScheme')) {
      this.selectedScheme = 'cash';
      this.isSchemeBalanceApplied = true
    }
    else if (localStorage.getItem('weigtScheme')) {
      this.selectedScheme = 'weight';
      this.isWeightBalanceApplied = true
    } else {
      this.isSchemeBalanceApplied = false
      this.isWeightBalanceApplied = false
    }
    if (localStorage.getItem('mainWallet')) {
      this.selectedMainWallet = true;
      this.isMainWalletApplied = true
    } else this.isMainWalletApplied = false
    if (localStorage.getItem('rewardPoints')) {
      this.selectedRewardPoints = true;
      this.isRewardPointApplied = true
      this.redeem_points = localStorage.getItem('redeemPoints')
    }
    if (localStorage.getItem('promoCode')) {
      this.isPromoCodeApplied = true
      this.promocode = localStorage.getItem('promoCode')
    }
    if (localStorage.getItem('instruction')) {
      this.instruction = localStorage.getItem('instruction')
    }
    this.getCartDetails()
    this.imageBase = environment.imageBase
  }

  getCartDetails() {
    this.isLoading = true
    let data = {
      promo_code_applied: this.isPromoCodeApplied,
      schembalance_applied: this.isSchemeBalanceApplied,
      weightbalance_applied: this.isWeightBalanceApplied,
      mainwallet_applied: this.isMainWalletApplied,
      reward_points_applied: this.isRewardPointApplied,
      promo_code: this.promocode,
      reward_points: this.redeem_points,
    }

    this.apiService.postData(apiEndPoints.get_cart_details, data).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.cartData = res?.Data;
        this.isLoading = false;
      }
    })
  }

  checkout() {
    let data = {
      promo_code_applied: this.isPromoCodeApplied,
      schembalance_applied: this.isSchemeBalanceApplied,
      weightbalance_applied: this.isWeightBalanceApplied,
      mainwallet_applied: this.isMainWalletApplied,
      reward_points_applied: this.isRewardPointApplied,
      promo_code: this.promocode,
      reward_points: this.redeem_points,
      special_instruction: this.instruction
    }
    this.apiService.postData(apiEndPoints.checkout, data).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.accessCode = res?.Data?.accesscode
        this.encRequest = res?.Data?.encrypted_data
        setTimeout(() => {
          this.form.nativeElement.submit()
        }, 500)
      } else this.toast.error(res?.Message)
    })
  }

  paymentSuccess() {
    let data = { id: this.paymentId, order_id: this.orderId }
    this.apiService.postData(apiEndPoints.order_payment_success, data).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.toast.success(res?.Message)
        this.router.navigateByUrl('/my-orders')
        this.getCartDetails()
      } else this.toast.error(res?.Message)
    })
  }

}
