<!-- <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="square-jelly-box" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner>

<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div> -->

<div class="myAccount">
  <div class="container">
    <div class="title">
      <h1>My Account</h1>
    </div>
    <div class="contentSection">
      <div class="sideMenu">
        <app-sidebar></app-sidebar>
      </div>
      <div class="contents">
        <div class="head">
          <span>My Wallet </span>
          <div class="info">
            <img src="\assets\images\wallet\Vector.svg" alt="info icon">
            <div class="infoTxt">Use your wallet balance while purchasing a product</div>
          </div>
        </div>
        <div class="walletSection">
          <div class="tabs">
            <div class="tabName" (click)="main()" [ngClass]="mainTab? 'active':''">
              Main Wallet Balance
            </div>
            <div class="tabName" (click)="scheme()" [ngClass]="schemeTab? 'active':''">
              Scheme Balance
            </div>
            <div class="tabName" (click)="reward()" [ngClass]="rewardTab? 'active':''">
              Reward Points
            </div>
          </div>
          <div class="tabContent" [ngClass]="mainTab? 'show':'hide'">
            <div class="walletBalance">
              <div class="leftSide">
                <div class="rate">{{data?.wallet_balance}}</div>
                <div class="wallet">Wallet Balance</div>
              </div>
              <div class="rightSide">
                <div class="primary-button golden" (click)="redeemPopUp()"><img src="\assets\images\wallet\gift.svg"
                    alt="gift icon"><span>Redeem Gift Card</span> </div>
              </div>
            </div>
            <div class="walletContent" *ngFor="let item of data?.main_wallet_history">
              <div class="detail">
                <div class="icon"><img src="\assets\images\wallet\Group 21878.svg" alt="arrow"></div>
                <div class="wallName">
                  <div class="name">{{item?.description}}</div>
                  <div class="date">
                    <span>{{item?.date}}</span>
                    <span>{{item?.time}}</span>
                  </div>
                </div>
              </div>
              <div class="price credit" *ngIf="item?.is_credit">{{item?.points}}</div>
              <div class="price debit" *ngIf="!item?.is_credit">{{item?.points}}</div>
            </div>
            <!-- <div class="walletContent">
              <div class="detail">
                <div class="icon"><img src="\assets\images\wallet\Group 21878.svg" alt=""></div>
                <div class="wallName">
                  <div class="name">Refund Added to wallet</div>
                  <div class="date">
                    <span>Jan 13 2022</span>
                    <span>3:46 Pm</span>
                  </div>
                </div>
              </div>
              <div class="price credit">+₹500</div>
            </div> -->
          </div>
          <div class="tabContent" [ngClass]="schemeTab? 'show':'hide'">


            <div class="subtabs">
              <div class="tabName" (click)="scheme1()" [ngClass]="schemeTab1? 'active':''">
                Cash Scheme
              </div>
              <div class="tabName" (click)="scheme2()" [ngClass]="schemeTab2? 'active':''">
                Gold Weight Scheme
              </div>
            </div>

            <div class="tabContent" [ngClass]="schemeTab1? 'show':'hide'">


              <div class="walletBalance">
                <div class="leftSide">
                  <div class="rate">{{data?.cash_scheme_balance}}</div>
                  <div class="wallet">Wallet Balance</div>
                </div>
                <div class="rightSide"></div>
              </div>
              <div class="walletContent" *ngFor="let item of data?.scheme_balance_history">
                <ng-container *ngIf="item.is_cash">
                <div class="detail" >
                  <div class="icon"><img src="\assets\images\wallet\Group 21878.svg" alt="arrow"></div>
                  <div class="wallName">
                    <div class="name">{{item?.description}}</div>
                    <div class="date">
                      <span>{{item?.date}}</span>
                      <span>{{item?.time}}</span>
                    </div>
                  </div>
                </div>
                <div class="price credit">{{item?.amount}}</div>
              </ng-container>
                <!-- <div class="price debit" *ngIf="!item?.is_credit">{{item?.points}}</div> -->
              </div>
              <!-- <div class="walletContent">
                <div class="detail">
                  <div class="icon"><img src="\assets\images\wallet\Group 21878.svg" alt=""></div>
                  <div class="wallName">
                    <div class="name">Used for the order (id:4568621)</div>
                    <div class="date">
                      <span>Jan 13 2022</span>
                      <span>3:46 Pm</span>
                    </div>
                  </div>
                </div>
                <div class="price debit">+₹500</div>
              </div> -->
            </div>

            <div class="tabContent" [ngClass]="schemeTab2? 'show':'hide'">
              <div class="walletBalance">
                <div class="leftSide">
                  <div class="rate">{{data?.weight_scheme_balance}}</div>
                  <div class="wallet">Wallet Balance</div>
                </div>
                <div class="rightSide"></div>
              </div>
              <div class="walletContent" *ngFor="let item of data?.scheme_balance_history">
                <ng-container *ngIf="!item.is_cash">
                <div class="detail" >
                  <div class="icon"><img src="\assets\images\wallet\Group 21878.svg" alt="array"></div>
                  <div class="wallName">
                    <div class="name">{{item?.description}}</div>
                    <div class="date">
                      <span>{{item?.date}}</span>
                      <span>{{item?.time}}</span>
                    </div>
                  </div>
                </div>
                <div class="price credit">{{item?.weight}}</div>
              </ng-container>
                <!-- <div class="price debit" *ngIf="!item?.is_credit">{{item?.points}}</div> -->
              </div>
              <!-- <div class="walletContent">
                <div class="detail">
                  <div class="icon"><img src="\assets\images\wallet\Group 21878.svg" alt=""></div>
                  <div class="wallName">
                    <div class="name">Used for the order (id:4568621)</div>
                    <div class="date">
                      <span>Jan 13 2022</span>
                      <span>3:46 Pm</span>
                    </div>
                  </div>
                </div>
                <div class="price debit">+₹500</div>
              </div> -->
            </div>
          </div>

          <div class="tabContent" [ngClass]="rewardTab? 'show':'hide'">
            <div class="walletBalance">
              <div class="leftSide">
                <div class="rate">{{data?.reward_points}}</div>
                <div class="wallet">Wallet Balance</div>
              </div>
              <div class="rightSide"></div>
            </div>
            <div class="walletContent" *ngFor="let item of data?.reward_points_history">
              <div class="detail">
                <div class="icon"><img src="\assets\images\wallet\Group 21878.svg" alt="arrow"></div>
                <div class="wallName">
                  <div class="name">{{item?.description}}</div>
                  <div class="date">
                    <span>{{item?.date}}</span>
                    <span>{{item?.time}}</span>
                  </div>
                </div>
              </div>
              <div class="price credit" *ngIf="item?.is_credit">{{item?.points}}</div>
              <div class="price debit" *ngIf="!item?.is_credit">{{item?.points}}</div>
            </div>
            <!-- <div class="walletContent">
              <div class="detail">
                <div class="icon"><img src="\assets\images\wallet\Group 21878.svg" alt=""></div>
                <div class="wallName">
                  <div class="name">Refund Added to wallet</div>
                  <div class="date">
                    <span>Jan 13 2022</span>
                    <span>3:46 Pm</span>
                  </div>
                </div>
              </div>
              <div class="price debit">+₹500</div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



<div class="giftPopup" [ngClass]="PopupGift?'show':'hide'">
  <div class="box">
    <form [formGroup]="redeemForm">
      <div class="close" (click)="popUpClose()"><img src="\assets\images\close-white.svg" alt="close"></div>
      <div><input type="text" placeholder="Enter Gift card Id" formControlName="gift_id"></div>
      <div><input type="tel" placeholder="Enter Redemption Code" formControlName="redemption_code"></div>
      <div class="button">
        <div class="primary-button white" (click)="popUpClose()">Cancel</div>
        <div class="primary-button golden" (click)="redeemGift()">Redeem</div>
      </div>
    </form>
  </div>
</div>
