<!-- <ngx-spinner
  bdColor="rgba(0, 0, 0, 0.8)"
  size="medium"
  color="#fff"
  type="square-jelly-box"
  [fullScreen]="true"
>
  <p style="color: white">Loading...</p>
</ngx-spinner>

<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" />
</div> -->

<div class="container">
  <h1>{{ data?.productName }}</h1>
  <div class="breadCrumb">
    <a href="" class="link">Home</a>
    <!-- <a href="#" class="link">GOLD</a>
    <a href="#" class="link">Earrings</a> -->
    <div class="link">{{ data?.productName }}</div>
  </div>
</div>
<div class="detailSection">
  <div class="container">
    <div class="section">
      <div class="product">
        <div class="productImage">
          <div class="ar">
            <!-- <img src="\assets\images\ar.png" alt="" /> -->
            <span class="info"></span>
          </div>
          <div class="">
            <ng-container *ngIf="!isMobile">
              <lib-ngx-image-zoom
                [thumbImage]="myThumbnail"
                [fullImage]="myFullresImage"
                [magnification]="1"
                [enableScrollZoom]="true"
                [lensWidth]="500"
              ></lib-ngx-image-zoom>
            </ng-container>
            <ng-container *ngIf="isMobile">
              <img
                [src]="myThumbnail"
                [alt]="data?.productName"
                class="mobile-product-image"
                (click)="openImagePopup(0)"
              />
            </ng-container>
          </div>
          <div
            class="wishlist"
            (click)="addToWishlist(data.id, data.isfavorite)"
            [ngClass]="{ active: data?.isfavorite }"
          >
            <img
              class="fav"
              src="\assets\images\favorite.png"
              alt="wishlist icon"
            />
            <img
              class="fav-gold"
              src="\assets\images\favorite-gold.png"
              alt="wishlist icon"
            />
          </div>
          <div class="share">
            <img
              class="shareImg"
              src="\assets\images\share.svg"
              alt="share icon"
            />
            <div class="socials">
              <a (click)="shareToFacebook()"
                ><i class="fa-brands fa-facebook-f"></i
              ></a>
              <a (click)="shareOnTwitter()"
                ><i class="fa-brands fa-twitter"></i
              ></a>
              <a (click)="shareOnInstagram()"
                ><i class="fa-brands fa-instagram"></i
              ></a>
              <a (click)="shareOnPinterest()"
                ><i class="fa-brands fa-pinterest-p"></i
              ></a>
            </div>
          </div>
        </div>
        <div class="productSlide">
          <swiper [config]="productSlide">
            <ng-container *ngFor="let img of images; let i = index">
              <ng-template swiperSlide
                ><img
                  src="{{ imageBase }}/{{ img?.thumbnail }}"
                  alt="product image"
                  (click)="changeImage(i)"
              /></ng-template>
            </ng-container>
          </swiper>
        </div>
      </div>
      <div class="productDetail">
        <div class="productName">
          {{ data?.productName }}
        </div>
        <div class="productCode">Product Code : {{ data?.barcode }}</div>
        <div class="details" *ngIf="data?.description">
          <p
            [class.collapsed]="isCollapsed"
            [style.height]="isCollapsed ? maxHeight + 'px' : 'auto'"
          >
            {{ data?.description }}
          </p>
          <a
            class="readMore"
            *ngIf="
              isCollapsable &&
              data?.description &&
              data?.description.length > 30
            "
            (click)="isCollapsed = !isCollapsed"
            >Read {{ isCollapsed ? "more" : "less" }}</a
          >
        </div>

        <div class="weight" *ngIf="data?.gold_availability == 1">
          Gold Weight: {{ data?.netweight }} g<span *ngIf="data?.size != 0"
            >|</span
          ><span *ngIf="data?.size != 0"> Size: {{ data?.size }} cm</span>
        </div>
        <div class="price">
          <div class="offer">
            <div class="rate">₹{{ data?.price }}</div>
          </div>
          <div class="old" *ngIf="data?.actualPrice">
            ₹{{ data?.actualPrice }}
          </div>
        </div>
        <div class="incl">(Inclusive of all taxes)</div>
        <div class="deliveryOption">
          <div class="dOption">Delivery Option</div>
          <div class="d-flex align-items-center pin">
            <div class="pincode">
              <form [formGroup]="pinForm">
                <span
                  ><input
                    type="tel"
                    formControlName="pincode"
                    placeholder="Enter Pincode"
                /></span>
                <span class="check" (click)="checkPincode()">Check</span>
                <span class="text-danger" *ngIf="pf.pincode.errors?.pattern"
                  >Please Enter Valid Pincode
                </span>
              </form>
            </div>
            <div *ngIf="deliveryStatus" class="out">
              {{ deliveryStatus?.message }}
              <a routerLink="/contact"> .Contact us</a> for any queries
            </div>
          </div>
        </div>
        <div *ngIf="data?.ordernow_available == 0 && data?.outOfStock == true">
          Out of Stock
        </div>
        <div
          *ngIf="data?.outOfStock == false && data?.cartCount == 0"
          class="addtoCart primary-button golden"
          (click)="addToCart(data?.id)"
        >
          Add To Cart
        </div>
        <div
          *ngIf="data?.outOfStock == true && data?.ordernow_available == 1"
          class="addtoCart primary-button golden"
          [routerLink]="['/order-now', data?.id]"
        >
          Order Now
        </div>
        <div
          *ngIf="data?.cartCount == 1"
          class="addtoCart primary-button golden"
          [routerLink]="['/cart']"
        >
          View Cart
        </div>
        <div class="priceBreakUp" *ngIf="data?.price_breakup != '' || null">
          <div class="head" (click)="showAccordion(1)">
            <span>
              <span class="img">
                <img src="\assets\images\price-tag.png" alt="price tag icon"
              /></span>
              <span class="pricebreak">Price Breakup</span>
            </span>
            <span class="plus"> <i class="fa fa-plus"></i> </span>
          </div>
          <div class="priceBreaks" [ngClass]="{ active: selectedIndex == 1 }">
            <swiper [config]="breakSlider">
              <ng-template
                swiperSlide
                *ngFor="let breakup of data?.price_breakup?.items"
              >
                <div class="break">
                  <div class="pName">{{ breakup?.title }}</div>
                  <div class="pBreak">₹{{ breakup?.breakup_price }}</div>
                </div>
              </ng-template>
            </swiper>
            <div class="total">
              <span>Total </span>
              <span> ₹ {{ data?.price_breakup?.total }}</span>
            </div>
          </div>
        </div>
        <div
          *ngIf="data?.customize_available == 1"
          class="customDesign"
          (click)="popUp()"
        >
          <img
            class="image"
            src="\assets\images\detailpage-custom-design.svg"
            alt="custom design image"
          />
          <div class="text">
            <div class="head">Customize Design</div>
            <p>
              Customize your jewellery as you wish give us a short description
              about how you want it
            </p>
            <div class="primary-button golden">Enquire</div>
          </div>
        </div>
        <div class="productData">
          <div class="heading">Product Details</div>
          <div class="box">
            <div class="sections">
              <div class="head">{{ data?.productDetail?.title }}</div>
              <div class="content">
                <ng-container *ngFor="let detail of productDetail">
                  <div class="fields">
                    <span>{{ detail?.key }}</span>
                    <span>: {{ detail?.value }}</span>
                  </div>
                </ng-container>
              </div>
            </div>
            <!-- <div class="sections hide" [ngClass]="{'show': selected == 1}">
              <div class="head">Basic Information</div>
              <div class="content">
                <div class="fields">
                  <span>Gold Purity</span>
                  <span>: 22KT(916)</span>
                </div>
                <div class="fields">
                  <span>Metal Color</span>
                  <span>: Yellow</span>
                </div>
                <div class="fields">
                  <span>Gross Weight(gm)</span>
                  <span>: 8.440</span>
                </div>
                <div class="fields">
                  <span>Net Weight (gm)</span>
                  <span>: 7.690</span>
                </div>
              </div>
            </div> -->
            <!-- <div class="more" [ngClass]="{'show': selected == 1}" (click)="more(1)">
              <span class="more">Read More <span><i class="fa fa-angle-down"></i></span></span>
              <span class="less">Read Less <span><i class="fa fa-angle-up"></i></span></span>
            </div> -->
          </div>
        </div>
        <div
          class="matchingProducts"
          *ngIf="data?.addonProducts && data?.addonProducts.length > 0"
        >
          <h3>Matching Products</h3>
          <div class="productsSimilar">
            <a href="#" class="products mainProduct">
              <img
                src="{{ imageBase }}/{{ data?.image }}"
                [alt]="data?.productName + ' image'"
              />
              <div class="productName">{{ data?.productName }}</div>
              <div class="rate">₹{{ data?.price }}</div>
            </a>
            <ng-container *ngFor="let product of addOns; let i = index">
              <a
                [routerLink]="['/detail', product.slug]"
                class="products addon"
              >
                <span class="check"
                  ><input
                    (click)="selectAddons(product.id, product.price)"
                    type="checkbox"
                    checked
                    name=""
                    id="{{ i + 1 }}" /><label for="{{ i + 1 }}"></label
                ></span>
                <img
                  src="{{ imageBase }}/{{ product.image }}"
                  [alt]="product.productName + ' image'"
                />
                <div class="productName">{{ product.productName }}</div>
                <div class="rate">₹{{ product.price }}</div>
              </a>
            </ng-container>
          </div>
          <div class="dashed"></div>
          <div class="productsSimilar cost">
            <a href="#" class="products mainProduct">
              <div class="item">1 Item</div>
              <div class="rate">
                ₹{{ data?.addon_calculations?.productPrice }}
              </div>
            </a>
            <a href="#" class="products addon">
              <div class="item">{{ addOnLength }} Add-ons</div>
              <div class="rate">₹{{ addOnsum }}</div>
            </a>
            <a href="#" class="products addon total">
              <div class="item">Total</div>
              <div class="rate">₹{{ grandTotal }}</div>
            </a>
          </div>
          <div class="add" (click)="multipleAddtoCart()">
            <span class="primary-button golden"
              >Add {{ addOnLength + 1 }} Items To Cart</span
            >
          </div>
        </div>
      </div>
    </div>
    <div
      class="similarProducts"
      *ngIf="data?.similarProducts && data?.similarProducts?.length > 0"
    >
      <h3>Similar Products</h3>
      <div class="products">
        <ng-container *ngFor="let product of similarproducts">
          <div class="slideItems">
            <div
              class="wishlist"
              (click)="addToWishlist(product.id, product.isfavorite)"
              [ngClass]="{ active: product.isfavorite }"
            >
              <img
                class="fav"
                src="\assets\images\favorite.png"
                alt="wishlist icon"
              />
              <img
                class="fav-gold"
                src="\assets\images\favorite-gold.png"
                alt="wishlist icon"
              />
            </div>
            <a class="productImage" [routerLink]="['/detail', product.slug]"
              ><img
                src="{{ imageBase }}/{{ product.image }}"
                [alt]="product.productName + ' image'"
                class="img-fluid"
            /></a>
            <a [routerLink]="['/detail', product.slug]" class="productName">{{
              product.productName
            }}</a>
            <a [routerLink]="['/detail', product.slug]" class="price">
              <div class="offer"><span>₹</span>{{ product.price }}</div>
              <div class="old" *ngIf="product?.actualPrice">
                <span>₹</span>{{ product.actualPrice }}
              </div>
            </a>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="bookEnquire">
      <a
        routerLink="/gold-schemes"
        class=""
        [ngStyle]="{
          'background-image':
            'url(' + imageBase + '/' + data?.mi_gold_banner?.image + ')'
        }"
      >
        <h3>{{ data?.mi_gold_banner?.title }}</h3>
        <p>{{ data?.mi_gold_banner?.description }}</p>
        <div class="join">{{ data?.mi_gold_banner?.button_text }}</div>
      </a>
      <a
        routerLink="/advance-booking"
        class=""
        [ngStyle]="{
          'background-image':
            'url(' + imageBase + '/' + data?.advance_booking_banner?.image + ')'
        }"
      >
        <h3>{{ data?.advance_booking_banner?.title }}</h3>
        <div class="book">{{ data?.advance_booking_banner?.button_text }}</div>
      </a>
    </div>
  </div>
</div>

<!-- popup -->
<div class="enquirePopup" [ngClass]="PopupStatus ? 'show' : 'hide'">
  <div class="box">
    <form [formGroup]="enquireForm">
      <div class="field">
        <textarea
          name=""
          id=""
          cols="30"
          rows="3"
          placeholder="Description"
          formControlName="description"
        ></textarea>
      </div>
      <div class="field">
        <input
          type="tel"
          placeholder="Mobile Number"
          formControlName="mobile"
        />
      </div>
      <div class="buttons">
        <div class="cancel" (click)="popUpClose()">Cancel</div>
        <div class="submit" (click)="submitEnquiry()">
          <input type="submit" value="Submit" />
        </div>
      </div>
    </form>
  </div>
</div>

<div class="PopupProduct" [ngClass]="PopupProduct ? 'show' : 'hide'">
  <div class="productImage">
    <div class="close" (click)="PopupProductClose()">
      <img src="\assets\images\close-white.svg" alt="close icon" />
    </div>

    <div class="productsimg">
      <span
        ><img src="\assets\images\Component 31.png" alt="product image"
      /></span>
      <span
        ><img src="\assets\images\Component 31.png" alt="product image"
      /></span>
      <span
        ><img src="\assets\images\Component 31.png" alt="product image"
      /></span>
      <span
        ><img src="\assets\images\Component 31.png" alt="product image"
      /></span>
      <span
        ><img src="\assets\images\Component 31.png" alt="product image"
      /></span>
    </div>
    <lib-ngx-image-zoom
      [thumbImage]="myThumbnail"
      [fullImage]="myFullresImage"
      [magnification]="1"
      [enableScrollZoom]="true"
      [lensWidth]="500"
    ></lib-ngx-image-zoom>
  </div>
</div>

<div class="fullscreen-popup" *ngIf="isImagePopupOpen && isMobile" (click)="closeImagePopup()">
  <div class="popup-content" (click)="$event.stopPropagation()">
    <button class="close-btn" (click)="closeImagePopup()">
      <img src="\assets\images\close-white.svg" alt="close icon" />
    </button>

    <div class="image-container"
         (touchstart)="onTouchStart($event)"
         (touchmove)="onTouchMove($event)"
         (touchend)="onTouchEnd()">
      <img
        [src]="imageBase + '/' + images[selectedImageIndex]?.image"
        [alt]="data?.productName"
        [ngStyle]="{'pointer-events': 'none'}"
      />
    </div>

    <div class="navigation-buttons">
      <button
        class="nav-btn prev"
        (click)="previousImage()"
        [disabled]="selectedImageIndex === 0"
      >
        <i class="fa fa-chevron-left"></i>
      </button>
      <button
        class="nav-btn next"
        (click)="nextImage()"
        [disabled]="selectedImageIndex === images.length - 1"
      >
        <i class="fa fa-chevron-right"></i>
      </button>
    </div>

    <div class="image-counter">
      {{ selectedImageIndex + 1 }} / {{ images.length }}
    </div>
  </div>
</div>