<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div>

<div class="myAccount">
  <div class="container">
    <div class="title">
      <h1>My Account</h1>
    </div>
    <div class="contentSection">
      <div class="sideMenu">
        <app-sidebar></app-sidebar>
      </div>
      <div class="contents" *ngIf="isGiftForm">
        <div class="head">
          <span>Personalize Your Gift Card</span>
        </div>
        <form [formGroup]="giftForm">
          <div class="giftSection">
            <div class="leftSection">
              <div class="image">
                <img src="{{ data?.image }}" alt="gift card image" />
              </div>
              <div class="name">{{ data?.title }}</div>
            </div>
            <div class="rightSection">
              <div class="field">
                <div>Amount</div>
                <input
                  type="number"
                  formControlName="amount"
                  (keydown)="onKeyDown($event)"
                />
                <span *ngIf="gf.amount.invalid && gf.amount.touched">
                  <span class="text-danger" *ngIf="gf.amount.errors?.required">
                    Amount is required
                  </span>
                </span>
                <span class="text-danger" *ngIf="gf.amount.errors?.pattern"
                  >Please Enter Valid Amount
                </span>
              </div>
              <div class="field">
                <div>Recipient Name</div>
                <input type="text" formControlName="recipName" />
                <span *ngIf="gf.recipName.invalid && gf.recipName.touched">
                  <span
                    class="text-danger"
                    *ngIf="gf.recipName.errors?.required"
                  >
                    Name is required
                  </span>
                </span>
              </div>
              <div class="field">
                <div>Mobile Number</div>
                <input
                  type="number"
                  formControlName="mobile"
                  (keydown)="onKeyDown($event)"
                />
                <span *ngIf="gf.mobile.invalid && gf.mobile.touched">
                  <span class="text-danger" *ngIf="gf.mobile.errors?.required">
                    Mobile Number is required
                  </span>
                </span>
                <span class="text-danger" *ngIf="gf.mobile.errors?.pattern"
                  >Please Enter Valid Mobile Number</span
                >
              </div>
              <div class="field">
                <div>Email</div>
                <input type="email" formControlName="email" />
                <span *ngIf="gf.email.invalid && gf.email.touched">
                  <span class="text-danger" *ngIf="gf.email.errors?.required">
                    Email is required
                  </span>
                </span>
                <span class="text-danger" *ngIf="gf.email.errors?.pattern"
                  >Please Enter Valid Email
                </span>
              </div>
              <div class="field">
                <div>Message</div>
                <textarea
                  name=""
                  id=""
                  cols="30"
                  rows="3"
                  formControlName="message"
                ></textarea>
                <span *ngIf="gf.message.invalid && gf.message.touched">
                  <span class="text-danger" *ngIf="gf.message.errors?.required">
                    Message is required
                  </span>
                </span>
              </div>
              <div class="field">
                <div>Sender's Name</div>
                <input type="text" formControlName="senderName" />
                <span *ngIf="gf.senderName.invalid && gf.senderName.touched">
                  <span
                    class="text-danger"
                    *ngIf="gf.senderName.errors?.required"
                  >
                    Name is required
                  </span>
                </span>
              </div>
              <div class="button">
                <div class="primary-button white" (click)="giftcard()">
                  Preview
                </div>
                <div class="primary-button golden" (click)="submit()">
                  Continue
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
      <div class="contents" *ngIf="isBilling">
        <form [formGroup]="billingForm">
          <div class="head">
            <span>Billing Information</span>
          </div>
          <div class="giftSection">
            <div class="editPersonalInfo">
              <div class="box">
                <div class="form">
                  <div class="twoSection">
                    <div class="field">
                      <div class="fieldName">Name</div>
                      <input type="text" formControlName="name" />
                      <span *ngIf="bf.name.invalid && bf.name.touched">
                        <span
                          class="text-danger"
                          *ngIf="bf.name.errors?.required"
                        >
                          Name is required
                        </span>
                      </span>
                    </div>
                    <div class="field">
                      <div class="fieldName">Email</div>
                      <input type="text" formControlName="email" />
                      <span *ngIf="bf.email.invalid && bf.email.touched">
                        <span
                          class="text-danger"
                          *ngIf="bf.email.errors?.required"
                          >Email is required
                        </span>
                      </span>
                      <span class="text-danger" *ngIf="bf.email.errors?.pattern"
                        >Please Enter Valid Email</span
                      >
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field">
                      <div class="fieldName">Address</div>
                      <textarea
                        name=""
                        id=""
                        cols="30"
                        rows="6"
                        formControlName="address"
                      ></textarea>
                      <span *ngIf="bf.address.invalid && bf.address.touched">
                        <span
                          class="text-danger"
                          *ngIf="bf.address.errors?.required"
                        >
                          Address is required
                        </span>
                      </span>
                    </div>
                    <div class="field">
                      <div class="billCountry">
                        <div class="fieldName">Country</div>
                        <ng-select
                          class="filtername selectOption"
                          [items]="countries"
                          bindLabel="name"
                          bindValue="id"
                          [(ngModel)]="selectedCountry"
                          [ngModelOptions]="{ standalone: true }"
                          [clearable]="false"
                          [searchable]="false"
                          (change)="selectCountry()"
                        >
                        </ng-select>
                        <span class="text-danger" *ngIf="!selectedCountry">{{
                          countryValidation
                        }}</span>
                      </div>
                      <div>
                        <div class="fieldName">Province / State</div>
                        <ng-select
                          class="filtername selectOption"
                          [items]="states"
                          bindLabel="name"
                          bindValue="id"
                          [(ngModel)]="selectedState"
                          [ngModelOptions]="{ standalone: true }"
                          [clearable]="false"
                          [searchable]="false"
                          [disabled]="!states || states.length === 0"
                        >
                        </ng-select>
                        <span class="text-danger" *ngIf="!selectedState">{{
                          stateValidation
                        }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field">
                      <div class="fieldName">Postal Code / Zip</div>
                      <input
                        type="number"
                        formControlName="pincode"
                        (keydown)="onKeyDown($event)"
                      />
                      <span *ngIf="bf.pincode.invalid && bf.pincode.touched">
                        <span
                          class="text-danger"
                          *ngIf="bf.pincode.errors?.required"
                        >
                          Pincode is required
                        </span>
                      </span>
                      <span
                        class="text-danger"
                        *ngIf="bf.pincode.errors?.pattern"
                        >Please Enter Valid Pincode
                      </span>
                    </div>
                    <div class="field">
                      <div class="fieldName">City</div>
                      <input type="text" formControlName="city" />
                      <span *ngIf="bf.city.invalid && bf.city.touched">
                        <span
                          class="text-danger"
                          *ngIf="bf.city.errors?.required"
                        >
                          City is required
                        </span>
                      </span>
                    </div>
                  </div>
                  <div class="twoSection">
                    <div class="field">
                      <div class="fieldName">Contact Phone*</div>
                      <input
                        type="number"
                        formControlName="mobile"
                        (keydown)="onKeyDown($event)"
                      />
                      <span *ngIf="bf.mobile.invalid && bf.mobile.touched">
                        <span
                          class="text-danger"
                          *ngIf="bf.mobile.errors?.required"
                        >
                          Mobile Number is required
                        </span>
                      </span>
                      <span
                        class="text-danger"
                        *ngIf="bf.mobile.errors?.pattern"
                        >Please Enter Valid Mobile NUmber</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="buttons">
            <div class="save primary-button golden" (click)="completeOrder()">
              Complete Order
            </div>
          </div>
        </form>
        <form
          *ngIf="!checkDomain"
          #form
          ngNoForm
          id="nonseamless"
          method="post"
          name="redirect"
          action="{{ ccAvenueUrl }}?command=initiateTransaction&enc_request={{
            encRequest
          }}&access_code={{
            accessCode
          }}&request_type=XML&response_type=XML&version=1.1"
        >
          <input
            type="hidden"
            id="encRequest"
            name="encRequest"
            [ngModel]="encRequest"
          />
          <input
            type="hidden"
            name="access_code"
            id="access_code"
            [ngModel]="accessCode"
          />
        </form>
        <form
          *ngIf="checkDomain"
          #form
          ngNoForm
          id="nonseamless"
          method="post"
          name="redirect"
          action="{{ ccAvenueBeta }}?command=initiateTransaction"
        >
          <input
            type="hidden"
            id="encRequest"
            name="encRequest"
            [ngModel]="encRequest"
          />
          <input
            type="hidden"
            name="access_code"
            id="access_code"
            [ngModel]="accessCode"
          />
        </form>
      </div>
    </div>
  </div>
</div>

<div class="registerPopup otp" [ngClass]="PopupGift ? 'show' : 'hide'">
  <div class="box">
    <div class="top">
      <div class="rate">₹{{ amount }}</div>
      <div class="close" (click)="popUpClose()">
        <img src="{{ data?.image }}" alt="close" />
      </div>
      <div class="giftfor">
        <img src="\assets\images\gift-cards\rings.svg" alt="gift card image" />
        <div class="name">{{ data?.title }}</div>
      </div>
    </div>
    <div class="bottom">
      <div class="from">From</div>
      <div class="name">{{ sender_name }}</div>
      <p>{{ message }}</p>
      <div class="re">Redemption Code</div>
      <div class="code">xxxxx</div>
    </div>
  </div>
</div>
