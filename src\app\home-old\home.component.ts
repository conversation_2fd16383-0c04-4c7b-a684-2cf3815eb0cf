import { Component, OnInit, Renderer2 } from '@angular/core';
import SwiperCore, {
  Swiper,
  SwiperOptions,
  Virtual,
  Navigation,
  FreeMode,
  Pagination,
  Autoplay,
} from 'swiper';
import { SwiperComponent } from 'swiper/angular';
import { environment } from 'src/environments/environment.prod';
SwiperCore.use([Virtual, FreeMode, Navigation, Pagination, Autoplay]);
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from '../Services/api.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { CommonService } from '../Services/common.service';
import { Meta } from '@angular/platform-browser';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
})
export class HomeComponent implements OnInit {
  bannerMain: SwiperOptions = {
    spaceBetween: 0,
    slidesPerView: 1,
    freeMode: false,
    watchSlidesProgress: true,
    pagination: { clickable: true },
    navigation: true,
    //loop: true,
    autoplay: true,
  };
  specificationSlider: SwiperOptions = {
    slidesPerView: 8,
    freeMode: false,
    watchSlidesProgress: true,
    pagination: false,
    navigation: false,
    breakpoints: {
      320: {
        slidesPerView: 1.5,
        spaceBetween: 10,
      },
      360: {
        slidesPerView: 2.5,
      },
      640: {
        slidesPerView: 4.5,
      },
      700: {
        slidesPerView: 5.5,
      },
      992: {
        slidesPerView: 8,
        spaceBetween: 43,
      },
    },
  };
  trendSlide: SwiperOptions = {
    spaceBetween: 0,
    slidesPerView: 4,
    freeMode: true,
    watchSlidesProgress: true,
    navigation: {
      nextEl: '#trend-Next',
      prevEl: '#trend-Prev',
    },
    autoplay: {
      delay: 2000,
    },
    breakpoints: {
      320: {
        slidesPerView: 1,
      },
      480: {
        slidesPerView: 2,
      },
      992: {
        slidesPerView: 3,
        autoplay: {
          delay: 2000,
        },
      },
      1200: {
        slidesPerView: 4,
      },
    },
  };
  brandSlide: SwiperOptions = {
    spaceBetween: 34,
    slidesPerView: 4,
    freeMode: false,
    loop: false,
    watchSlidesProgress: true,
    navigation: {
      nextEl: '#category-Next',
      prevEl: '#category-Prev',
    },
    autoplay: {
      delay: 2000,
    },
    breakpoints: {
      320: {
        slidesPerView: 1,
      },
      480: {
        slidesPerView: 2,
      },
      700: {
        slidesPerView: 4,
      },
    },
  };
  hotCategorySlide: SwiperOptions = {
    spaceBetween: 22,
    slidesPerView: 5,
    freeMode: true,
    watchSlidesProgress: true,
    navigation: {
      nextEl: '#category-Next',
      prevEl: '#category-Prev',
    },
    autoplay: {
      delay: 2000,
    },
    breakpoints: {
      320: {
        slidesPerView: 1,
      },
      480: {
        slidesPerView: 2,
      },
      700: {
        slidesPerView: 4,
      },
      992: {
        autoplay: {
          delay: 2000,
        },
      },
    },
  };
  clientSlide: SwiperOptions = {
    spaceBetween: 18,
    centeredSlides: true,
    loop: false,
    slidesPerView: 3,
    initialSlide: 2,
    freeMode: true,
    watchSlidesProgress: true,
    navigation: {
      nextEl: '#client-Next',
      prevEl: '#client-Prev',
    },
    // autoplay: {
    //   delay: 2000,
    // },
    breakpoints: {
      320: {
        slidesPerView: 1,
      },
      480: {
        slidesPerView: 2,
      },
      700: {
        slidesPerView: 3,
      },
    },
  };
  featuredSlide: SwiperOptions = {
    spaceBetween: 0,
    slidesPerView: 3,
    freeMode: false,
    watchSlidesProgress: true,
    navigation: {
      nextEl: '#category-Next',
      prevEl: '#category-Prev',
    },
    breakpoints: {
      320: {
        slidesPerView: 1,
      },
      480: {
        slidesPerView: 2,
      },
      700: {
        slidesPerView: 3,
      },
    },
  };

  homeData: any;
  imageBase: any;
  trendingListData: any;
  featuredListData: any;
  trendingCollectionData: any;
  menWomenSection: any;
  offerData: any;
  miGoldSection: any;
  perfectBride: any;
  brands: any;
  categories: any;
  testimonials: any;
  blogs: any;
  form: FormGroup | any;
  testimonialModal: boolean = false;
  isSubmitted: boolean = true;
  filedata: any;
  isImageUploaded: boolean = true;
  imageURL: any;
  fileExtArray = ['jpg', 'jpeg', 'JPEG', 'JPG', 'PNG', 'png', 'webp', 'WEBP'];
  showModal: boolean = false;
  isLoading: boolean = false;
  windowRef = window;

  constructor(
    private apiService: ApiService,
    private toast: ToastrService,
    private commonService: CommonService,
    private router: Router,
    private spinner: NgxSpinnerService,
    private metaService: Meta
  ) { }

  popUp() {
    this.testimonialModal = !this.testimonialModal;
  }

  popUpClose() {
    this.testimonialModal = false;
  }

  get testimonialControls() {
    return this.form.controls;
  }

  ngOnInit(): void {
    this.getHome();
    this.addMetaTags();
    this.imageBase = environment.imageBase;

    this.form = new FormGroup({
      name: new FormControl('', Validators.required),
      description: new FormControl('', Validators.required),
    });
    // const script = document.createElement('script');
    // script.innerHTML = `
    //   getSkusListWithTryOn({ companyName: 'Ttdevassyjewellery' })
    //   .then((skusList) => {
    //     addTryOnButton({ skusList });
    //   })
    //   function addTryOnButton({ skusList }) {
    //     loadTryOnButton({
    //       psku: { main: skusList[0].toString(), recommendedSkus: skusList },
    //       page: 'product',
    //       company: 'Ttdevassyjewellery',
    //       nonTryOnProductsReport: 'true',
    //       buynow: { enable: 'true' },
    //       prependButton: { id: 'digitalBtn1' },
    //       MBprependButton: { id: 'digitalBtn1' },
    //       styles:{
    //         tryonbutton:{backgroundColor:'#CE9599',color:'black'},
    //         tryonbuttonHover:{backgroundColor:'black',color:'white',border:'none',},
    //         MBtryonbutton:{width:'100%',borderRadius:'25px'}
    //         },
    //       tryonButtonData: { text: 'Start Digital Wear' },
    //     });
    //   };`;
    // this.renderer.appendChild(document.body, script);
  }

  addMetaTags() {
    this.metaService.updateTag({
      name: 'keywords',
      content:
        'TT Devassy Jewellery Official Website | Purest gold online jewellery store in india | Zora Only one collections | Gold, Diamond, Plpatinum , Handcrafted , bridal and Designer jewellery Buy Online | 916 BIS Hallmarked with HUID',
    });
  }

  getHome() {
    // this.isLoading = true
    this.apiService.getData(apiEndPoints.HOME).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.homeData = res?.Data;
        this.trendingListData = this.homeData?.top_trending_list;
        this.featuredListData = this.homeData?.featured_best_list;
        this.trendingCollectionData = this.homeData?.top_trending_collection;
        this.menWomenSection = this.homeData?.for_men_women;
        this.offerData = this.homeData?.offer_banner;
        this.miGoldSection = this.homeData?.mi_gold_banner;
        this.perfectBride = this.homeData?.perfect_bride;
        this.brands = this.homeData?.brands_we_love;
        this.categories = this.homeData?.hot_categories;
        this.testimonials = this.homeData?.testimonials;
        this.blogs = this.homeData?.blogs;
        // this.isLoading = false
      }
    });
  }

  addToWishlist(id: any, isfavorite: any) {
    if (localStorage.getItem('isLogged')) {
      let data = { type: 0, productId: id };
      if (!isfavorite) data['type'] = 1;
      this.apiService
        .postData(apiEndPoints.WISHLIST, data)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) this.getHome();
        });
    } else {
      this.toggleModal();
    }
  }

  bannerRedirections(type: any, slug: any, title: any, bannerName?: any, bannerCategory?: any) {
    if (bannerName && bannerCategory) {
      this.windowRef.webengage.track('Banner Clicked', { 'Banner Name': bannerName, 'Banner Category': bannerCategory });
    }
    if (type == 1) this.router.navigate(['/detail', slug]);
    else if (type == 2 || type == 3 || type == 4) {
      if (type == 2) type = 'watches';
      else if (type == 3) type = 'collection';
      else if (type == 4) type = 'category';
      this.router.navigate(['/listing', type, slug]);
    }

    if (title == 'Zora') this.router.navigateByUrl('/zora-collection');

    if (type == 5 && slug == 3) {
      this.router.navigateByUrl('/virtual-shopping');
    } else if (type == 5 && slug == 4) {
      this.router.navigateByUrl('/zora-collection');
    } else if (type == 5 && slug == 5) {
      this.router.navigateByUrl('/divo-collection');
    } else if (type == 5 && slug == 6) {
      this.router.navigateByUrl('/poyem-collection');
    } else if (type == 5 && slug == 7) {
      this.router.navigateByUrl('/orlin-collection');
    } else if (type == 5 && slug == 8) {
      this.router.navigateByUrl('/book-appointment');
    } else if (type == 5 && slug == 2) {
      this.router.navigateByUrl('/gold-schemes');
    } else if (type == 5 && slug == 1) {
      this.router.navigateByUrl('/advance-booking');
    } else if (type == 5 && slug == 9) {
      this.router.navigateByUrl('/gift-voucher');
    } else if (type == 5 && slug == 10) {
      this.router.navigateByUrl('/digital-gold');
    } else if (type == 5 && slug == 11) {
      this.router.navigateByUrl('/about');
    }
  }

  openWhatsapp() {
    window.open('https://api.whatsapp.com/send?phone=9497916000', '_blank');
    this.router.navigate([''], { skipLocationChange: true });
  }

  uploadImage(e: any) {
    this.filedata = e.files[0];
    let imageName = e.files[0].name;
    let fileExtensionName = imageName.split('.').pop();
    if (this.fileExtArray.includes(fileExtensionName)) {
      let reader = new FileReader();
      reader.readAsDataURL(this.filedata);
      reader.onload = () => {
        this.imageURL = reader.result;
      };
      this.isImageUploaded = true;
    } else {
      this.toast.error(
        fileExtensionName.toUpperCase() +
        ' not supported. Please upload an image file'
      );
    }
  }

  submitTestimonial() {
    if (localStorage.getItem('isLogged')) {
      if (!this.form.valid) {
        this.isSubmitted = false;
        return;
      }

      if (this.filedata) {
        const formData = new FormData();
        formData.append('name', this.form.get('name')?.value);
        formData.append('description', this.form.get('description')?.value);
        formData.append('image', this.filedata);

        this.apiService
          .postTestimonial(apiEndPoints.post_testimonial, formData)
          .subscribe((res: any) => {
            if (res?.errorcode == 0) {
              this.isSubmitted = true;
              this.toast.success('Submitted successfully');
              this.popUpClose();
              this.imageURL = '';
              this.ngOnInit();
            }
          });
      } else {
        this.isImageUploaded = false;
      }
    } else {
      this.testimonialModal = false;
      this.toggleModal();
    }
  }

  toggleModal() {
    this.commonService.sendClickEvent();
    this.showModal = true;
  }

  bannerClicked(bannerName: any, bannerCategory: any, url: any) {
    this.windowRef.webengage.track('Banner Clicked', { 'Banner Name': bannerName, 'Banner Category': bannerCategory });
    this.router.navigate([url]);
  }
}
