* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.video-slider-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background: #000;
}

.video-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.5s ease;

  &.loading {
    opacity: 0.7;
  }

  &.mobile {
    display: none;
  }
}

/* Progress Bar */
.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4px;
  background: linear-gradient(90deg, #dc143c, #b91c3c);
  transition: width 0.1s linear;
  z-index: 10;
  box-shadow: 0 0 10px rgba(220, 20, 60, 0.5);
}

/* Navigation Arrows */
.nav-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;

  &:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: translateY(-50%);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }
}

.nav-arrow-left {
  left: 90px;
}

.nav-arrow-right {
  right: 90px;
}

/* Slide Indicators */
.slide-indicators {
  display: flex;
  gap: 12px;
  z-index: 10;
  margin-top: 26px;
  width: 100%;
}

.image-slide {
  height: 100%;

  .slide-image {
    object-fit: cover;
    height: 100%;
    width: 100%;
  }
}

.indicator {
  max-width: 42px;
  width: 20%;
  height: 2px;
  background: #ffffff4d;
  cursor: pointer;
  box-shadow: none;
  border: none;

  &:hover {
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.2);
  }

  &.active {
    background: #d3ad68;
  }
}

/* Auto-slide Toggle */
.auto-slide-toggle {
  position: absolute;
  top: 30px;
  right: 30px;
  background: rgba(0, 0, 0, 0.5);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  backdrop-filter: blur(10px);

  &:hover,
  &.active {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
  }
}

/* Next Banner Highlight */
.next-banner {
  display: flex;
  gap: 12px;
  position: absolute;
  bottom: 30px;
  right: 30px;
  background: #ffffff1a;
  padding: 12px;
  color: white;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 15;
  transition: all 0.4s ease;
  cursor: pointer;
  min-width: 280px;
  max-width: 345px;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
    border-color: rgba(255, 255, 255, 0.4);

    .next-banner-preview {
      opacity: 1;
      transform: translateY(0);
    }
  }

  &:active {
    transform: translateY(-2px);
  }
}
.next-banner-content {
  width: 100%;
}
.next-banner-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.next-banner-title {
  font-size: 18px;
  font-weight: 500;
  line-height: 1.3;
  margin-bottom: 4px;
}

.next-banner-subtitle {
  font-size: 14px;
  opacity: 0.9;
  font-weight: 400;
}

.next-banner-preview {
  min-width: 86px;
  width: 86px;
  height: 86px;
  aspect-ratio: 1/1;
  overflow: hidden;
  transition: all 0.3s ease;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

/* Loading Overlay */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  backdrop-filter: blur(5px);
}

.spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Error Overlay */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 20;
  backdrop-filter: blur(10px);

  p {
    margin-bottom: 20px;
    font-size: 18px;
    text-align: center;
  }
}

.retry-btn {
  margin: 5px 10px;
  padding: 10px 20px;
  background: #dc143c;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;

  &:hover {
    background: #b91c3c;
    transform: translateY(-2px);
  }

  &:active {
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-arrow {
    width: 50px;
    height: 50px;
  }

  .video-player {
    &.desktop {
      display: none;
    }

    &.mobile {
      display: block;
    }
  }

  .nav-arrow-left {
    left: 20px;
  }

  .nav-arrow-right {
    right: 20px;
  }

  .next-banner {
    bottom: 20px;
    right: 20px;
    min-width: 220px;
  }

  .next-banner-title {
    font-size: 16px;
  }

  .next-banner-subtitle {
    font-size: 13px;
  }

  .auto-slide-toggle {
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
  }

  .slide-indicators {
    bottom: 20px;
  }
}

@media (max-width: 480px) {
  .next-banner {
    bottom: 15px;
    right: 15px;
    min-width: 200px;
  }

  .next-banner-title {
    font-size: 14px;
  }

  .next-banner-subtitle {
    font-size: 12px;
  }

  .nav-arrow {
    width: 45px;
    height: 45px;
  }

  .nav-arrow-left {
    left: 15px;
  }

  .nav-arrow-right {
    right: 15px;
  }
}
