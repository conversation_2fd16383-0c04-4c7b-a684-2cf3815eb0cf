import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from '../ApiEndPoints';

@Component({
  selector: 'app-diamond-education',
  templateUrl: './diamond-education.component.html',
  styleUrls: ['./diamond-education.component.scss']
})
export class DiamondEducationComponent implements OnInit {
  data: any;

  constructor(private ApiService: ApiService, private taost: ToastrService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.diamondEducation).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.data = res?.data
      } else this.taost.error(res?.message)
    })
  }

}
