.check {
  text-align: center;
  margin-bottom: 30px;
  margin-top: 35px;
  display: flex;
  font-weight: 400;

  span {
    font-size: 13px;

    &+span {
      padding-left: 15px;
    }
  }

  a {
    color: #CB9F52;
    text-decoration: underline;
  }
}

.adBanner {
  background-repeat: no-repeat;
  background-size: cover;
  height: 504px;
  /*  */
  position: relative;
  background-position: right;

  .content {
    max-width: 660px;
    color: #fff;
    padding: 0 30px;
    position: absolute;
    left: 104px;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;

    h1 {
      color: #CCA154;
      text-transform: capitalize;
      font-size: 44px;
      margin-bottom: 15px;
    }

    h3 {
      font-size: 32px;
      color: #fff;
      text-transform: capitalize;
    }




  }

}

.mostUnique {
  display: flex;
  gap: 87px;
  padding: 100px 0 0;

  .leftSection {
    width: 50%;
  }

  .rightSection {
    width: 50%;
  }

  .image {
    text-align: center;
    margin-bottom: 20px;
  }

  .content {

    h2 {
      color: #D2AB66;
    }
  }

}

.collectionSection {
  display: flex;
  padding-top: 110px;

  .image {
    width: 50%;
    padding-right: 40px;
  }

  .gold {
    width: 50%;
    text-align: center;
  }

  .imageLine {
    position: relative;
  }

  .imageLine:before {
    content: "";
    position: absolute;
    right: -40px;
    top: -40px;
    width: 100%;
    height: 100%;
    border: 3px solid #D2AB66;
    z-index: -1;
  }

  .name {
    font-size: 20px;
    font-weight: 500;
    padding: 30px 0 25px;
  }

  .text {
    font-size: 20px;
  }
}

.collectionSecond {
  display: flex;
  padding-top: 90px;
  gap: 91px;

  .collections {
    width: 60%;
    display: flex;
  }

  .image {
    width: 33.33%;
    //padding-right: 40px;
    padding-bottom: 40px;

    //padding-left: 55px;
    img {
      width: 100%;
    }
  }

  .imageLine {
    position: relative;
  }

  .imageLine:before {
    content: "";
    position: absolute;
    right: -40px;
    bottom: -40px;
    width: 100%;
    height: 100%;
    border: 3px solid #D2AB66;
    z-index: -1;
  }

  .gold {
    text-align: center;
    padding-right: 10px;

    img {
      object-fit: contain;
      height: 200px;
    }
  }


  .name {
    font-size: 20px;
    font-weight: 500;
    padding: 30px 0 25px;
  }

  .text {
    font-size: 20px;
  }
}

.bannerAd {
  margin-top: 100px;

  img {
    width: 100%;
  }
}


.bookEnquire {
  display: flex;
  margin: 40px -20px 0px;
  justify-content: space-between;

  h3 {
    color: #fff;
    margin-bottom: 20px;
    z-index: 1;
  }

  p {
    margin-bottom: 30px;
    z-index: 1;
  }

  a {
    width: 50%;
    display: flex;
    margin: 0 20px;
    background-repeat: no-repeat;
    background-size: cover;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: #fff;
    text-align: center;
    padding: 50px 7%;
    position: relative;

    &:first-child:before {
      content: "";
      background-color: #25675dcc;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;

    }

    &:hover:first-child:before {
      background-color: #25675dd8;
      transition: ease .3s;
    }

    &:last-child:before {
      content: "";
      background: rgba(159, 113, 30, 0.67);
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    &:hover:last-child:before {
      background: rgba(159, 114, 30, 0.774);
      transition: ease .3s;
    }
  }

  .join {
    color: #29635A;
    background-color: #fff;
    padding: 18px 70px;
    z-index: 1;
  }

  .book {
    color: #D2AB66;
    background-color: #fff;
    padding: 18px 70px;
    z-index: 1;
  }
}

.weHelp {
  margin: 100px 0;

  .box {
    display: flex;
    max-width: 80%;
    margin: 0 auto;
    z-index: 9;
    background: #fff;
  }


  .rightSide {
    padding: 40px;
    width: 55%;
    position: relative;
    background-color: #fff;
    border: 1px solid #BBBBBB;

    .head {
      font-size: 20px;
      padding-bottom: 30px;
    }

    p {
      text-align: center;
      font-size: 14px;
      margin-bottom: 15px;
    }

    .field {
      width: 100%;
      padding-bottom: 5px;

      div {
        padding-bottom: 10px;
        font-size: 14px;
        color: #787878;
        font-weight: 500;
      }
    }

    .check {
      text-align: center;
      margin-bottom: 10px;
      margin-top: 35px;

      span {
        padding-left: 10px;
        font-size: 13px;
      }

      a {
        color: #CB9F52;
        text-decoration: underline;
      }
    }


    input[type=text],
    input[type=url],
    input[type=tel],
    input[type=email],
    input[type=password],
    input[type=number],
    select,
    textarea,
    input[type=date] {
      border: 1px solid #D5DBDE;
      padding: 20px 16px;
    }

    // .continue {
    //   margin: 20px 0;
    //   width: 100%;
    //   text-align: center;
    //   font-size: 12px;
    //   position: relative;

    //   &::before {
    //     content: "";
    //     position: absolute;
    //     width: 100%;
    //     top: 50%;
    //     border-bottom: 1px solid #ddd;
    //     left: 0;
    //   }

    //   span {
    //     background: #fff;
    //     z-index: 4;
    //     position: relative;
    //     padding: 0 12px;
    //   }
    // }


    // .log {
    //   display: flex;
    //   width: 100%;
    //   justify-content: space-between;

    //   a {
    //     padding: 10px 30px;
    //     border: 1px solid #D5DBDE;
    //     width: 48%;
    //     text-align: center;
    //   }
    // }
    .bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      font-size: 12px;
      padding-top: 30px;

      .resend {
        cursor: pointer;
      }
    }

  }

  .close {
    position: absolute;
    right: -24px;
    top: -8px;
    cursor: pointer;
    opacity: unset;
  }

  .leftSide {
    width: 45%;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
}



@media screen and (max-width:1200px) {
  .bookEnquire {
    display: flex;
    flex-wrap: wrap;
    margin: 30pxpx -20px 0;

    a {
      width: 100%;
      margin-bottom: 20px;
    }
  }
}

@media screen and (max-width: 992px) {

  .mostUnique {
    padding: 50px 0 0;
  }

  .collectionSection {

    .text {
      font-size: 14px;
    }

    .name {
      font-size: 16px;
    }

    .gold img {
      width: 170px;
    }
  }


  .collectionSecond {
    .name {
      font-size: 14px;
    }

    .text {
      font-size: 14px;
    }
  }

  .bannerAd {
    margin-top: 60px;
  }

  .weHelp {
    margin: 50px 0;

    .leftSide {
      display: none;
    }

    .rightSide {
      width: 100%;
    }
  }
}

@media screen and (max-width: 768px) {

  .adBanner {
    .content {
      left: 2%;

      h1 {
        font-size: 30px;
      }

      h3 {
        font-size: 22px;
      }
    }
  }

  .mostUnique {
    flex-wrap: wrap;
    gap: 60px;

    .image {
      width: 170px;
      margin: 0 auto 20px;
    }

    .content {
      text-align: center;
    }
    .leftSection{
      width: 100%;
    }
    .rightSection {
      text-align: center;
      width: 100%;
    }
  }

  .collectionSection {
    flex-wrap: wrap;

    .image {
      width: 200px;
      margin: 0 auto;
    }

    .gold {
      width: 100%;
      margin-top: 20px;
    }
  }

  .collectionSecond {
    gap: 2%;

    .collections {
      width: 100%;
      flex-wrap: wrap;
    }

    .image {
      padding: 0;
      margin: 0 auto;
    }

    .gold {
      margin-bottom: 30px;
    }
  }
}

@media screen and (max-width: 640px) {
  .weHelp .box {
    max-width: 100%;

    .rightSide {
      padding: 20px;
    }
  }

  .collectionSecond {
    gap: 30px;
    flex-wrap: wrap;

    .image {
      width: 100%;
    }
  }
}