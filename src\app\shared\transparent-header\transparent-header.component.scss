.jumping-dots-loader {
    width: 30px;
    height: 20px;
    position: absolute;
    transform: translate(-50%, -50%);
    left: 50%;
    top: 50%;
    bottom: 0;
    border-radius: 100%;
    /* position: relative; */
    margin: 0 auto;
}

.jumping-dots-loader span {
    display: inline-block;
    width: 5px;
    height: 5px;
    border-radius: 100%;
    background-color: rgb(250, 246, 247);
    margin: 5px 2px;
}

.jumping-dots-loader span:nth-child(1) {
    animation: bounce 1s ease-in-out infinite;
}

.jumping-dots-loader span:nth-child(2) {
    animation: bounce 1s ease-in-out 0.33s infinite;
}

.jumping-dots-loader span:nth-child(3) {
    animation: bounce 1s ease-in-out 0.66s infinite;
}

@keyframes bounce {

    0%,
    50%,
    100% {
        -webkit-transform: translateY(0);
        -ms-transform: translateY(0);
        -o-transform: translateY(0);
        transform: translateY(0);
    }

    25% {
        -webkit-transform: translateY(-3px);
        -ms-transform: translateY(-3px);
        -o-transform: translateY(-3px);
        transform: translateY(-3px);
    }
}





.header-top {
    padding: 10px 0;
    background: #F3F3F3;
    font-size: 12px;

    .section {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .leftSection {
        display: flex;
        width: 50%;
        align-items: center;


        .blocks {
            display: flex;
            align-items: center;
            padding-right: 20px;
            font-weight: normal;
            color: #D2AB66;
        }

        .blocks span {
            padding-left: 5px;
        }

    }

    .rightSection {
        width: 50%;
        text-align: right;
        display: flex;
        align-items: center;
        justify-content: flex-end;

        .phone {
            font-weight: 600;
            display: flex;
            align-items: center;

            span {
                padding-right: 5px;
            }
        }

        .price {
            padding-left: 40px;
            display: flex;
        }

        .flag {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .goldSwipe {
            width: 140px;
        }

        .rate {
            font-weight: 600;
            padding-left: 6px;
        }
    }
}

.header {
    box-shadow: 2px 2px 22px rgb(0 0 0 / 6%);
}

.menuSection {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    padding: 13px 0 34px;

    .leftSection {
        width: 40%;
        padding-right: 48px;

        .leftTop {
            position: relative;
            width: 90%;

            input[type="text"] {
                margin-bottom: 0;
                padding: 12px 95px 12px 2px;
            }
        }

        .photo {
            position: absolute;
            right: 50px;
            top: 5px;
            cursor: pointer;
            padding: 5px 10px;
        }

        .mike {
            position: absolute;
            right: 25px;
            top: 5px;
            cursor: pointer;
            padding: 5px 10px;
        }

        .search {
            position: absolute;
            right: 0;
            top: 5px;
            padding: 5px 0px 5px 10px;
            cursor: pointer;
        }
    }

    .menus {
        padding-top: 20px;
        font-size: 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;


        .menu {
            padding: 10px 0px 5px 0px;
            border-bottom: 1px solid #fff;

            &:hover {
                border-bottom: 1px solid #D2AB66;
                font-weight: 600;
                color: #D2AB66;
            }
        }

    }

    .logo {
        text-align: center;
        width: 250px;
        padding: 0 0px 8px;
    }

    .rightSection {
        text-align: right;
        width: 40%;
        padding-left: 48px;

        .rightTop {
            display: flex;
            justify-content: flex-end;
            align-items: center;
        }

        .digitalWear {
            color: #10347F;
            font-size: 12px;
            display: flex;
            align-items: center;
            padding-right: 10px;
            font-weight: 500;

            span {
                padding-left: 10px;
            }
        }

        .loginAccount,
        .wishlist {
            padding: 5px 27px;
            display: flex;
            align-items: center;

            &.active {
                position: relative;

                .dot {
                    background: #D2AB66;
                    width: 6.5px;
                    height: 6.5px;
                    border-radius: 50%;
                    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
                    position: absolute;
                    right: 25px;
                    top: 6px;
                    border: 0.5px solid #FFFFFF;
                }
            }

        }

        .cart {
            padding: 5px 0 5px 27px;
            display: flex;

            &.active {
                position: relative;

                .dot {
                    background: #D2AB66;
                    width: 6.5px;
                    height: 6.5px;
                    border-radius: 50%;
                    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
                    position: absolute;
                    right: -2px;
                    top: 6px;
                    border: 0.5px solid #FFFFFF;
                }
            }
        }
    }
}

.dropSerach {
    background: #fff;
    padding: 20px 17px 6px;
    box-shadow: 0 0 2px #0000002e;
    position: absolute;
    z-index: 5;
    height: 228px;
    overflow: auto;
    width: 100%;

    &::-webkit-scrollbar {
        width: 0;
    }

    .searchItem {
        display: flex;
        align-items: center;
        padding-bottom: 20px;
        cursor: pointer;
    }

    .image {
        width: 57px;
        height: 57px;
    }

    .content {
        width: calc(100% - 57px);
        padding-left: 22px;
        font-size: 12px;
    }

    .image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .price {
        font-weight: 600;
        padding-top: 2px;
    }
}

.menu:hover .subMenus {
    display: block;
    left: 0;
    z-index: 3;
}

.subMenus {
    padding: 50px 0;
    position: absolute;
    width: 100%;
    height: auto;
    display: none;
    background: #fff;
    left: 0;
    top: 151px;

    .watchesSub {
        //display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 0 -10px;
    }

    .watchesSub a {
        border: 1px solid #1D1D1D14;
        width: 150px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        margin: 0 10px;
        font-weight: 400;
        overflow: hidden;

        img {
            transition: ease .5s;
        }

        &:hover img {
            transform: scale(.8);
            transition: ease .5s;
        }
    }

    .menuSub {
        display: flex;
        justify-content: space-between;
    }

    .leftLinks {
        display: flex;
        width: 740px;
        max-width: 100%;


        ul {
            list-style: none;
            padding: 0;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            align-content: flex-start;

            li {
                width: 25%;
            }
        }

        a {
            padding: 0px 15px 15px 0;
            width: 100%;
            font-weight: 400;

            &:hover {
                color: #D2AB66;
            }
        }

        .view {
            font-weight: 600;
        }
    }


    .rightImage {
        display: flex;
        flex-direction: row;
        width: 50%;
        justify-content: flex-end;

        a {
            margin-left: 30px;
            overflow: hidden;

            img {
                transition: ease .9s;
            }

            &:hover img {
                transform: translateX(5px);
                transition: ease .9s;
            }
        }
    }
}

.book {
    transform: rotate(90deg);
    position: fixed;
    right: -90px;
    background: linear-gradient(90.18deg, #D2AB66 -3.26%, #CB9F52 99.23%);
    color: #fff;
    padding: 12px 35px;
    top: 50%;
    font-size: 12px;
    z-index: 2;
}

.registerPopup {
    position: fixed;
    width: 100%;
    height: 100%;
    background: #00000040;
    top: 0;
    left: 0;
    z-index: 99;

    .box {
        display: flex;
        max-width: 820px;
        width: 95%;
        margin: 0 auto;
        box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
        position: fixed;
        top: 50%;
        left: 50%;
        z-index: 9;
        background: #fff;
        transform: translate(-50%, -50%);
        overflow: hidden;
    }

    &.show {
        display: flex !important;
    }

    &.hide {
        display: none;
    }


    .rightSide {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        align-content: center;
        padding: 30px;
        width: 55%;
        position: relative;
        background-color: #fff;

        .head {
            text-align: center;
            font-size: 20px;
            font-weight: 600;
            padding-bottom: 10px;
        }

        p {
            text-align: center;
            font-size: 14px;
            margin-bottom: 15px;
        }

        .field {
            width: 100%;

            &.country {
                position: relative;

                select {
                    position: absolute;
                    left: 0;
                    width: 80px;
                    margin-bottom: 0;
                    height: 82%;
                    top: 0;
                    border: none !important;
                    padding: 0 10px !important;
                }
            }


        }

        .check {
            text-align: center;
            margin-bottom: 10px;
            margin-top: 35px;

            span {
                padding-left: 10px;
                font-size: 13px;
            }

            a {
                color: #CB9F52;
                text-decoration: underline;
            }
        }


        input[type=text],
        input[type=url],
        input[type=tel],
        input[type=email],
        input[type=password],
        input[type=number],
        select,
        textarea,
        input[type=date] {
            border: 1px solid #D5DBDE;
            padding: 20px 16px;
        }

        input[type=tel] {
            padding-left: 90px !important;
        }

        // .continue {
        //   margin: 20px 0;
        //   width: 100%;
        //   text-align: center;
        //   font-size: 12px;
        //   position: relative;

        //   &::before {
        //     content: "";
        //     position: absolute;
        //     width: 100%;
        //     top: 50%;
        //     border-bottom: 1px solid #ddd;
        //     left: 0;
        //   }

        //   span {
        //     background: #fff;
        //     z-index: 4;
        //     position: relative;
        //     padding: 0 12px;
        //   }
        // }


        // .log {
        //   display: flex;
        //   width: 100%;
        //   justify-content: space-between;

        //   a {
        //     padding: 10px 30px;
        //     border: 1px solid #D5DBDE;
        //     width: 48%;
        //     text-align: center;
        //   }
        // }
        .bottom {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
            font-size: 12px;
            padding-top: 30px;

            .resend {
                cursor: pointer;
            }
        }

    }

    .close {
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
        opacity: unset;
        padding: 10px;
        z-index: 1;

        img {
            filter: invert(100%);
        }
    }

    .leftSide {
        width: 45%;

        img {
            height: 100%;
            width: 100%;
            object-fit: cover;
        }
    }

}

.headerMobile,
.mobMenu {
    display: none;
}


.header-home {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9;
    box-shadow: none;
    background: linear-gradient(180deg, #0000008a, #00000000);
    backdrop-filter: blur(3px);

    .menuSection {
        .menus {

            .menu {
                color: #fff;
                border-bottom: 1px solid transparent;
                font-weight: 400;
                text-transform: uppercase;

                &.active,
                &:hover {

                    border-bottom: 1px solid #D2AB66;
                }

                .subMenus {
                    top: 111px;
                    background: #00000036;

                    .leftLinks a {
                        color: #fff;

                        &:hover {
                            color: #D2AB66;
                        }
                    }
                }
            }
        }

        .leftSection {
            .search {
                filter: invert(100%);
            }

            input[type=text] {
                color: #fff;

                &::placeholder {
                    opacity: 1;
                    color: #BBBBBB;
                }
            }
        }

        .rightSection .rightTop {
            img {
                filter: invert(100%);
            }
        }
    }


}




@media screen and (max-width:1300px) {
    .menuSection .rightSection {
        padding-left: 10px;
    }

    .menuSection .leftSection {
        padding-right: 10px;
    }

}

@media screen and (max-width:1199px) {

    .header-top,
    .header {
        display: none;
    }

    .headerMobile {
        display: block;

        .mobileMenu {
            padding: 10px 20px 0px 0;
            display: flex;
        }

        .goldSwipe {
            text-align: center;
            background: #F3F3F3;
            padding: 10px 0;
            font-size: 14px;
        }

        .swiper-slide {
            display: flex;
            justify-content: center;
        }

        .flag {
            margin-top: -5px;
            margin-right: 5px;
        }

        .cart {
            padding: 10px 0 0px 20px;
            display: flex;
        }

        .mobileHeader {
            display: flex;
            justify-content: space-between;
            padding: 20px 0;
            align-items: flex-end;
        }

        .logo {
            width: 200px;
        }

        .searchSection {
            position: relative;
            margin: 10px 0;

            input[type="text"] {
                margin-bottom: 0;
                border: 1px solid #D9D9D9;
                padding: 13px 105px 13px 13px;
                border-radius: 10px;
            }

            .photo {
                position: absolute;
                right: 53px;
                top: 3px;
                cursor: pointer;
                padding: 5px 10px;
            }

            .mike {
                position: absolute;
                right: 28px;
                top: 3px;
                cursor: pointer;
                padding: 5px 10px;
            }

            .search {
                position: absolute;
                right: 13px;
                top: 3px;
                padding: 5px 0px 5px 10px;
                cursor: pointer;
            }
        }

    }



    .headerMobilehome {
        left: 0;
        position: absolute;
        top: 0;
        z-index: 99;
        width: 100%;

        .mobileHeader {
            padding-top: 47px;
        }

        .cart {
            img {
                filter: invert(100%);
            }
        }
    }


    .price {
        padding: 10px 0;
        background: #F3F3F3;
    }

    .priceToday {
        display: flex;
        font-size: 14px;
        justify-content: center;
    }

    .mobMenu {
        position: absolute;
        left: -1000%;
        top: 0;
        height: 1000%;
        width: 100%;
        display: flex;
        transition: ease .5s;

        &.active {
            left: 0;
            transition: ease .9s;
        }

        .menu {
            width: 80%;
            background: #fff;
            z-index: 9;
        }

        .shade {
            width: 20%;
            background: #00000026;
        }

        // accordion
        button.faq-accordion {
            cursor: pointer;
            padding: 15px 70px 15px 0px;
            width: 100%;
            border: none;
            text-align: left;
            outline: none;
            transition: 0.4s;
            background-color: transparent;
            font-size: 14px;
            position: relative;
            font-weight: 600;
        }

        button.faq-accordion::before {
            content: "";
            position: absolute;
            right: 20px;
            top: 0;
        }

        /*button not active*/
        button.faq-accordion:after {
            content: "";
            background-image: url(/assets/images/mobile/plus.svg);
            background-repeat: no-repeat;
            background-size: contain;
            width: 12px;
            height: 12px;
            position: absolute;
            right: 0px;
            top: 20px;
        }

        /* minus button */
        button.faq-accordion.active:after {
            background-image: url(/assets/images/mobile/minus.svg);
            height: 2px;
            top: 23px;
        }

        .option {
            display: block;
            padding-bottom: 15px;
        }

        .course-panel {
            background-color: transparent;
            max-height: 0;
            overflow: hidden;
            width: 0;
            line-height: 1.6em;
            letter-spacing: 0.4px;
            font-weight: 400;
            font-style: normal;
            display: flex;
            flex-direction: column;

            a {
                padding: 0px 0 10px 0;
                font-size: 14px;
            }

            p {
                padding: 0 50px 30px 20px;
                font-size: 14px;
            }

            &.open-accordion {
                max-height: max-content;
                width: 100%;
            }
        }

        .drops {
            margin: 0 15px;
            border-bottom: 1px solid #DDDDDD;
        }

        .drops:last-child .course-panel {
            border: none;
        }

        .secondSec {
            display: none;
            border-top: 1px solid #14264B26;
        }

        .secondSec.show {
            display: block;
        }

        .account {
            padding: 40px 15px 20px;
            display: flex;
            align-items: center;
        }

        .image {
            //  overflow: hidden;
            border-radius: 50%;
            object-fit: cover;
            height: 47px;
            width: 47px;
            background-color: wheat;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: x-large;
        }

        .detail {
            width: calc(100% - 47px);
            padding-left: 15px;
            color: #1D1D1D;
            font-weight: 500;
        }

        .name {
            font-size: 16px;
        }

        .mobileno {
            font-size: 12px;
        }

        .link {
            display: flex;
            padding: 10px 15px;
            align-items: center;
            font-size: 14px;

            &:hover {
                background: #D2AB66;
                color: #fff;

                path {
                    stroke: #fff;
                }
            }

            &.digital:hover path {
                fill: #fff;
                stroke: #fff;
            }

            &.design:hover path {
                fill: #fff;
                stroke: none;
            }
        }
    }

    .menuLink {
        padding-left: 10px;
    }

    .bottom {
        display: flex;
        padding: 0 15px;
        margin: 15px 0;

        a {
            padding: 10px;
            border: 1px solid #D5DBDE;
            border-radius: 6px;
            margin-right: 25px;
            display: flex;
        }
    }

}

@media screen and (max-width:768px) {
    .registerPopup.show {
        display: flex;
        flex-direction: column;
        width: 100%;

        .leftSide {
            display: none;
        }

        .check {
            margin-bottom: 10px;
        }

        .rightSide {
            width: 100%;
            padding: 20px;
        }

        .box {
            max-width: 100%;
            width: 80%;
        }
    }
}