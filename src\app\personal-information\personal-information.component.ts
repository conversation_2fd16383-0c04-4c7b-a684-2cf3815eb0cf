import { Component, OnInit } from '@angular/core';
import { NgbInputDatepickerConfig } from '@ng-bootstrap/ng-bootstrap';
import { DatepickerOptions } from 'ng2-datepicker';
import { getYear } from 'date-fns';
import locale from 'date-fns/locale/en-US';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from './../Services/api.service';
import * as moment from 'moment';
@Component({
  selector: 'app-personal-information',
  templateUrl: './personal-information.component.html',
  styleUrls: ['./personal-information.component.scss'],
  providers: [NgbInputDatepickerConfig],
})
export class PersonalInformationComponent implements OnInit {
  date = new Date();
  dateNew = new Date();
  dateString: any;
  activecountry: Boolean = false;
  activeState: Boolean = false;
  activecity: Boolean = false;

  isEditProfile: boolean = false;
  isProfile: boolean = true;

  country!: string;
  state!: string;
  city!: string;
  profileData: any;
  profileForm!: FormGroup;
  addressForm!: FormGroup;
  editAddressForm!: FormGroup;
  isAddress: boolean = true;
  addAddress: boolean = false;
  editAddress: boolean = false;
  submitted: boolean = false;

  options: DatepickerOptions = {
    minYear: getYear(new Date()) - 30, // minimum available and selectable year
    maxYear: getYear(new Date()) + 30, // maximum available and selectable year
    placeholder: ' ', // placeholder in case date model is null | undefined, example: 'Please pick a date'
    format: 'LLLL do yyyy', // date format to display in input
    formatTitle: 'LLLL yyyy',
    formatDays: 'EEEEE',
    firstCalendarDay: 0, // 0 - Sunday, 1 - Monday
    locale: locale, // date-fns locale
    position: 'bottom',
    inputClass: '', // custom input CSS class to be applied
    calendarClass: 'datepicker-default', // custom datepicker calendar CSS class to be applied
    scrollBarColor: '#dfe3e9',
  };
  countries: any;
  states: any;
  addressData: any;
  editAddressData: any;
  deliveryCheck: any;
  isLoading: boolean = false;
  confirmPopup: boolean = false;
  deleteid: any;

  windowRef: any = window;

  constructor(
    private formBuilder: FormBuilder,
    private apiService: ApiService,
    private toast: ToastrService
  ) {}

  ngOnInit(): void {
    this.initProfileForm();
    this.initAddressForm();
    this.initeditAddressForm();
    this.getProfile();
    this.getCountries();
    this.getAddress();
  }

  getProfile() {
    // this.isLoading = true
    this.apiService.getData(apiEndPoints.GET_PROFILE).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.profileData = res?.Data;
        // this.isLoading = false
      }
    });
  }

  initProfileForm() {
    this.profileForm = this.formBuilder.group({
      fname: ['', [Validators.required]],
      mobile: [''],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      gender: ['male'],
      dob: [''],
      anniversary: [''],
      gst: [
        '',
        [
          Validators.pattern(
            '^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$'
          ),
        ],
      ],
    });
  }

  get pf() {
    return this.profileForm.controls;
  }

  initAddressForm() {
    this.addressForm = this.formBuilder.group({
      name: ['', Validators.required],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      code: ['91'],
      address: ['', [Validators.required]],
      country: ['', Validators.required],
      state: [{ value: '', disabled: true }, [Validators.required]],
      city: ['', Validators.required],
    });
  }

  get af() {
    return this.addressForm.controls;
  }

  submitAddress() {
    if (this.addressForm.invalid) {
      this.addressForm.markAllAsTouched();
      return;
    }
    let data = {
      name: this.addressForm.get('name')?.value,
      countrycode: this.addressForm.get('code')?.value,
      mobile: this.addressForm.get('mobile')?.value,
      country_id: this.addressForm.get('country')?.value,
      state_id: this.addressForm.get('state')?.value,
      city: this.addressForm.get('city')?.value,
      pincode: this.addressForm.get('pincode')?.value,
      address: this.addressForm.get('address')?.value,
    };
    // this.isLoading = true
    this.apiService
      .postData(apiEndPoints.ADD_ADDRESS, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address added Successfully');

          //Webengage Start
          let country = this.countries.find(
            (x: any) => x.id == data.country_id
          );
          let state = this.states.find((x: any) => x.id == data.state_id);
          this.windowRef.webengage.track('Delivery Address Added', {
            'Full Name': this.addressForm.get('name')?.value,
            country: country?.name,
            pincode: this.addressForm.get('pincode')?.value,
            state: state?.name,
            city: this.addressForm.get('city')?.value,
            Address: this.addressForm.get('address')?.value,
            'Mobile Number': this.addressForm.get('mobile')?.value,
          });
          //Webengage End

          window.location.reload();
          // this.isLoading = false
        } else {
          // this.isLoading = false
          this.toast.error('failed');
        }
      });
  }

  initeditAddressForm() {
    this.editAddressForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      code: [''],
      address: ['', [Validators.required]],
      country: ['', [Validators.required]],
      state: [[Validators.required]],
      city: ['', [Validators.required]],
    });
  }

  get eaf() {
    return this.editAddressForm.controls;
  }

  updateAddress() {
    if (this.editAddressForm.invalid) {
      this.editAddressForm.markAllAsTouched();
      return;
    }
    let data = {
      addressId: this.editAddressData?.id,
      name: this.editAddressForm.get('name')?.value,
      countrycode: this.editAddressForm.get('code')?.value,
      mobile: this.editAddressForm.get('mobile')?.value,
      country_id: this.editAddressForm.get('country')?.value,
      state_id: this.editAddressForm.get('state')?.value,
      city: this.editAddressForm.get('city')?.value,
      pincode: this.editAddressForm.get('pincode')?.value,
      address: this.editAddressForm.get('address')?.value,
    };
    // this.isLoading = true
    this.apiService
      .postData(apiEndPoints.EDIT_ADDRESS, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address updated Successfully');
          window.location.reload();
          // this.isLoading = false
        } else {
          // this.isLoading = false
          this.toast.error('failed');
        }
      });
  }

  updateProfile() {
    if (this.profileForm.invalid) {
      this.profileForm.markAllAsTouched();
      return;
    }
    let dob = this.profileForm.get('dob')?.value
      ? moment(this.profileForm.get('dob')?.value).format('YYYY-MM-DD')
      : this.profileForm.get('dob')?.value;
    let anniversary = this.profileForm.get('anniversary')?.value
      ? moment(this.profileForm.get('anniversary')?.value).format('YYYY-MM-DD')
      : this.profileForm.get('anniversary')?.value;

    let data = {
      name: this.profileForm.get('fname')?.value,
      mobile: this.profileForm.get('mobile')?.value,
      email: this.profileForm.get('email')?.value,
      gender: this.profileForm.get('gender')?.value,
      dob: dob,
      anniversary_date: anniversary,
      gst_no: this.profileForm.get('gst')?.value,
    };
    // this.isLoading = true
    this.apiService
      .postData(apiEndPoints.EDIT_PROFILE, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.isEditProfile = false;
          this.isProfile = true;
          this.isAddress = true;

          //Webengage Start
          // this.windowRef.webengage.track("User Signed Up", {
          //   "Email" : this.profileForm.get('email')?.value || "",
          //   "First Name" : this.profileForm.get('fname')?.value || "",
          //   "Last Name" : this.profileForm.get('fname')?.value || "",
          //   "Phone Number" : this.profileForm.get('mobile')?.value || "",
          //   "Date of Birth" : this.profileForm.get('dob')?.value || "",
          //   "Gender" : this.profileForm.get('gender')?.value || "",
          //   "Source" : "Website",
          // });

          this.windowRef.webengage.user.setAttribute('we_first_name', data.name);
          this.windowRef.webengage.user.setAttribute('we_email', data.email);
          this.windowRef.webengage.user.setAttribute('we_phone', `+91${data.mobile}`);
          if(data.dob) this.windowRef.webengage.user.setAttribute('we_birth_date', data.dob);

          this.windowRef.webengage.track('Edit Personal Info', {
            Name: this.profileForm.get('fname')?.value || '',
            Email: this.profileForm.get('email')?.value || '',
            Gender: this.profileForm.get('gender')?.value || '',
            DOB: this.profileForm.get('dob')?.value || '',
            'Anniversary Date':
              this.profileForm.get('anniversary')?.value || '',
            'GST Number': this.profileForm.get('gst')?.value || '',
            Source: 'Website',
          });

          //Webengage End

          // window.location.reload();
          // this.isLoading = false
        } else {
          // this.isLoading = false
          this.toast.error('error occured');
        }
      });
  }

  showProfile() {
    this.isProfile = false;
    this.isEditProfile = true;
    this.isAddress = false;

    // if (this.profileData?.anniversary_date == null) {
    //   this.profileForm.get('anniversary')?.reset()
    // } else {
    //   this.profileForm.get('anniversary')?.setValue(new Date(this.profileData?.anniversary_date))
    // }

    // if (this.profileData?.dob == null) {
    //   this.profileForm.get('dob')?.reset()
    // } else {
    //   this.profileForm.get('dob')?.setValue(new Date(this.profileData?.dob))
    // }

    this.profileForm.get('mobile')?.disable();
    this.profileForm.get('fname')?.setValue(this.profileData?.name);
    this.profileForm.get('mobile')?.setValue(this.profileData?.mobile);
    this.profileForm.get('email')?.setValue(this.profileData?.email);
    this.profileForm.get('gender')?.setValue(this.profileData?.gender);
    // this.profileForm.get('dob')?.setValue(new Date(this.profileData?.dob));
    this.profileForm.get('gst')?.setValue(this.profileData?.gst_no);
    this.profileForm.get('dob')?.setValue(this.profileData?.dob);
    this.profileForm
      .get('anniversary')
      ?.setValue(this.profileData?.anniversary_date);
  }

  showAddAdress() {
    this.isProfile = false;
    this.isAddress = false;
    this.addAddress = true;
  }

  showEditAdress(id: any) {
    this.isProfile = false;
    this.isAddress = false;
    this.editAddress = true;
    let data = { addressId: id };
    this.apiService
      .postData(apiEndPoints.GET_SINGLE_ADDRESS, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.editAddressData = res?.Data;
          let countryid = { countryId: this.editAddressData?.country_id };
          this.apiService
            .postData(apiEndPoints.STATE, countryid)
            .subscribe((res: any) => {
              if (res?.ErrorCode == 0) {
                this.states = res?.Data;
              }
            });
          this.editAddressForm
            .get('name')
            ?.setValue(this.editAddressData?.name),
            this.editAddressForm
              .get('code')
              ?.setValue(this.editAddressData?.countrycode),
            this.editAddressForm
              .get('mobile')
              ?.setValue(this.editAddressData?.mobile),
            this.editAddressForm
              .get('country')
              ?.setValue(this.editAddressData?.country_id),
            this.editAddressForm
              .get('state')
              ?.setValue(this.editAddressData?.state_id),
            this.editAddressForm
              .get('city')
              ?.setValue(this.editAddressData?.city),
            this.editAddressForm
              .get('pincode')
              ?.setValue(this.editAddressData?.pincode),
            this.editAddressForm
              .get('address')
              ?.setValue(this.editAddressData?.address);
        }
      });
  }

  cancel(type: any) {
    switch (type) {
      case 'profile':
        this.isEditProfile = false;
        this.isProfile = true;
        this.isAddress = true;
        break;
      case 'address':
        this.addAddress = false;
        this.isProfile = true;
        this.isAddress = true;
        break;
      case 'editAddress':
        this.editAddress = false;
        this.isProfile = true;
        this.isAddress = true;
        break;
    }
  }

  getCountries() {
    this.apiService.getData(apiEndPoints.COUNTRY).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.countries = res?.Data;
      }
    });
  }

  selectedCountry(event: any, type: any) {
    const countryId = event.target.value;
    let data = { countryId: countryId };
    this.apiService.postData(apiEndPoints.STATE, data).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.states = res?.Data;
        if (type == 'add') this.addressForm?.get('state')?.enable();
      }
    });
  }

  getAddress() {
    this.apiService.getData(apiEndPoints.GET_ADDRESS).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.addressData = res?.Data;
      }
    });
  }

  deleteAddres() {
    let data = { addressId: this.deleteid };
    this.confirmPopup = false;
    this.apiService
      .postData(apiEndPoints.DELETE_ADDRESS, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success('Address Deleted Successfully');
          setTimeout(() => {
            window.location.reload();
          }, 500);
        } else this.toast.error('failed');
      });
  }

  setDefaultAddress(value: any) {
    let data = { addressId: value };
    this.apiService
      .postData(apiEndPoints.DEFAULT_ADDRESS, data)
      .subscribe((res: any) => {
        if (res.ErrorCode == 0) {
          this.getAddress();

          //Webengage Start
          let address = this.addressData.find((x: any) => x.id == value);

          this.windowRef.webengage.track('Delivery Address Selected', {
            'Full Name': address?.name,
            country: address?.country,
            pincode: address?.pincode,
            state: address?.state,
            city: address?.city,
            Address: address?.address,
            'Mobile Number': address?.mobile,
          });
          //Webengage End
        }
      });
  }

  checkPincode() {
    let pincode = this.addressForm.get('pincode')?.value;
    this.apiService
      .postData(apiEndPoints.DELIVERY_CHECK, { pincode: pincode })
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.warning(res?.Data?.message);
        }
      });
  }

  popupConfirm(id: any) {
    this.deleteid = id;
    this.confirmPopup = true;
  }
}
