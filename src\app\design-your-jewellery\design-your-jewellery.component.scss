.designJewellery {
    padding: 40px 0 100px;

    .form {
      display: flex;
      margin: 0 auto;
      width: 75%;
      justify-content: center;
      max-height: 820px;

      .leftSide {
        width: 50%;
      }

      .rightSide {
        padding: 30px 40px;
        border: 1px solid #BBBBBB;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 50%;
      }

      input,
      select,
      textarea {
        border: 1px solid #BBBBBB;
      }

      select {
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: url("/assets/images/angle-down.svg");
        background-repeat: no-repeat;
        background-position: 98%;

      }

      .field span {
        font-size: 14px;
        color: #787878;
        font-weight: 500;
        margin-bottom: 10px;
      }

      .field.time {
        .timePicker {
          position: relative;
          &::after {
            content: "";
            position: absolute;
            width: 24px;
            height: 24px;
            z-index: -1;
            right: 10px;
            top: 10px;
            background-image: url(/assets/images/clock.svg);
            background-repeat: no-repeat;
          }
        }


        input {
          cursor: pointer;
          display: block;
          width: 100%;
          padding: 10px;
          background: transparent;
          font-size: 12px;
          &:focus-visible {
            outline: none;
          }
        }
      }

      .leftSide img {
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }

    h1 {
      font-size: 44px;
      margin-bottom: 25px;

      @media screen and (max-width: 992px) {
        font-size: 30px;
        margin-bottom: 10px;
      }

      @media screen and (max-width: 480px) {
        font-size: 25px;
      }
    }

  }

  .uploading {
    width: 100%;
    height: 120px;
    border: 1px solid #D5DBDE;
    position: relative;
    margin-bottom: 30px;
    &::before{
        content: "";
        background-image: url(/assets/images/gold-scheme/upload.svg);
        width: 35px;
        height: 34px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        background-repeat: no-repeat;
        background-size: contain;
    }
    input[type="file"] {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        outline: none;
        opacity: 0;
    }
    .preview{
      max-height: 100%;
      max-width: 100%;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }

}

  // .ng-untouched {
  //   display: block;
  //   position: relative;

  //   &::after {
  //     content: "";
  //     position: absolute;
  //     width: 24px;
  //     height: 24px;
  //     z-index: -1;
  //     right: 10px;
  //     top: 10px;
  //     background-image: url(/assets/images/calander.svg);
  //     background-repeat: no-repeat;
  //   }
  // }

  .datepicker-container.datepicker-default {
    width: 100%;
  }


@media screen and (max-width:992px) {

  .designJewellery {
    padding: 20px 0 40px;

    .form{
      .leftSide {
        display: none;
      }

      .rightSide {
        width: 100%;
        padding: 20px;
      }
    }

  }
}
@media screen and (max-width:640px) {

  .designJewellery {
    .form{
      width: 90%;
    }

  }
}
