<!-- <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="square-jelly-box" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner>

<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div> -->

<div class="container">
  <div class="breadCrumb">
    <a routerLink="" class="link">Home</a>
    <a routerLink="/blog" class="link">Blogs</a>
    <div class="link">{{data?.blog_title}}</div>
  </div>
</div>

<div class="container">
  <div class="blogContent">
    <div class="leftSide">

      <span class="topSection">
        <div class="subHead" *ngFor="let item of data?.category">{{item}}</div>
        <h1>{{data?.blog_title}}</h1>
        <div class="bottom">
          <div class="tt">{{data?.author}}</div>
          <div class="date">{{data?.date}} </div>
        </div>
        <div class="topImage"><img class="blogImage" src="{{data?.blog_image}}" alt=""><span class="icon"
            *ngIf="data?.is_featured"><img src="\assets\images\blog\Group 21596.svg" alt=""></span></div>
        <div [innerHTML]="data?.blog_description"></div>
        <!-- <h6>dangle it long</h6>
                <p>If you are wearing a cotton saree, accentuate the looks with a long necklace. An antique necklace with coloured stones or intricate motifs would be a match for elegant sarees. You can also pair them with plain silk sarees. Bangles and a matching earring are the other essentials for this traditional look. </p> -->
      </span>
      <div class="share">
        <span>Share</span>
        <a href="#" class="social"><img src="\assets\images\blog\fb.png" alt=""></a>
        <a href="#" class="social"><img src="\assets\images\blog\twitter.png" alt=""></a>
        <a href="#" class="social"><img src="\assets\images\blog\pint.png" alt=""></a>
        <a href="#" class="social"><img src="\assets\images\blog\linked.png" alt=""></a>
      </div>
    </div>
    <div class="rightSide">
      <span class="search"><input type="text" placeholder="Search for your favorite product"> <span class="searching"><i
            class="fa fa-search"></i></span></span>
      <div class="recent">
        <h6>Recent Posts</h6>
        <ng-container *ngFor="let item of recentBlogs">
          <a [routerLink]="['/blog-details',item?.id]" class="post">
            <div class="postname">{{item?.title}}</div>
            <div class="date">{{item?.date}}</div>
          </a>
        </ng-container>
      </div>
      <div class="category">
        <h6>Categories</h6>
        <ng-container *ngFor="let item of blogCategories">
          <span class="cate">
            <div class="name">{{item?.title}}</div>
            <div class="count">{{item?.count}}</div>
          </span>
        </ng-container>
      </div>
    </div>
  </div>
  <div class="relatedPost">
    <h2>Related Posts</h2>
    <div class="related">
      <ng-container *ngFor="let item of data?.related_blog">
        <a [routerLink]="['/blog-details',item?.related_blog_id]" class="box">
          <div class="topImage"><img class="blogImage" src="{{item?.related_blog_image}}" alt="">
            <span class="icon" *ngIf="item?.is_featured"><img src="\assets\images\blog\Group 21596.svg" alt=""></span>
          </div>
          <div class="date">{{item?.date}}</div>
          <div class="subHead">{{item?.related_blog_title}} </div>
          <div class="name">{{item?.author}}</div>
        </a>
      </ng-container>
    </div>
  </div>
  <div class="commentSection">
    <div class="commentHead">
      <span class="icon"><img src="\assets\images\blog\Group 193.svg" alt=""></span>
      <div class="count">Comments ({{data?.comments_count}})</div>
    </div>
    <ng-container *ngFor="let item of data?.comments">
      <div class="comments">
        <div class="image"><img src="\assets\images\blog\Ellipse 123.png" alt=""></div>
        <div class="details">
          <div class="name">{{item?.user_name}}</div>
          <p>{{item?.comment}} </p>
          <div class="date">{{item?.date}}</div>
        </div>
      </div>
    </ng-container>
    <div class="leaveReply">
      <h2>Leave a Reply</h2>
      <form [formGroup]="commentForm">
        <textarea name="" id="" cols="30" rows="4" placeholder="Write your Review" formControlName="comment"></textarea>
        <div class="button">
          <div class="primary-button golden" (click)="postComment()">Post comment</div>
        </div>
      </form>
    </div>
  </div>
