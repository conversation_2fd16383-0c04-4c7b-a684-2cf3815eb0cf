import { ApiService } from './../../Services/api.service';
import {
  ChangeDetectorRef,
  Component,
  ElementRef,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgOtpInputComponent, NgOtpInputConfig } from 'ng-otp-input';
import SwiperCore, {
  Swiper,
  SwiperOptions,
  Virtual,
  Navigation,
  FreeMode,
  Pagination,
} from 'swiper';
SwiperCore.use([Virtual, FreeMode, Navigation, Pagination]);
import firebase from 'firebase/app';
import 'firebase/auth';
import 'firebase/firestore';
import { environment } from 'src/environments/environment.prod';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { Subscription, timer } from 'rxjs';
import { take } from 'rxjs/operators';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { CommonService } from 'src/app/Services/common.service';
import { PhoneNumberUtil } from 'google-libphonenumber';
import { password } from '../enums/auth.enum';
const conf = environment.firebaseConfig;
declare var webkitSpeechRecognition: any;
declare var SpeechRecognition: any;

@Component({
  selector: 'app-transparent-header',
  templateUrl: './transparent-header.component.html',
  styleUrls: ['./transparent-header.component.scss']
})
export class TransparentHeaderComponent implements OnInit {
  phoneNumber: any;
  mobile!: number;
  recapthaVerifier: any;
  selectedIndex: any;
  loginModal: boolean = false;
  authorizationModal: boolean = false;
  registerModal: any = false;
  otpModal: boolean = false;
  searchProduct: any;
  isSearchResults: boolean = false;
  loading: boolean = false;
  auth!: string;
  menuSlide: SwiperOptions = {
    spaceBetween: 0,
    autoplay: {
      delay: 2000,
    },
    slidesPerView: 6,
    freeMode: true,
    watchSlidesProgress: true,
    pagination: false,
    navigation: false,
  };

  watchSlide: SwiperOptions = {
    spaceBetween: 0,
    slidesPerView: 6,
    freeMode: false,
    watchSlidesProgress: true,
  };

  goldSlide: SwiperOptions = {
    autoplay: {
      delay: 2000,
    },
    spaceBetween: 0,
    slidesPerView: 1,
    freeMode: false,
    watchSlidesProgress: true,
  };

  mobilemenu: any = false;
  loginForm!: FormGroup;
  registerForm!: FormGroup;
  windowRef: any = window;
  verificationId: any;
  otp: any;
  countryCode: any;
  private timerSubscription!: Subscription;
  timeLeft: any = 20;
  isLogged: any;
  isVirtual = false;
  currentRoute: any;

  otpInputConfig: NgOtpInputConfig = {
    length: 6,
    allowNumbersOnly: true,
  };
  categories: any;
  watchBrands: any;
  imageBase: any;
  countries: any;
  searchResults: any;
  recognizedText: any;
  image: any;
  rates: any;
  showLoginModal: boolean = false;
  brands: any;
  logged: any;
  wishlistCount: any;
  cartCount: any;
  userId!: number;
  isMobile!: boolean;
  @ViewChild('recaptchaWrapperRef') recaptchaWrapperRef!: ElementRef;
  @ViewChild(NgOtpInputComponent) ngOtpInput!: NgOtpInputComponent;
  userData: any;
  isHomePage: boolean = false;

  constructor(
    private fb: FormBuilder,
    private apiService: ApiService,
    private router: Router,
    private toast: ToastrService,
    private renderer: Renderer2,
    private commonService: CommonService,
    private ref: ChangeDetectorRef
  ) {
    this.commonService.getClickEvent().subscribe(() => {
      this.loginModal = true;
      this.setStyle();
    });

    this.commonService.getLoginEvent().subscribe(() => {
      this.logged = true;
    });

    this.commonService.getLogoutEvent().subscribe(() => {
      this.logged = false;
    });

    this.commonService.getWishlistEvent().subscribe(() => {
      this.getWishlist();
    });

    this.commonService.getCartEvent().subscribe(() => {
      this.getcartcount();
    });

    this.commonService.getAuthorizationEvent().subscribe((res) => {
      if (res) {
        this.authorizationModal = true;
        this.setStyle();
      }
    });

    this.commonService.checkMobileEvent().subscribe((res) => {
      this.isMobile = JSON.parse(localStorage.getItem('ismObile') || '');
    });
  }

  ngOnInit(): void {
    this.router.events.subscribe((value: any) => {
      let array = window.location.pathname.split('/');
      this.currentRoute = array[array.length - 1];
      if (this.currentRoute == '') {
        this.isHomePage = true;
      }
      this.ref.detectChanges();
      // setTimeout(() => {
      //   if (!localStorage.getItem('isLogged') && this.currentRoute == '') {
      //     this.loginModal = true;
      //   }
      // }, 1000);
    });

    this.logged = localStorage.getItem('isLogged') ? true : false;

    if (this.logged) {
      this.apiService
        .getData(apiEndPoints.GET_PROFILE)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) {
            this.userData = res?.Data;
          }
        });
    }

    const script = document.createElement('script');
    script.innerHTML = `
      getSkusListWithTryOn({ companyName: 'Ttdevassyjewellery' })
      .then((skusList) => {

        addTryOnButton({ skusList });
      })
      function addTryOnButton({ skusList }) {
        loadTryOnButton({
          psku: { main: skusList[0].toString(), recommendedSkus: skusList.slice(0,20) },
          page: 'product',
          company: 'Ttdevassyjewellery',
          nonTryOnProductsReport: 'true',
          buynow: { enable: 'true' },
          prependButton: { class: 'digitalWear' },
          MBprependButton: { class: 'digitalWear' },
          styles: {
            tryonbutton: { backgroundColor: 'white', color: 'black', border: '1px solid black', borderRadius: '25px' },
            tryonbuttonHover: { backgroundColor: 'black', color: 'white', border: 'none', borderRadius: '25px' },
            MBtryonbutton: { width: '100%', borderRadius: '25px' }
          },
          tryonButtonData: { text: 'Digital wear', faIcon: 'fa fa-camera' },
        });
      };`;
    this.renderer.appendChild(document.body, script);

    this.initLoginForm();
    this.initRegisterForm();
    // firebase.initializeApp(conf);
    this.getCategories();
    this.getWatches();
    this.imageBase = environment.imageBase;
    this.getCountries();
    this.getGoldRate();
    this.getBrands();
    this.getWishlist();
    this.getcartcount();
  }

  getGoldRate() {
    this.apiService.getData(apiEndPoints.goldrates).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.rates = res?.Data;
      }
    });
  }

  initLoginForm() {
    this.loginForm = this.fb.group({
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      code: ['91', [Validators.required]],
    });
  }

  initRegisterForm() {
    this.registerForm = this.fb.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required]],
    });
  }

  getBrands() {
    this.apiService.getData(apiEndPoints.ttbrands).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.brands = res?.Data;
      }
    });
  }

  brandRedirection(type: any, id: any, title?: string) {
    // if (type == 1) this.router.navigate(['/detail', id]);
    // else if (type == 2 || 3 || 4) this.router.navigate(['/listing', type, id]);
    if (type == 1) this.router.navigate(['/detail', id]);
    else if (type == 2 || (type == 3 && title != 'Poyems') || type == 4)
      this.router.navigate(['/listing', type, id]);
    if (title == 'Zora') this.router.navigateByUrl('/zora-collection');
    if (title == 'Poyems') window.location.href = 'https://poyems.com/';

    if (type == 5 && id == 3) {
      this.router.navigateByUrl('/virtual-shopping');
    } else if (type == 5 && id == 4) {
      this.router.navigateByUrl('/zora-collection');
    } else if (type == 5 && id == 5) {
      this.router.navigateByUrl('/divo-collection');
    } else if (type == 5 && id == 6) {
      // this.router.navigateByUrl('/poyem-collection');
    } else if (type == 5 && id == 7) {
      this.router.navigateByUrl('/orlin-collection');
    } else if (type == 5 && id == 8) {
      this.router.navigateByUrl('/book-appointment');
    } else if (type == 5 && id == 2) {
      this.router.navigateByUrl('/gold-schemes');
    } else if (type == 5 && id == 1) {
      this.router.navigateByUrl('/advance-booking');
    } else if (type == 5 && id == 9) {
      this.router.navigateByUrl('/gift-voucher');
    } else if (type == 5 && id == 10) {
      this.router.navigateByUrl('/digital-gold');
    } else if (type == 5 && id == 11) {
      this.router.navigateByUrl('/about');
    }
  }

  onOtpChange(e: any) {
    this.otp = e;
  }

  popUp() {
    if (localStorage.getItem('isLogged') == 'true') {
      this.closeMenu();
      this.router.navigateByUrl('/personal-information');
    } else {
      this.loginModal = !this.loginModal;
      this.setStyle();
    }
  }

  popUpClose(key: any) {
    switch (key) {
      case 'login':
        this.loginModal = false;
        this.removeStyle();
        if (this.loginForm?.get('mobile')?.value)
          this.loginForm.get('mobile')?.reset();
        break;

      case 'otp':
        this.ngOtpInput.setValue('');
        this.otpModal = false;
        this.removeStyle();
        document.getElementById('recaptcha-container')?.remove();
        this.recaptchaWrapperRef.nativeElement.innerHTML = `<div id="recaptcha-container"></div>`;
        break;

      case 'register':
        this.registerModal = false;
        this.removeStyle();
        break;
    }
  }

  showAccordion(index: any) {
    if (this.selectedIndex == index) {
      this.selectedIndex = '';
    } else {
      this.selectedIndex = index;
    }
  }

  mobileShow() {
    this.mobilemenu = !this.mobilemenu;
  }

  closeMenu() {
    this.mobilemenu = false;
  }

  login() {
    if (!this.loginForm.get('mobile')?.value) {
      this.toast.error('Enter your number');
      return;
    }

    if (this.loginForm.invalid) {
      this.toast.error('must be a number');
      return;
    }

    this.loading = true;
    this.countryCode = '+' + this.loginForm.get('code')?.value;
    this.phoneNumber = this.loginForm.get('mobile')?.value;
    this.mobile = this.loginForm.get('mobile')?.value;
    const mobile = this.countryCode + this.phoneNumber;

    let isValid = this.isValidPhoneNumber(
      this.phoneNumber,
      this.loginForm.get('code')?.value
    );
    if (!isValid) {
      this.loading = false;
      this.toast.error('Invalid Number');
      return;
    }

    // this.windowRef = window;
    // this.windowRef.reCaptchaVerifier = new firebase.auth.RecaptchaVerifier(
    //   'recaptcha-container',
    //   {
    //     size: 'invisible',
    //     callback: (response: any) => {},
    //   }
    // );
    // this.windowRef.reCaptchaVerifier.render().then((widget: any) => {
    //   this.windowRef.recaptchaWidgetId = widget;
    // });
    // const appVerifier = this.windowRef.reCaptchaVerifier
    // firebase.auth().signInWithPhoneNumber(mobile, appVerifier).then((confirmationResult) => {
    //   this.verificationId = confirmationResult.verificationId;
    //   this.toast.success(`OTP successfully sent to ${mobile}`)
    //   this.popUpClose('login')
    //   this.otpModal = true;
    //   this.setStyle();
    //   this.loading=false;

    //   this.startTimer()
    // }).catch((error: any) => {
    //   this.loading=false
    //   this.loginForm.get('mobile')?.reset()
    //   this.windowRef.reCaptchaVerifier = null; // remove the reCAPTCHA container
    //   document.getElementById('recaptcha-container')?.remove();
    //   this.recaptchaWrapperRef.nativeElement.innerHTML = `<div id="recaptcha-container"></div>`
    //   // if (error.message == "TOO_SHORT" || "TOO_LONG")
    //   this.toast.error(error.message)
    // })
    const data = {
      mobile: this.mobile,
      country_code: this.countryCode,
    };
    this.apiService
      .verfifyMobileNumber(apiEndPoints.request_otp, data)
      .subscribe(
        (res) => {
          if (res.ErrorCode === 0) {
            this.loading = false;
            this.toast.success(`OTP successfully sent to ${mobile}`);
            this.userId = res.Data.user_id;
            this.popUpClose('login');
            this.otpModal = true;
            this.setStyle();
            this.startTimer();
            //webengage start
            this.windowRef.webengage.track('Login', {
              'Phone Number': this.countryCode + this.mobile,
            });
            //webengage end

            // this.windowRef.webengage.user.login('9SBOkLVMWvPX');
          } else {
            this.loading = false;
            this.toast.error('Somethingwent wrong', 'Error');
          }
        },
        (error) => {
          this.loading = false;
          this.loginForm.get('mobile')?.reset();
          this.windowRef.reCaptchaVerifier = null; // remove the reCAPTCHA container
          document.getElementById('recaptcha-container')?.remove();
          this.recaptchaWrapperRef.nativeElement.innerHTML = `<div id="recaptcha-container"></div>`;
          // if (error.message == "TOO_SHORT" || "TOO_LONG")
          this.toast.error(error.message, 'Error');
        }
      );
  }

  verifyOtp() {
    this.loading = true;

    const data = {
      user_id: this.userId,
      mobile: this.mobile,
      otp: this.otp,
      country_code: this.countryCode,
    };
    this.apiService.verifyOTP(apiEndPoints.verify_otp, data).subscribe(
      (res) => {
        if (res?.ErrorCode === 0) {
          this.loading = false;
          this.isLogged = 'true';
          localStorage.setItem('isLogged', this.isLogged);
          localStorage.setItem('token', res?.Data?.access_token);
          localStorage.setItem('userId', res?.Data?.user_id);
          this.popUpClose('otp');

          //Webengage Start
          this.windowRef.webengage.track('OTP Verified', {
            'Phone Number': this.countryCode + this.mobile,
          });

          this.windowRef.webengage.user.login(this.countryCode + this.mobile);
          this.windowRef.webengage.user.setAttribute('we_phone', this.countryCode + this.mobile);
          this.windowRef.webengage.user.setAttribute('we_whatsapp_opt_in', true);
          //Webengage End

          if (res?.Data?.step_count == 0) {
            this.registerModal = true;
            this.setStyle();
          }
          this.commonService.sendLoginEvent();
          this.commonService.sendWishlistEvent();
        } else {
          this.ngOtpInput.setValue('');
          this.loading = false;
        }
      },
      (error) => {
        this.ngOtpInput.setValue('');
        this.loading = false;
        this.toast.error(error, 'Failed');
      }
    );
  }

  resendOtp() {
    this.ngOtpInput.setValue('');
    this.timeLeft = 20;
    const data = {
      mobile: this.mobile,
      country_code: this.countryCode,
    };
    this.apiService
      .verfifyMobileNumber(apiEndPoints.request_otp, data)
      .subscribe(
        (res) => {
          if (res.ErrorCode === 0) {
            this.loading = false;
            this.toast.success(
              `OTP Resend successfully sent to ${this.mobile}`
            );
            this.userId = res.Data.user_id;
            // this.popUpClose('login');
            // this.otpModal = true;
            // this.setStyle();
            if (this.timerSubscription) {
              this.timerSubscription.unsubscribe();
            }
            this.startTimer();
            // this.windowRef.webengage.user.login('9SBOkLVMWvPX');
          } else {
            this.loading = false;
            this.toast.error('Somethingwent wrong', 'Error');
          }
        },
        (error) => {
          this.loading = false;
          this.loginForm.get('mobile')?.reset();
          // this.windowRef.reCaptchaVerifier = null; // remove the reCAPTCHA container
          // document.getElementById('recaptcha-container')?.remove();
          // this.recaptchaWrapperRef.nativeElement.innerHTML = `<div id="recaptcha-container"></div>`;
          // if (error.message == "TOO_SHORT" || "TOO_LONG")
          this.toast.error(error.message, 'Error');
        }
      );

    // this.phoneNumber = this.loginForm.get('mobile')?.value;
    // const mobile = this.countryCode + this.mobile;
    // const appVerifier = this.windowRef.reCaptchaVerifier;
    // firebase
    //   .auth()
    //   .signInWithPhoneNumber(mobile, appVerifier)
    //   .then((confirmationResult) => {
    //     this.verificationId = confirmationResult.verificationId;
    //     this.startTimer();
    //   })
    //   .catch((error: any) => {
    //     // this.toast.error(error.message)
    //   });
  }

  startTimer() {
    this.timerSubscription = timer(0, 1000)
      .pipe(take(this.timeLeft))
      .subscribe(() => {
        if (this.timeLeft > 0) {
          this.timeLeft -= 1;
        } else {
          this.timerSubscription.unsubscribe();
        }
      });
  }

  transform(value: number): string {
    const minutes: number = Math.floor(value / 60);
    return (
      ('00' + minutes).slice(-2) +
      ':' +
      ('00' + Math.floor(value - minutes * 60)).slice(-2)
    );
  }

  register() {
    if (this.registerForm.invalid) {
      return;
    }

    const name = this.registerForm.get('name')?.value;
    const email = this.registerForm.get('email')?.value;

    const data = {
      name: name,
      email: email,
    };

    this.apiService
      .postData(apiEndPoints.REGISTER, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.popUpClose('register');

          //webengage start
          this.windowRef.webengage.track('Registration', {
            'First Name': name,
            Email: email,
          });
          this.windowRef.webengage.user.setAttribute('we_first_name', name);
          this.windowRef.webengage.user.setAttribute('we_email', email);
          //webengage end
        } else {
          this.toast.error(res?.message);
        }
      });
  }

  redirect(key: any) {
    switch (key) {
      case 'wishlist':
        if (localStorage.getItem('isLogged'))
          this.router.navigateByUrl('/wishlist');
        else {
          this.loginModal = !this.loginModal;
          this.setStyle();
        }
        break;
      case 'cart':
        if (localStorage.getItem('isLogged'))
          this.router.navigateByUrl('/cart');
        else {
          this.loginModal = !this.loginModal;
          this.setStyle();
        }
        break;
    }
  }

  getCategories() {
    this.apiService
      .getData(apiEndPoints.GET_CATEGORIES)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.categories = res?.Data;
          const filter = this.categories.findIndex((i: any) => i.id === 7);
          this.categories.splice(filter, 1);
        }
      });
  }

  getWatches() {
    this.apiService.getData(apiEndPoints.WATCH_BRANDS).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.watchBrands = res?.Data;
      }
    });
  }

  getCountries() {
    this.apiService.getData(apiEndPoints.COUNTRY).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.countries = res?.Data;
      }
    });
  }

  serachProducts() {
    if (!this.searchProduct) {
      this.isSearchResults = false;
      return;
    }

    this.apiService
      .postData(apiEndPoints.SEARCH, { keyword: this.searchProduct })
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.searchResults = res?.Data;
          if (res?.Data?.items.length > 0) this.isSearchResults = true;
          else this.isSearchResults = false;

          //webengage
          this.windowRef.webengage.track('Product Searched', {
            'Search Keyword': this.searchProduct,
            'Product Count': Number(res?.Data?.items.length),
            Source: 'Website',
          });
          //webengage
        }
      });
  }

  searchRedirection(slug: any) {
    this.isSearchResults = false;
    this.router.navigate(['/detail', slug]);
    this.searchProduct = '';
  }

  openWhatsapp() {
    window.open('https://api.whatsapp.com/send?phone=9169160161', '_blank');
    this.router.navigate([''], { skipLocationChange: true });
  }

  getVoiceSearchResult() {
    this.apiService
      .postData(apiEndPoints.SEARCH, { keyword: this.recognizedText })
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.searchResults = res?.Data;
          this.ref.detectChanges();
          if (res?.Data?.items.length > 0) {
            this.isSearchResults = true;
          } else this.isSearchResults = false;
        }
      });
  }

  startListening() {
    if ('SpeechRecognition' in window) {
      const recognition = new SpeechRecognition();
      recognition.lang = 'en-US';
      recognition.start();
      recognition.onresult = (event: any) => {
        const result = event.results[0][0].transcript;
        this.recognizedText = result;
        this.isSearchResults = true;
        this.getVoiceSearchResult();
      };
      recognition.speechend = () => {
        recognition.end();
      };
    } else if ('webkitSpeechRecognition' in window) {
      const recognition = new webkitSpeechRecognition();
      recognition.lang = 'en-US';
      recognition.start();
      recognition.onresult = (event: any) => {
        const result = event.results[0][0].transcript;
        this.recognizedText = result;
        this.isSearchResults = true;
        this.getVoiceSearchResult();
      };
      recognition.speechend = () => {
        recognition.end();
      };
    } else {
    }
  }

  searchImage(event: any) {
    if (event.target.files.length > 0) {
      const reader = new FileReader();
      reader.readAsDataURL(event.target.files[0]);
      this.image = event.target.files[0];
      const formData = new FormData();
      formData.append('image', this.image);
      this.apiService
        .postData(apiEndPoints.image_search, formData)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) {
            this.searchResults = res?.Data;
            if (res?.Data?.items.length > 0) {
              this.isSearchResults = true;
            }
          } else this.isSearchResults = false;
        });
    }
  }

  redirection(page: any) {
    this.popUpClose('login');
    if (page == 'tnc') {
      this.router.navigateByUrl('/terms');
    } else if (page == 'privacy') {
      this.router.navigateByUrl('/privacy-policy');
    }
  }

  isValidPhoneNumber(phoneNumber: any, countryCode: any): boolean {
    let phoneNumberUtil = PhoneNumberUtil.getInstance();
    let regionCode = phoneNumberUtil.getRegionCodeForCountryCode(countryCode);

    try {
      let number = phoneNumberUtil.parseAndKeepRawInput(
        phoneNumber,
        regionCode
      );
      return phoneNumberUtil.isValidNumber(number);
    } catch (e) {
      return false;
    }
  }

  getWishlist() {
    this.apiService.getData(apiEndPoints.GET_WISHLIST).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.wishlistCount = res?.Data?.totalCount;
      }
    });
  }

  getcartcount() {
    this.apiService.getData(apiEndPoints.cartcount).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.cartCount = res?.Data?.cartCount;
      }
    });
  }

  authenticate() {
    if (this.auth.trim() === password.auth) {
      this.authorizationModal = false;
      this.removeStyle();
    }
  }

  setStyle() {
    this.renderer.setStyle(document.body, 'overflow', 'hidden');
  }

  removeStyle() {
    this.renderer.removeStyle(document.body, 'overflow');
  }

  categoryRedirection(
    type: any,
    slug: any,
    item: any,
    level: string,
    parent?: any,
    isClose?: boolean
  ) {
    if (type == 2) type = 'watches';
    else if (type == 3) type = 'collection';
    else if (type == 4) type = 'category';
    if (isClose) this.closeMenu();

    //Webengage Start
    if (level == 'L1') {
      this.windowRef.webengage.track('L1 Category Viewed', {
        'L1 Category ID': String(item?.id),
        'L1 Category Name': item?.name,
      });
    }
    if (level == 'L2') {
      this.windowRef.webengage.track('L2 Category Viewed', {
        'L1 Category ID': parent?.id,
        'L1 Category Name': parent?.name,
        'L2 Category ID': String(item?.id),
        'L2 Category Name': item?.name,
      });
    }
    //Webengage End

    this.router.navigate(['/listing', type, slug]);
  }

  silverRedirection() {
    let item = this.categories?.find((x: any) => x?.name == 'Silver');
    this.windowRef.webengage.track('L1 Category Viewed', {
      'L1 Category ID': item?.id,
      'L1 Category Name': item?.name,
    });

    this.router.navigate(['/listing/category/silver']);
  }

}
