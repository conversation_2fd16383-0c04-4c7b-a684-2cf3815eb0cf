{"$schema": "./node_modules/@angular/service-worker/config/schema.json", "index": "/index.html", "assetGroups": [{"name": "app", "installMode": "prefetch", "resources": {"files": ["/favicon.ico", "/index.html", "/*.css", "/*.js"]}}, {"name": "assets", "installMode": "lazy", "updateMode": "prefetch", "resources": {"files": ["/assets/**", "/*.(eot|svg|cur|jpg|png|webp|gif|otf|ttf|woff|woff2|ani)"]}}, {"name": "videos", "installMode": "lazy", "updateMode": "prefetch", "resources": {"urls": ["https://gziw1jnd2b.ufs.sh/f/**"]}}], "dataGroups": [{"name": "api", "urls": ["/api/**"], "cacheConfig": {"maxSize": 100, "maxAge": "1h", "timeout": "10s", "strategy": "freshness"}}, {"name": "videos", "urls": ["https://gziw1jnd2b.ufs.sh/f/**"], "cacheConfig": {"maxSize": 50, "maxAge": "7d", "strategy": "performance"}}]}