<div class="myAccount">
  <div class="container">
    <div class="title">
      <h1>My Account</h1>
    </div>
    <div class="contentSection">
      <div class="sideMenu">
        <app-sidebar></app-sidebar>
      </div>
      <div class="contents">
        <div class="head"><PERSON><PERSON> and <PERSON>arn</div>
        <div class="referSection">
          <div class="heading"><PERSON><PERSON> and <PERSON><PERSON>n</div>
          <p>Refer us to your friends or relatives to earn valuable discount points for your next purchase.</p>
          <div class="info">
            <div class="section">
              <span class="number">1</span>
              <span class="text">Select the occassion to refer </span>
            </div>
            <div class="section">
              <span class="number">2</span>
              <span class="text">Enter the details of person to refer</span>
            </div>
            <div class="section">
              <span class="number">3</span>
              <span class="text">Earn rewards for each successfull referal</span>
            </div>
          </div>
          <div class="select">
            <ng-container *ngFor="let item of ocassions">
              <div class="mainBox">
                <div class="box" [class.selected]="selectedOccasion === item?.id">
                  <input type="radio" class="radio" [(ngModel)]="selectedOccasion" [value]="item?.id">
                  <img src="{{item?.image}}" alt="occasion image">
                </div>
                <span class="text">{{item?.title}}</span>
              </div>
            </ng-container>
            <!-- <div class="box">
              <input type="radio" class="radio">
              <img src="\assets\images\referals\Group 21987.svg" alt="">
            </div>
            <div class="box">
              <input type="radio" class="radio">
              <img src="\assets\images\referals\Mobile inbox-pana.svg" alt="">
            </div>
            <div class="box">
              <input type="radio" class="radio">
              <img src="\assets\images\referals\Online calendar-pana.svg" alt="">
            </div> -->
          </div>
          <div class="button">
            <div class="primary-button golden" (click)="popUp()">Refer Now</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



<div class="referPopup" [ngClass]="Popuprefer?'show':'hide'">
  <div class="box">
    <form [formGroup]="referForm">
      <div class="close" (click)="popUpClose()"><img src="\assets\images\close-white.svg" alt="close icon"></div>
      <div>
        <input type="text" placeholder="Name" formControlName="name">
        <span *ngIf="rf.name.invalid && rf.name.touched">
          <span class="text-danger" *ngIf="rf.name.errors?.required" style="font-size: 12px;"> Name is required </span>
        </span>
      </div>
      <div>
        <input type="tel" placeholder="Mobile" formControlName="mobile">
        <span *ngIf="rf.mobile.invalid && rf.mobile.touched">
          <span class="text-danger" *ngIf="rf.mobile.errors?.required" style="font-size: 12px;"> Mobile Number is
            required </span>
        </span>
        <span class="text-danger" *ngIf="rf.mobile.errors?.pattern" style="font-size: 12px;">Please Enter Valid Mobile
          NUmber</span>
      </div>
      <div class="button">
        <div class="primary-button white" (click)="popUpClose()">Cancel</div>
        <div class="primary-button golden" (click)="sendReferal()">Submit</div>
      </div>
    </form>
  </div>
</div>
