import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GiftCardSuccessComponent } from './gift-card-success.component';

describe('GiftCardSuccessComponent', () => {
  let component: GiftCardSuccessComponent;
  let fixture: ComponentFixture<GiftCardSuccessComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ GiftCardSuccessComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(GiftCardSuccessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
