import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';

@Component({
  selector: 'app-cancelation-policy',
  templateUrl: './cancelation-policy.component.html',
  styleUrls: ['./cancelation-policy.component.scss']
})
export class CancelationPolicyComponent implements OnInit {
  data: any;

  constructor(private ApiService:ApiService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.CANCELLATION_POLICY).subscribe((res:any) => {
      if(res?.errorcode == 0) {
        this.data = res?.data
      }
    })
  }

}
