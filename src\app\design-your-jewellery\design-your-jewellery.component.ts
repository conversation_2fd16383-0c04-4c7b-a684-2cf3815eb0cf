import { ApiService } from './../Services/api.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment.prod';

@Component({
  selector: 'app-design-your-jewellery',
  templateUrl: './design-your-jewellery.component.html',
  styleUrls: ['./design-your-jewellery.component.scss']
})
export class DesignYourJewelleryComponent implements OnInit {
  recaptcha_key: any = environment.recaptcha_key
  designForm!: FormGroup
  URL: any;
  image: any;
  fileExtArray = ['jpg', 'jpeg', 'JPEG', 'JPG', 'PNG', 'png', 'svg', 'SVG']
  isLoading : boolean = false

  constructor(
    private formBuilder: FormBuilder, private ApiService: ApiService, private toast: ToastrService
  ) { }

  ngOnInit(): void {
    this.initDesignForm()
  }

  initDesignForm() {
    this.designForm = this.formBuilder.group({
      name: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.pattern('[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,}$')]],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      description: ['', [Validators.required]],
      image: ['', [Validators.required]],
      recaptchaReactive: ['',Validators.required]
    })
  }

  get df() {
    return this.designForm.controls
  }

  onFileChange(event: any) {
    if (event.target.files.length > 0) {
      let imageName = event.target.files[0].name
      let fileExtensionName = imageName.split('.').pop()
      if (this.fileExtArray.includes(fileExtensionName)) {
        const reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onloadend = (e: any) => {
          this.URL = e.target['result'];
        };
        this.image = event.target.files[0];
      }
      else {
        this.toast.error(fileExtensionName.toUpperCase() + ' not supported. Please upload an image file')
      }
    }
  }

  submit() {
    if (this.designForm.invalid) {
      this.designForm.markAllAsTouched()
      return
    }
    const formData = new FormData();
    formData.append('name', this.designForm.get('name')?.value);
    formData.append('email', this.designForm.get('email')?.value);
    formData.append('mobile', this.designForm.get('mobile')?.value);
    formData.append('description', this.designForm.get('description')?.value);
    formData.append('image', this.image);

    // this.isLoading = true;
    this.ApiService.postData(apiEndPoints.jewelleryDesign, formData).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.toast.success(res?.Message)
        this.designForm.reset()
        this.URL = null;
        // this.isLoading = false
      } else {
        // this.isLoading = false;
        this.toast.error(res?.Message)
      }
    })
  }
}
