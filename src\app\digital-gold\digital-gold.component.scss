.adBanner {
  background-repeat: no-repeat;
  background-size: cover;
  height: 504px;
  /*  */
  position: relative;
  background-position: right;

  .content {
    max-width: 570px;
    color: #fff;
    padding: 0 30px;
    position: absolute;
    left: 104px;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;

    h1 {
      color: #CCA154;
      text-transform: capitalize;
      font-size: 60px;
      margin-bottom: 15px;
    }

    h3 {
      font-size: 30px;
      color: #CCA154;
      text-transform: capitalize;
    }




  }

}

.goldSections {
  margin-bottom: 100px;

  .contents {
    margin-top: 100px;

    .container {
      display: flex;
      justify-content: space-between;
    }

    &:nth-child(odd) {
      .container {
        padding-right: 0;
      }

      .text {
        padding-right: 60px;
      }
    }

    &:nth-child(even) {
      .container {
        flex-direction: row-reverse;
        padding-left: 0;
      }

      .text {
        padding-left: 60px;
      }
    }

    h6 {
      color: #1D1D1D;
      font-family: "Gotham";
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      text-transform: capitalize;
      margin-bottom: 14px;
    }

    p {
      font-size: 16px;
    }

    .data+.data {
      margin-top: 30px;
    }

    .data {
      .primary-button {
        max-width: 240px;
        margin-top: 20px;
      }
    }
  }

  .image {
    width: 540px;

    img {
      aspect-ratio: 1;
      object-fit: cover;
    }
  }

  .text {
    width: calc(100% - 540px);
  }


  a.primary-button.golden.digital-button {
    border: 3px solid #FFD700;
    border-radius: 30px;
    background: #fff0d5;
    max-width: 200px;
    padding: 13px 43px 13px 13px;
    position: relative;
    color: #000;
    font-weight: 600;
  }

  a.primary-button.golden.digital-button::before {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    background: #FFD700;
    border-radius: 50%;
    right: 19px;
    top: 50%;
    transform: translateY(-50%);
  }
}

@media screen and (min-width: 1919px) {
  .adBanner .content {
    left: 13%;
  }
}

@media screen and (max-width: 1200px) {
  .goldSections .text {
    width: calc(100% - 80px);
  }

  .goldSections .image {
    width: 400px;
  }
}

@media screen and (max-width: 992px) {
  .goldSections .text {
    width: calc(100% - 40px);
  }
}

@media screen and (max-width: 768px) {
  .adBanner {
    .content {
      left: 2%;

      h1 {
        font-size: 30px;
      }

      h3 {
        font-size: 22px;
      }
    }
  }

  .goldSections {
    margin-bottom: 50px;

    .contents {
      margin-top: 50px;

      &:nth-child(odd) .container {
        padding-right: 25px;
        flex-direction: column-reverse;
      }

      .image {
        width: 70%;
        margin: 0 0 30px;
      }

      &:nth-child(odd) .text {
        width: 100%;
        padding-right: 0;
      }

      &:nth-child(even) .container {
        flex-direction: column-reverse;
        padding-left: 25px;
      }

      &:nth-child(even) .text {
        padding-left: 0;
        width: 100%;
      }

    }
  }

}
