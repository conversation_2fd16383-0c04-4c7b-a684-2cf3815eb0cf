import { CommonService } from 'src/app/Services/common.service';
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent implements OnInit {
  windowRef: any = window;

  constructor(private router: Router, private CommonService: CommonService) {}

  ngOnInit(): void {}

  goldSchemeRedirection() {
    localStorage.setItem('schemePopup', '1');
    this.router.navigateByUrl('/my-gold-schemes');
  }

  logout() {
    localStorage.removeItem('isLogged');
    localStorage.removeItem('token');
    localStorage.removeItem('userId');
    // this.toast.success("Logged out")
    this.router.navigateByUrl('');
    this.windowRef.webengage.user.logout();
    this.CommonService.sendLogoutEvent();
  }

  walletClicked() {
    this.windowRef.webengage.track('My Wallet Clicked', {});
    this.router.navigateByUrl('/wallet');
  }
}
