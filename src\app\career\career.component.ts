import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ApiService } from 'src/app/Services/api.service';
import { apiEndPoints } from '../ApiEndPoints';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-career',
  templateUrl: './career.component.html',
  styleUrls: ['./career.component.scss'],
})
export class CareerComponent implements OnInit {
  activeTab: string = '';
  careerModal: boolean = false;
  pageContent: any = {};
  jobData: any = {};
  tabs: any[] = [];
  fileName: string = '';
  isDragOver = false;
  applyForm: FormGroup;
  selectedJobTitle: string = '';
  selectedJobId: string = '';
  selectedFile: File | null = null;

  // Loading and error states
  isLoading: boolean = false;
  isSubmitting: boolean = false;
  pageError: string = '';
  jobsError: string = '';
  fileError: string = '';

  constructor(
    private ApiService: ApiService,
    private fb: FormBuilder,
    private toast: ToastrService
  ) {
    // Initialize form with proper validators
    this.applyForm = this.fb.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
        ],
      ],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.required, Validators.pattern(/^[0-9]{10}$/)]],
      qualification: ['', [Validators.required, Validators.minLength(2)]],
      message: ['', [Validators.maxLength(500)]],
    });
  }

  ngOnInit(): void {
    this.getPageContent();
    this.getCareerList();
  }

  getPageContent() {
    this.isLoading = true;
    this.pageError = '';

    this.ApiService.getData(apiEndPoints.careerPage).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res?.errorcode == 0) {
          this.pageContent = res?.data;
        } else {
          this.pageError = res?.message || 'Failed to load page content';
        }
      },
      error: (error) => {
        this.isLoading = false;
        this.pageError = 'Failed to load page content. Please try again.';
      },
    });
  }

  getCareerList() {
    this.jobsError = '';

    this.ApiService.getData(apiEndPoints.careerList).subscribe({
      next: (res: any) => {
        if (res?.errorcode == 0) {
          this.processCareerData(res?.data);
        } else {
          this.jobsError = res?.message || 'Failed to load job listings';
        }
      },
      error: (error) => {
        this.jobsError = 'Failed to load job listings. Please try again.';
      },
    });
  }

  processCareerData(data: any) {
    this.tabs = [];
    this.jobData = {};

    if (data?.categories && data.categories.length > 0) {
      data.categories.forEach((category: any) => {
        if (category.jobs && category.jobs.length > 0) {
          const tabId = this.createTabId(category.category);

          this.tabs.push({
            id: tabId,
            label: category.category,
          });

          this.jobData[tabId] = category.jobs.map((job: any) => ({
            id: job.id,
            title: job.desgination || job.designation,
          }));
        }
      });

      if (this.tabs.length > 0) {
        this.activeTab = this.tabs[0].id;
      }
    }
  }

  createTabId(categoryName: string): string {
    return categoryName
      .toLowerCase()
      .replace(/&/g, 'and')
      .replace(/\s+/g, '')
      .replace(/[^a-z0-9]/g, '');
  }

  openModal(jobTitle?: string, jobId?: string) {
    this.selectedJobTitle = jobTitle || '';
    this.selectedJobId = jobId || '';
    this.careerModal = true;
    this.resetForm();
  }

  popUpClose() {
    this.careerModal = false;
    this.selectedJobTitle = '';
    this.resetForm();
  }

  resetForm() {
    this.applyForm.reset();
    this.fileName = '';
    this.selectedFile = null;
    this.fileError = '';
    this.markFormGroupUntouched();
  }

  onFileSelected(event: any) {
    const file: File = event.target.files[0];
    this.handleFileSelection(file);
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragOver = false;
    const file = event.dataTransfer?.files[0];
    if (file) {
      this.handleFileSelection(file);
    }
  }

  handleFileSelection(file: File) {
    this.fileError = '';

    if (!file) {
      return;
    }

    // Check file type - only PDF allowed
    if (file.type !== 'application/pdf') {
      this.fileError = 'Only PDF files are allowed';
      this.fileName = '';
      this.selectedFile = null;
      return;
    }

    // Check file size - max 25MB
    const maxSize = 25 * 1024 * 1024; // 25MB in bytes
    if (file.size > maxSize) {
      this.fileError = 'File size must be less than 25MB';
      this.fileName = '';
      this.selectedFile = null;
      return;
    }

    this.fileName = file.name;
    this.selectedFile = file;
  }

  onSubmit() {
    if (this.applyForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    if (!this.selectedFile) {
      this.fileError = 'Please upload your resume (PDF format)';
      return;
    }

    this.isSubmitting = true;
    const formData = new FormData();

    // Add form fields
    Object.keys(this.applyForm.value).forEach((key) => {
      const value = this.applyForm.value[key];
      if (value) {
        formData.append(key, value);
      }
    });

    // Add job title
    formData.append('jobTitle', this.selectedJobTitle);
    formData.append('jobId', this.selectedJobId);

    // Add file
    formData.append('resume', this.selectedFile);

    this.submitApplication(formData);
  }

  submitApplication(formData: FormData) {
    this.ApiService.postData(
      apiEndPoints.submitApplication,
      formData
    ).subscribe({
      next: (res: any) => {
        this.isSubmitting = false;
        if (res?.errorcode == 0) {
          this.toast.success('Application submitted successfully!');
          this.popUpClose();
        } else {
          this.toast.error(
            res?.message || 'Error submitting application. Please try again.'
          );
        }
      },
      error: (error) => {
        this.isSubmitting = false;
        this.toast.error('Error submitting application. Please try again.');
      },
    });
  }

  markFormGroupTouched() {
    Object.keys(this.applyForm.controls).forEach((key) => {
      this.applyForm.get(key)?.markAsTouched();
    });
  }

  markFormGroupUntouched() {
    Object.keys(this.applyForm.controls).forEach((key) => {
      this.applyForm.get(key)?.markAsUntouched();
    });
  }

  // Helper methods for template
  getFormControl(controlName: string) {
    return this.applyForm.get(controlName);
  }

  hasError(controlName: string, errorType?: string): boolean {
    const control = this.getFormControl(controlName);
    if (!control || !control.touched) return false;

    if (errorType) {
      return control.hasError(errorType);
    }
    return control.invalid;
  }

  getErrorMessage(controlName: string): string {
    const control = this.getFormControl(controlName);
    if (!control || !control.touched || control.valid) return '';

    const errors = control.errors;
    if (!errors) return '';

    if (errors['required'])
      return `${this.getFieldLabel(controlName)} is required`;
    if (errors['email']) return 'Please enter a valid email address';
    if (errors['pattern'])
      return 'Please enter a valid phone number (10 digits)';
    if (errors['minlength'])
      return `${this.getFieldLabel(controlName)} must be at least ${
        errors['minlength'].requiredLength
      } characters`;
    if (errors['maxlength'])
      return `${this.getFieldLabel(controlName)} must not exceed ${
        errors['maxlength'].requiredLength
      } characters`;

    return 'Please enter a valid value';
  }

  getFieldLabel(controlName: string): string {
    const labels: { [key: string]: string } = {
      name: 'Name',
      email: 'Email',
      phone: 'Phone Number',
      qualification: 'Qualification',
      message: 'Message',
    };
    return labels[controlName] || controlName;
  }

  // Check if there are any jobs available
  hasJobsAvailable(): boolean {
    return (
      this.tabs.length > 0 &&
      this.jobData[this.activeTab] &&
      this.jobData[this.activeTab].length > 0
    );
  }

  // Check if page content is available
  hasPageContent(): boolean {
    return (
      this.pageContent &&
      this.pageContent.benefitsHeading &&
      this.pageContent.benefitsHeading.benefits &&
      this.pageContent.benefitsHeading.benefits.length > 0
    );
  }

  // Retry methods for error states
  retryLoadPageContent() {
    this.getPageContent();
  }

  retryLoadJobs() {
    this.getCareerList();
  }
}
