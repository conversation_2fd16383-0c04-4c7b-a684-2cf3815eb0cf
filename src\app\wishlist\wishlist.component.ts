import { ToastrService } from 'ngx-toastr';
import { environment } from './../../environments/environment.prod';
import { apiEndPoints } from './../ApiEndPoints';
import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';
import { CommonService } from '../Services/common.service';

@Component({
  selector: 'app-wishlist',
  templateUrl: './wishlist.component.html',
  styleUrls: ['./wishlist.component.scss'],
})
export class WishlistComponent implements OnInit {
  wishlistData: any;
  imageBase: any;
  isLoading: boolean = false;
  windowRef: any = window;

  constructor(
    private apiService: ApiService,
    private commonService: CommonService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.getWishlist();
    this.imageBase = environment.imageBase;
  }

  getWishlist() {
    this.isLoading = true;

    this.apiService.getData(apiEndPoints.GET_WISHLIST).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.wishlistData = res?.Data;
        }
        //Webengage Start
        this.windowRef.webengage.track('Wishlist View', {
          'Page URL': String(window.location.href),
        });
        //Webengage End

        this.isLoading = false;
      },
      (error) => {
        this.isLoading = false;
        this.toastr.error('Error loading wishlist');
      }
    );
  }

  addToWishlist(id: any, isfavorite: any) {
    let data = { type: 0, productId: id };
    if (!isfavorite) data['type'] = 1;
    this.apiService
      .postData(apiEndPoints.WISHLIST, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.getWishlist();
          this.commonService.sendWishlistEvent();

          //Webengage Start
          const productDetails = this.wishlistData.products?.find(
            (product: any) => product.id == id
          );
          const event = isfavorite
            ? 'Removed From Wishlist'
            : 'Added to Wishlist';
          this.windowRef.webengage.track(event, {
            'Product ID': isfavorite
              ? productDetails?.id
              : productDetails?.slug,
            'Product Name': productDetails?.productName,
            // 'L1 Category Name': '',
            // 'L1 Category ID': '',
            // 'L2 Category Name': '',
            // 'L2 Category ID': '',
            Availability: productDetails?.outOfStock == false ? true : false,
            isBundle: false,
            Quantity: 1,
            MRP: parseFloat(productDetails?.price),
            Currency: 'INR',
            Source: 'Website',
            'Page URL': window.location.href,
          });
          //Webengage End
        }
      });
  }

  addToCart(id: any) {
    let data = { productId: id };
    this.apiService
      .postData(apiEndPoints.ADD_TO_CART, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toastr.success('Product Moved To Cart');
          this.commonService.sendCartEvent();
          this.getWishlist();
        } else if (res?.ErrorCode == 1) this.toastr.info(res?.Message);
      });
  }
}
