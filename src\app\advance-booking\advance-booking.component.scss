.adBanner {
  background-repeat: no-repeat;
  background-size: cover;
  height: 504px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  .content {
    max-width: 570px;
    text-align: center;
    color: #fff;
    padding: 0 30px;

    h1 {
      color: #fff;
    }

    p {
      margin: 30px 0 30px;
    }

    .primary-button.golden {
      width: 278px;
    }
  }

}

.howItWorks {
  padding: 100px 0 10px;

  .contents {
    display: flex;
    justify-content: space-between;
  }

  .box {
    text-align: center;
    width: 180px;
  }

  .step {
    font-weight: 600;
    padding: 20px 0 10px;
  }

  p {
    font-size: 14px;
  }
}

.bannerAd {
  margin-top: 100px;

  img {
    width: 100%;
  }
}
.justIn {
  background: #FFFBF3;
  padding: 30px 0 ;
  margin-top: 100px;
  h2{
    margin-bottom: 40px;
  }
  .section {
    margin-bottom: 40px;
    h6{
      margin-bottom: 15px;
    }
    p {
      margin-bottom: 20px;
    }

    ul li{
      margin-bottom: 13px;
    }

    a {
      color: #CCA154;
      text-decoration: underline;
    }
  }


}

.terms {
  padding-top: 100px;

  .contents ul {
    max-width: 70%;
    margin: 0 auto;
    list-style: none;
  }


  li::before {
    width: 10px;
    content: "";
    height: 10px;
    background: #000;
    border-radius: 2px;
    position: absolute;
    left: -30px;
    top: 10px;
  }

  li {
    position: relative;
    padding-bottom: 10px;
  }
  .bottom{
    margin: 30px 0 0 0;
    text-align: center;
  }
  
  .primary-button.golden {
    width: 278px;
  }
}

.helpiline {
  margin-top: 100px;
  background: #D2AB66;
  text-align: center;
  padding: 15px 0;
  font-size: 25px;
  color: #fff;


  .content {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  a {
    display: flex;
    align-items: center;
    color: #fff;
    margin-left: 30px;

    img {
      padding-right: 12px;
    }
  }
}

@media screen and (max-width: 992px) {
  .adBanner {
    margin-top: 10px;
  }

  .howItWorks {
      padding: 50px 0 10px;
  }

  .bannerAd {
      margin-top: 30px;
  }

  .terms {
    padding-top: 50px;
      .contents ul {
        max-width: 80%;
    }
  }

  .helpiline {
    margin-top: 30px;
    .content {
        font-size: 16px;
    }
  }

}

@media screen and (max-width: 640px) {

  .howItWorks .box {
    width: 50%;
    padding: 20px 10px;
  }

  .howItWorks .contents {
    flex-wrap: wrap;
  }

}

@media screen and (max-width: 480px) {
  .helpiline .content {
    display: flex;
    flex-direction: column;
    span {
        padding-bottom: 10px;
    }
  }
  .helpiline a {
      margin-left: 0;
  }
}
