import { ToastrService } from 'ngx-toastr';
import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-referals',
  templateUrl: './referals.component.html',
  styleUrls: ['./referals.component.scss']
})
export class ReferalsComponent implements OnInit {
  Popuprefer: any = false;
  ocassions: any;
  selectedOccasion: any;
  enteredName: any;
  enteredMobile: any
  referForm!: FormGroup

  constructor(private ApiService: ApiService, private toast: ToastrService, private fb: FormBuilder) { }

  ngOnInit(): void {
    this.getRferals()
    this.initReferForm()
  }

  initReferForm() {
    this.referForm = this.fb.group({
      name: ['', [Validators.required]],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]]
    })
  }

  get rf() {
    return this.referForm.controls
  }

  getRferals() {
    this.ApiService.getData(apiEndPoints.REFERAL_OCASSION).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.ocassions = res?.Data
      } else this.toast.error(res?.Message)
    })
  }

  sendReferal() {
    if (this.referForm.invalid) {
      this.referForm.markAllAsTouched()
      return;
    }
    let data = { Occasion_id: this.selectedOccasion, name: this.referForm.get('name')?.value, mobile: this.referForm.get('mobile')?.value };

    this.ApiService.postData(apiEndPoints.REFERAL_REQUEST, data).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.toast.success(res?.Message);
        this.popUpClose();
        this.selectedOccasion = '';
        this.referForm.reset()
      } else {
        this.toast.error(res?.Message);
      }
    });
  }

  popUp() {
    if (this.selectedOccasion) {
      this.Popuprefer = !this.Popuprefer;
    } else {
      this.toast.error('Select an Occasion')
    }
  }

  popUpClose() {
    this.Popuprefer = false;
  }

}
