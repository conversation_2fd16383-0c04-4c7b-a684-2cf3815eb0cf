.myAccount {
    padding: 40px 0 100px;
}
.sideMenu {
    width: 270px;
}
.contentSection {
    display: flex;

    .contents {
        width: calc(100% - 270px);
        padding-left: 50px;
    }
}


.head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    span{font-weight: 600;}
    .add {
        width: auto;
    }
    .add {
        font-size: 14px;
        font-weight: 600;
        position: relative;
        text-align: right;
        cursor: pointer;
        &::before {
            content: "+";
            position: absolute;
            left: -25px;
            top: 1px;
            border: 1px solid #000;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            text-align: end;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

}

.goldSchemes {
    padding: 10px 30px 30px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    margin: 1px;

    .id {
        font-size: 16px;
        padding-bottom: 15px;
    }

    .leftSide {
        width: 70%;
    }

    .box {
        display: flex;
    }

    .field {
        width: 50%;
    }

    .data {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
    }
    .text{
        padding-top: 20px;
    }
    .name {
        font-weight: 600;
        padding-top: 3px;
    }

    .primary-button {
        margin-bottom: 15px;
        width: 184px;
    }
}

h1 {
  font-size: 44px;
  margin-bottom: 25px;
}


@media screen and (max-width: 992px) {
    .myAccount {
        padding: 20px 0 50px;
    }
    .sideMenu {
        width: 60px;
    }
    .contentSection .contents {
        width: calc(100% - 60px);
    }

    h1 {
        font-size: 30px;
        margin-bottom: 10px;
    }
}
@media screen and (max-width: 640px) {
    .contentSection {
      flex-direction: column;
      .contents {
        width: 100%;
        padding-left: 0;
        padding-top: 30px;
      }

    }
    .sideMenu {
        width: 100%;
    }

  }
@media screen and (max-width: 480px) {

    .goldSchemes {
        padding: 10px 20px 30px;
        .box {
            display: flex;
            flex-direction: column;
        }

        .field{
            width: 100%;
        }

        .leftSide {
            width: 100%;
        }
    }

    h1 {
        font-size: 25px;
    }

}
