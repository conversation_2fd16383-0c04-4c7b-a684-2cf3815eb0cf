.myAccount {
  padding: 40px 0 100px;
}

h1 {
  font-size: 44px;
  margin-bottom: 25px;
}

.sideMenu {
  width: 270px;
}

.contentSection {
  display: flex;

  .contents {
    width: calc(100% - 270px);
    padding-left: 50px;
  }
}

.head {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 15px;

  span {
    font-weight: 600;
  }

  .add {
    width: auto;
    font-size: 14px;
    font-weight: 600;
    position: relative;
    text-align: right;
    cursor: pointer;

    &::before {
      content: "+";
      position: absolute;
      left: -25px;
      top: 1px;
      border: 1px solid #000;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      text-align: end;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .info {
    position: relative;
    cursor: pointer;

    .infoTxt {
      display: none;
      position: absolute;
      width: max-content;
      right: -16px;
      top: 50px;
      background: #FFFFFF;
      box-shadow: 0px 0px 13px rgba(0, 0, 0, 0.09);
      padding: 10px;
      font-size: 16px;
      z-index: 1;

      &::before {
        content: "";
        position: absolute;
        top: -7px;
        right: 10px;
        border: 15px solid #fff;
        transform: rotate(45deg);
        z-index: -1;
      }
    }

    &:hover {
      .infoTxt {
        display: block;
      }

    }
  }

}

.walletSection {
  padding: 30px;
  background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
  margin: 1px;

  .tabs {
    display: flex;

    .tabName {
      width: 33.33%;
      text-align: center;
      color: #000;
      font-weight: 600;
      padding-bottom: 10px;
      border-bottom: 1px solid #0000001a;
      cursor: pointer;
    }

    .tabName.active {
      color: #D0A861;
      border-bottom: 2px solid #D0A861;
    }
  }

  .tabContent {
    padding: 30px 0;
    display: none;

    &.active {
      display: block;
    }

    .walletBalance {
      background: #FFFBF3;
      padding: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40px;

      .leftSide {
        width: calc(100% - 220px);
      }

      .rightSide {
        width: 220px;
        text-align: right;
        display: flex;
        justify-content: flex-end;
      }

      .rate {
        color: #D2AB66;
        font-weight: 600;
        font-size: 32px;
      }

      .wallet {
        font-size: 14px;
        font-weight: 600;
      }

      .primary-button {
        width: 254px;
        display: flex;
        align-items: center;
        justify-content: center;

        span {
          padding-left: 10px;
        }
      }
    }

    .walletContent {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;

      .detail {
        display: flex;
        width: 80%;

        .icon {
          margin-right: 20px;
        }

        .wallName {
          font-size: 16px;
        }

        .name {
          font-weight: 600;
          padding-bottom: 2px;
        }

        .date {
          display: flex;
        }

        .date span {
          padding-right: 30px;
        }
      }

      .price {
        width: 20%;
        text-align: right;
        font-size: 16px;
        font-weight: 600;

        &.credit {
          color: #239A48;
        }

        &.debit {
          color: #ED5A5A;
        }
      }

    }
  }


}

.giftPopup {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  .box {
    display: flex;
    flex-direction: column;
    width: 695px;
    margin: 0 auto;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    position: fixed;
    top: 50%;
    padding: 30px;
    left: 50%;
    z-index: 9;
    background: #fff;
    transform: translate(-50%, -50%);

    input {
      border: 1px solid #E0E0E0;
      margin-bottom: 20px;
    }
  }

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }

  .button {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;

    .primary-button {
      width: 200px;
      margin: 0 0 0 20px;
    }
  }

}

.subtabs {
  display: flex;
  align-items: center;
  justify-content: center;
  width: max-content;
  margin: 0 auto;
  border: 1px solid rgba(230, 230, 230, 1);
  border-radius: 47px;
  padding: 6px 0;
  .tabName.active {
    background: #CB9F52;
    border-radius: 47px;
    color: #fff;
  }

  .tabName {
    padding: 7px 20px;
    margin: 0 10px;
    cursor: pointer;
  }
}


@media screen and (max-width: 1200px) {
  .walletSection {
    .tabs .tabName {
      font-size: 14px;
    }

    .tabContent .walletBalance {
      flex-direction: column;

      .leftSide {
        width: 100%;
        margin-bottom: 20px;
      }

      .rightSide {
        width: 100%;
        justify-content: flex-start;
      }
    }
  }
}


@media screen and (max-width: 992px) {
  .myAccount {
    padding: 20px 0 50px;
  }

  .sideMenu {
    width: 60px;
  }

  .contentSection .contents {
    width: calc(100% - 60px);
  }

  h1 {
    margin-bottom: 10px;
    font-size: 30px;
  }
}

@media screen and (max-width: 640px) {
  .contentSection {
    flex-direction: column;

    .contents {
      width: 100%;
      padding-left: 0;
      padding-top: 30px;
    }

  }

  .sideMenu {
    width: 100%;
  }

  .walletSection {
    padding: 20px;

    .tabContent .walletBalance {
      padding: 20px;
      margin-bottom: 20px;

      .rate {
        font-size: 24px;
      }
    }
  }

}

@media screen and (max-width: 480px) {


  .walletSection .tabContent {
    padding: 20px 0 0;


    .walletContent {
      flex-direction: column;

      .detail {
        width: 100%;

        .icon {
          margin-right: 10px;
        }

        .date {
          flex-direction: column;
        }
      }

      .price {
        width: 100%;
        margin-top: 10px;
        margin-left: 46px;
        text-align: left;
      }

    }

  }
  .subtabs {
      display: flex;
      flex-direction: column;
      border: none;
      .tabName {
          border: 1px solid #CB9F52;
          border-radius: 47px;
          margin: 3px;
      }
  }

  h1 {
    font-size: 25px;
  }

}
