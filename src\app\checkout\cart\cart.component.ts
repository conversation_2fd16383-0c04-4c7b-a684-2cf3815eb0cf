import { ToastrService } from 'ngx-toastr';
import { environment } from './../../../environments/environment.prod';
import { apiEndPoints } from './../../ApiEndPoints';
import { Component, OnInit } from '@angular/core';
import { ApiService } from 'src/app/Services/api.service';
import { Router } from '@angular/router';
import { CommonService } from 'src/app/Services/common.service';

@Component({
  selector: 'app-cart',
  templateUrl: './cart.component.html',
  styleUrls: ['./cart.component.scss'],
})
export class CartComponent implements OnInit {
  Popupredeem: boolean = false;
  cartData: any;
  imageBase: any;
  isPromoCodeApplied: any = false;
  isSchemeBalanceApplied: any = false;
  isWeightBalanceApplied: any = false;
  isMainWalletApplied: any = false;
  isRewardPointApplied: any = false;
  selectedScheme: any = '';
  selectedMainWallet: any;
  redeem_points: any;
  selectedRewardPoints: any;
  promocode: any;
  instruction: any;
  confirmPopup: boolean = false;
  isLoading: boolean = false;
  cartid: any;
  windowRef: any = window;

  constructor(
    private apiService: ApiService,
    private toast: ToastrService,
    private router: Router,
    private commonService: CommonService
  ) {}

  ngOnInit(): void {
    this.getCartDetails();
    this.imageBase = environment.imageBase;
    if (
      localStorage.getItem('cashScheme') ||
      localStorage.getItem('weigtScheme') ||
      localStorage.getItem('mainWallet') ||
      localStorage.getItem('rewardPoints') ||
      localStorage.getItem('promoCode') ||
      localStorage.getItem('redeemPoints')
    ) {
      localStorage.removeItem('cashScheme');
      localStorage.removeItem('weigtScheme');
      localStorage.removeItem('mainWallet');
      localStorage.removeItem('rewardPoints');
      localStorage.removeItem('promoCode');
      localStorage.removeItem('redeemPoints');
    }
  }

  getCartDetails(): Promise<any> {
    // this.isLoading = true
    let data = {
      promo_code_applied: this.isPromoCodeApplied,
      schembalance_applied: this.isSchemeBalanceApplied,
      weightbalance_applied: this.isWeightBalanceApplied,
      mainwallet_applied: this.isMainWalletApplied,
      reward_points_applied: this.isRewardPointApplied,
      promo_code: this.promocode,
      reward_points: this.redeem_points,
    };
    return new Promise((resolve, reject) => {
      this.apiService.postData(apiEndPoints.get_cart_details, data).subscribe(
        (res: any) => {
          if (res.ErrorCode == 0) {
            this.cartData = res?.Data;

            //Webengage Start
            this.windowRef.webengage.track('Cart Viewed', {
              'Cart ID': this.cartData.cartId,
              'No. Of Products': this.cartData?.products?.length,
              'Total Amount': parseFloat(this.cartData?.grand_total),
              Quantity: this.cartData?.products?.length,
              'Product Details': this.cartData?.products.map((item: any) => ({
                'Product ID': item?.slug,
                'Product Name': item?.name,
                Availability: item?.outOfStock == false ? true : false,
                isBundle: false,
                Quantity: 1,
                MRP: Number(item?.price).toFixed(2),
                Currency: 'INR',
              })),
              Source: 'Website',
              'Page URL': window.location.href,
              'Cart Value': parseFloat(this.cartData?.sub_total),
            });
            //Webengage End

            resolve(this.cartData);
            reject('API call failed');
            // this.isLoading = false
          } else {
            this.toast.error(res?.Message);
            // this.isLoading = false
            if (this.selectedRewardPoints) this.selectedRewardPoints = false;
          }
        },
        (err) => {
          reject(err);
        }
      );
    });
  }

  removeFromCart() {
    this.confirmPopup = false;
    let data = { productId: this.cartid };
    this.apiService
      .postData(apiEndPoints.REMOVE_CART, data)
      .subscribe((res: any) => {
        if (res.ErrorCode == 0) {
          this.getCartDetails();
          this.commonService.sendCartEvent();

          //Webengage Start
          const producDetails = this.cartData?.products.find(
            (x: any) => x.id == this.cartid
          );
          this.windowRef.webengage.track('Removed From Cart', {
            'Cart ID': '',
            'Product Id': producDetails?.slug,
            'Product Name': producDetails?.name,
            'L1 Category Name': '',
            'L1 Category ID': '',
            'L2 Category Name': '',
            'L2 Category ID': '',
            Availability: producDetails?.outOfStock == false ? true : false,
            isBundle: false,
            isSample: false,
            Quantity: 1,
            MRP: Number(producDetails?.price),
            Currency: 'INR',
            Source: 'Website',
            'Page URL': window.location.href,
          });
          //Webengage End
        }
      });
  }

  addToWishlist(id: any, isfavorite: any) {
    let data = { type: 0, productId: id };
    if (!isfavorite) data['type'] = 1;
    this.apiService
      .postData(apiEndPoints.WISHLIST, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.getCartDetails();
          this.commonService.sendWishlistEvent();

          //Webengage Start
          const producDetails = this.cartData?.products.find(
            (x: any) => x.id == id
          );
          const event = isfavorite
            ? 'Removed From Wishlist'
            : 'Added to Wishlist';
          this.windowRef.webengage.track(event, {
            'Product ID': isfavorite ? producDetails?.id : producDetails?.slug,
            'Product Name': producDetails?.name,
            // 'L1 Category Name': '',
            // 'L1 Category ID': '',
            // 'L2 Category Name': '',
            // 'L2 Category ID': '',
            Availability: producDetails?.outOfStock == false ? true : false,
            isBundle: false,
            Quantity: 1,
            MRP: parseFloat(producDetails?.price),
            Currency: 'INR',
            Source: 'Website',
            'Page URL': window.location.href,
          });
          //Webengage End
        }
      });
  }

  onSchemeChange(event: any) {
    this.selectedScheme = event.target.value;
    if (this.selectedScheme == 'cash') {
      this.isSchemeBalanceApplied = true;
      localStorage.setItem('cashScheme', 'true');
    }
    if (this.selectedScheme == 'weight') {
      this.isWeightBalanceApplied = true;
      localStorage.setItem('weigtScheme', 'true');
    }
    this.getCartDetails();
  }

  onScheme(event: any) {
    if (!event.target.checked) {
      this.selectedScheme = '';
      this.isSchemeBalanceApplied = false;
      this.isWeightBalanceApplied = false;
      this.getCartDetails();
      localStorage.removeItem('cashScheme');
      localStorage.removeItem('weigtScheme');
    }
  }

  onMainBalanceChange() {
    if (this.selectedMainWallet) {
      this.isMainWalletApplied = true;
      localStorage.setItem('mainWallet', 'true');
    } else {
      this.isMainWalletApplied = false;
      localStorage.removeItem('mainWallet');
    }
    if (localStorage.getItem('redeemPoints'))
      this.redeem_points = localStorage.getItem('redeemPoints');
    this.getCartDetails();
  }

  popUp() {
    if (this.cartData?.possible_rewardpoints && !this.selectedRewardPoints) {
      this.Popupredeem = !this.Popupredeem;
    } else {
      this.selectedRewardPoints = false;
      this.isRewardPointApplied = false;
      this.redeem_points = '';
      this.getCartDetails();
    }
  }

  async redeemRewardPoints() {
    if (this.redeem_points) {
      this.isRewardPointApplied = true;
      await this.getCartDetails();
      if (this.cartData?.possible_rewardpoints) {
        localStorage.setItem('redeemPoints', this.redeem_points);
        this.popUpClose('redeem');
        this.selectedRewardPoints = true;
        localStorage.setItem('rewardPoints', 'true');
      } else {
        this.toast.error('Reward points Validation error');
        this.isRewardPointApplied = false;
        this.redeem_points = '';
        this.getCartDetails();
        this.selectedRewardPoints = false;
        localStorage.removeItem('rewardPoints');
        localStorage.removeItem('redeemPoints');
      }
    } else this.isRewardPointApplied = false;
  }

  popUpClose(type: any) {
    if (type == 'redeem') {
      this.Popupredeem = false;
      this.selectedRewardPoints = false;
      this.redeem_points = '';
    }
    if (type == 'confirm') this.confirmPopup = false;
  }

  async applyPromoCode() {
    if (this.promocode) {
      this.isPromoCodeApplied = true;
      await this.getCartDetails();
      if (this.cartData?.valid_promocode) {
        this.toast.success(this.cartData?.promocode_message);
        localStorage.setItem('promoCode', this.promocode);

        //Webengage Start
        this.windowRef.webengage.track('Coupon Code Applied', {
          'Cart Value Before Discount': '',
          'Cart Value After Discount': Number(
            this.cartData?.grand_total
          ).toFixed(2),
          'Order Coupon Code': this.promocode,
          'Discount Amount': Number(
            this.cartData?.discount_lists.find(
              (x: any) => x.label == 'Promocode'
            )?.amount
          ).toFixed(2),
          Source: 'Website',
          'Page URL': window.location.href,
        });
        //Webengage End
      } else {
        this.toast.error(this.cartData?.promocode_message);

        //Webengage Start
        this.windowRef.webengage.track('Coupon Code Failed', {
          'Coupon Code': this.promocode,
          Source: 'Website',
          'Page URL': window.location.href,
        });
        //Webengage End

        this.promocode = '';
        this.isPromoCodeApplied = false;
      }
    }
  }

  continue() {
    let nostock = this.cartData?.products?.some(
      (product: any) => product.outOfStock
    );

    if (nostock) {
      this.toast.error('Some of the products are out of stock');
      return;
    }

    //Webengage Start
    this.windowRef.webengage.track('Order Initiated', {
      'Order ID': '',
      'Product Name': this.cartData?.products
        ?.map((x: any) => x.productName)
        .join(','),
      'Product ID': this.cartData?.products?.map((x: any) => x.slug).join(','),
      // 'L1 Category ID': '',
      // 'L1 Category Name': '',
      // 'L2 Category ID': '',
      // 'L2 Category Name': '',
      'No. Of Products': this.cartData?.products?.length,
      'Total Amount': parseFloat(this.cartData?.grand_total),
      Quantity: this.cartData?.products?.length,
      Currency: 'INR',
      'Product Details': this.cartData?.products?.map((x: any) => {
        return {
          'Product ID': x.slug,
          'Product Name': x.productName,
          Availability: x.outOfStock == false ? true : false,
          isBundle: false,
          Quantity: 1,
          MRP: Number(x.price).toFixed(2),
          'L1 Category ID': x?.maincategory_id,
          'L1 Category Name': x?.maincategory_name,
          'L2 Category ID': x?.subcategory_id,
          'L2 Category Name': x?.subcategory_name,
        };
      }),
      'Discount Amount':
        this.cartData?.discount_lists?.length > 0
          ? Number(this.cartData?.discount_lists[0]?.amount).toFixed(2)
          : 0,
      'Coupon Code': this.promocode ? this.promocode : '',
      'Sub Total': Number(this.cartData?.sub_total),
      Source: 'Website',
      'Page URL': window.location.href,
    });
    //Webengage End

    if (this.instruction) {
      localStorage.setItem('instruction', this.instruction);
    } else localStorage.removeItem('instruction');
    this.router.navigate(['/cart/delivery-details']);
  }

  confirmationBox(id: any) {
    this.cartid = id;
    this.confirmPopup = true;
  }

  onInstruction() {
    if (this.instruction) this.instruction = '';
  }
}
