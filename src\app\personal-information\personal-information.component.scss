.myAccount {
  padding: 40px 0 100px;
}
.sideMenu {
    width: 270px;
  }
.contentSection {
  display: flex;

    .contents {
      width: calc(100% - 270px);
      padding-left: 50px;

      .box {
          background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
        padding: 30px;
        margin: 1px;
        margin-bottom: 30px;
        position: relative;
      }
      .edit{
          position: absolute;
          right: 10px;
          top: 10px;
          display: flex;
          font-size: 14px;
          font-weight: 600;
          padding: 10px;
          cursor: pointer;
      }
      .head {
        font-weight: 600;
        padding-bottom: 20px;
        position: relative;
      }
      .pInfo {
          display: flex;

          .field {
          width: 290px;
          margin-bottom: 30px;
          font-size: 16px;
          }

          .text {
          font-weight: 600;
          padding-bottom: 5px;
          }
      }
      .addNew {
          display: flex;
          position: absolute;
          right: 0;
          top: 0;
          font-size: 14px;
          cursor: pointer;
      }
      .address {
          .name {
              font-size: 16px;
          }

          div {
              font-size: 14px;
              padding-bottom: 6px;
          }
      }
      .delete{
          position: absolute;
          right: 10px;
          bottom: 10px;
          padding: 10px;
          cursor: pointer;
      }
  }

  .form {
      .twoSection{
          display: flex;
          justify-content: space-between;
          .field {
              width: 49%;
          }
      }
      .threeSection {
          display: flex;
          justify-content: space-between;
          .field {
              width: 32%;
          }
      }


      input,
      select,
      textarea {
        border: 1px solid #BBBBBB;
      }

      select {
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: url("/assets/images/angle-down.svg");
        background-repeat: no-repeat;
        background-position: 98%;

      }

      .field span {
        font-size: 14px;
        color: #787878;
        font-weight: 500;
        margin-bottom: 10px;
        text-transform: capitalize;
      }

      .field.time {
        .timePicker {
          position: relative;
          &::after {
            content: "";
            position: absolute;
            width: 24px;
            height: 24px;
            z-index: -1;
            right: 10px;
            top: 10px;
            background-image: url(/assets/images/clock.svg);
            background-repeat: no-repeat;
          }
        }


        input {
          cursor: pointer;
          display: block;
          width: 100%;
          padding: 10px;
          background: transparent;
          font-size: 12px;
          &:focus-visible {
            outline: none;
          }
        }
      }
      .field.gender {
          display: flex;
          align-items: center;
          span {
              padding-right: 50px;
              margin-bottom: 0;
              margin-right: 0;
          }

          .radioBtn {
              padding-bottom: 0;
              flex-direction: row;
          }

      }

      .leftSide img {
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  .buttons {
      display: flex;
      justify-content: flex-end;
      .primary-button {
          width: 220px;
          margin-left: 20px;
      }
  }

  .filtername {
      padding: 12px 20px;
      border: 1px solid #D5DBDE;
      margin-right: 15px;
      font-size: 14px;
      cursor: pointer;
      width: 100%;
      height: 49px;
      position: relative;
      margin-bottom: 20px;

      img {
        padding-right: 10px;
      }
    }

    .drop {
      display: none;
    }

  .selectOption {

    span {
      padding-right: 5px;
    }

    .selectMenu {
      font-size: 14px;
      color: #000;
      position: relative;
      padding-right: 25px;

      &.sort {
        &::before {
          content: "";
          background-image: url(/assets/images/angle-down.svg);
          position: absolute;
          right: 0px;
          top: 3px;
          width: 20px;
          height: 20px;
        }
      }
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      position: absolute;
      opacity: 0;
      box-shadow: 0px 2px 2px #D5DBDE;
      background: #fff;
      width: 100%;
      left: 0px;
      top: 49px;
      z-index: -1;

      &.active {
        z-index: 3;
        opacity: 1;
        transition: all linear .2s;
        z-index: 1;
      }

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: -12px;
        width: 100%;
        height: 20px;

      }

      li {
        padding: 0px 15px;

        &:hover {
          color: #D2AB66;
        }
      }
    }

  }


}

input.ng-untouched::after,
form.ng-untouched::after {
  display: none;
}


.ng-untouched {
    display: block;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 24px;
      height: 24px;
      z-index: -1;
      right: 10px;
      top: 10px;
      background-image: url(/assets/images/calander.svg);
      background-repeat: no-repeat;
    }
  }

.datepicker-container.datepicker-default {
width: 100%;
}





[type="radio"]:checked,
[type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}
[type="radio"]:checked + label,
[type="radio"]:not(:checked) + label
{
    position: relative;
    padding-left: 38px;
    padding-top: 2px;
    cursor: pointer;
    line-height: 20px;
    display: inline-block;
    color: #666;
}
[type="radio"]:checked + label:before,
[type="radio"]:not(:checked) + label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 25px;
    height: 25px;
    border: 2px solid #D2AB66;
    border-radius: 100%;
    background: #fff;
}
[type="radio"]:checked + label:after,
[type="radio"]:not(:checked) + label:after {
    content: '';
    width: 13px;
    height: 13px;
    background: #D2AB66;
    position: absolute;
    top: 6px;
    left: 6px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
[type="radio"]:not(:checked) + label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
}
[type="radio"]:checked + label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}
.radioBtn {
    display: flex;
    flex-direction: column;
    padding-bottom: 12px;
    align-items: center;

    .checkbox+.checkbox, .radio {
        margin-top: 10px;
    }
    .radio {
        margin: 0 50px 0 0;
        color: #000;
        font-size: 14px;
    }

    label.radio-label {
        color: #000;
    }
}

.country {
  position: relative;
  select {
    position: absolute;
    left: 0;
    width: 80px;
    margin-bottom: 0;
    height: 47px;
    top: 0;
    z-index: 1;
    border: none !important;
    padding: 0 10px !important;
}

input[type=tel] {
  padding-left: 90px !important;
}
}

.referPopup {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  .box {
    display: flex;
    flex-direction: column;
    width: 695px;
    margin: 0 auto;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    position: fixed;
    top: 50%;
    padding: 30px;
    left: 50%;
    z-index: 9;
    background: #fff;
    transform: translate(-50%, -50%);
    border-radius: 12px;
    input{
      border: 1px solid #E0E0E0;
      margin-bottom: 20px;
    }
  }

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }
  .button {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
      .primary-button {
          width: 200px;
          margin: 0 0 0 20px;
      }
  }

}

h1 {
  font-size: 44px;
  margin-bottom: 25px;
}

@media screen and (max-width: 992px) {
  .myAccount {
    padding: 20px 0 50px;
  }
  .sideMenu {
      width: 60px;
  }
  .contentSection .contents {
      width: calc(100% - 60px);
  }

  h1 {
    font-size: 30px;
    margin-bottom: 10px;
  }
}



@media screen and (max-width: 640px) {
  .contentSection {
    flex-direction: column;
    .contents {
      width: 100%;
      padding-left: 0;
      padding-top: 30px;
      .pInfo .field{
        font-size: 14px;
        margin-bottom: 15px;
      }
    }
    .pInfo {
      flex-direction: column;
    }

  }
  .sideMenu {
      width: 100%;
  }

}

@media screen and (max-width: 480px) {
  h1 {
    font-size: 25px;
  }

}
