import {
  Component,
  OnInit,
  ViewChild,
  ChangeDetector<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>roy,
  ChangeDetectionStrategy,
  Inject,
  PLATFORM_ID,
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import SwiperCore, {
  Swiper,
  SwiperOptions,
  Virtual,
  Navigation,
  FreeMode,
  Pagination,
  Autoplay,
} from 'swiper';
import { SwiperComponent } from 'swiper/angular';
import { environment } from 'src/environments/environment.prod';
import { VideoService, VideoQuality } from '../Services/video.service';
import { BehaviorSubject, Observable, Subscription, combineLatest } from 'rxjs';
import { map, take, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from '../Services/api.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CommonService } from '../Services/common.service';
import { Meta, Title } from '@angular/platform-browser';
import { Subject } from 'rxjs';

// Only initialize swiper plugins once
SwiperCore.use([Virtual, FreeMode, Navigation, Pagination, Autoplay]);

// Simplified Banner interface
interface BannerItem {
  id?: number;
  title: string;
  url?: string;
  poster?: string;
  thumbnail?: string;
  image?: string;
  redirection_type?: number;
  slug?: string;
  bannerName?: string;
  bannerCategory?: string;
  type?: 'video' | 'image';
}

// Optimized Swiper configurations as constants
const SWIPER_CONFIGS = {
  specification: {
    slidesPerView: 8,
    freeMode: false,
    watchSlidesProgress: true,
    pagination: false,
    navigation: false,
    lazy: true,
    preloadImages: false,
    breakpoints: {
      320: { slidesPerView: 1.5, spaceBetween: 10 },
      360: { slidesPerView: 2.5 },
      640: { slidesPerView: 4.5 },
      700: { slidesPerView: 5.5 },
      992: { slidesPerView: 8, spaceBetween: 43 },
    },
  },
  trend: {
    spaceBetween: 0,
    slidesPerView: 4,
    freeMode: true,
    watchSlidesProgress: true,
    lazy: true,
    preloadImages: false,
    navigation: { nextEl: '#trend-Next', prevEl: '#trend-Prev' },
    autoplay: { delay: 3000, disableOnInteraction: false },
    breakpoints: {
      320: { slidesPerView: 1.4 },
      480: { slidesPerView: 2 },
      992: { slidesPerView: 3 },
      1200: { slidesPerView: 4 },
    },
  },
  brand: {
    spaceBetween: 34,
    slidesPerView: 4,
    loop: true,
    lazy: true,
    preloadImages: false,
    navigation: { nextEl: '#category-Next', prevEl: '#category-Prev' },
    autoplay: { delay: 3000, disableOnInteraction: false },
    breakpoints: {
      320: { slidesPerView: 1.5 },
      480: { slidesPerView: 2 },
      700: { slidesPerView: 4, spaceBetween: 20 },
    },
  },
  hotCategory: {
    spaceBetween: 22,
    slidesPerView: 5,
    freeMode: true,
    loop: true,
    lazy: true,
    preloadImages: false,
    navigation: { nextEl: '#category-Next', prevEl: '#category-Prev' },
    autoplay: { delay: 3000, disableOnInteraction: false },
    breakpoints: {
      320: { slidesPerView: 1 },
      480: { slidesPerView: 2 },
      700: { slidesPerView: 4 },
      992: { autoplay: { delay: 3000 } },
    },
  },
  client: {
    spaceBetween: 18,
    loop: true,
    slidesPerView: 3,
    initialSlide: 2,
    freeMode: true,
    lazy: true,
    preloadImages: false,
    navigation: { nextEl: '#client-Next', prevEl: '#client-Prev' },
    breakpoints: {
      320: { slidesPerView: 1.4 },
      480: { slidesPerView: 2 },
      700: { slidesPerView: 3 },
    },
  },
  featured: {
    spaceBetween: 0,
    slidesPerView: 3,
    freeMode: false,
    lazy: true,
    preloadImages: false,
    navigation: { nextEl: '#category-Next', prevEl: '#category-Prev' },
    breakpoints: {
      320: { slidesPerView: 1 },
      480: { slidesPerView: 2 },
      700: { slidesPerView: 3 },
    },
  },
} as const;

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeComponent implements OnInit, OnDestroy {
  @ViewChild('swiper', { static: false }) swiperRef?: SwiperComponent;

  // Use readonly configs
  readonly specificationSlider = SWIPER_CONFIGS.specification;
  readonly trendSlide = SWIPER_CONFIGS.trend;
  readonly brandSlide = SWIPER_CONFIGS.brand;
  readonly hotCategorySlide = SWIPER_CONFIGS.hotCategory;
  readonly clientSlide = SWIPER_CONFIGS.client;
  readonly featuredSlide = SWIPER_CONFIGS.featured;

  // Essential data properties only
  homeData: any;
  readonly imageBase = environment.imageBase;
  
  // Lazy loaded sections
  trendingListData: any;
  featuredListData: any;
  processedBanners: BannerItem[] = [];
  
  // Form properties (lazy initialized)
  form?: FormGroup;
  testimonialModal = false;
  isSubmitted = true;
  filedata: any;
  isImageUploaded = true;
  imageURL: any;
  showModal = false;
  isLoading = false;
  
  // Optimize file extensions check
  private readonly fileExtArray = new Set(['jpg', 'jpeg', 'JPEG', 'JPG', 'PNG', 'png', 'webp', 'WEBP']);
  
  // Subscription management
  private readonly destroy$ = new Subject<void>();
  private homeDataSubscription?: Subscription;

  // Video player properties (minimal)
  readonly videoPlayerAutoSlide = true;
  readonly videoPlayerSlideInterval = 8000;

  constructor(
    private apiService: ApiService,
    private toast: ToastrService,
    private commonService: CommonService,
    private router: Router,
    private metaService: Meta,
    private titleService: Title,
    private cdr: ChangeDetectorRef,
    private videoService: VideoService,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  ngOnInit(): void {
    // Only run in browser
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    // Set page title and meta tags first (for SEO)
    this.setPageMeta();
    
    // Load critical data
    this.loadHomeData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    // Clean up subscription
    if (this.homeDataSubscription) {
      this.homeDataSubscription.unsubscribe();
    }
  }

  private setPageMeta(): void {
    this.titleService.setTitle('TT Devassy Jewellery - Premium Gold & Diamond Jewelry Online');
    
    this.metaService.updateTag({
      name: 'description',
      content: 'Shop premium gold, diamond & platinum jewelry online. BIS hallmarked with HUID. Free shipping, lifetime exchange & guaranteed buyback.'
    });

    this.metaService.updateTag({
      name: 'keywords',
      content: 'gold jewelry, diamond jewelry, platinum jewelry, BIS hallmarked, online jewelry store, bridal jewelry'
    });
  }

  private loadHomeData(): void {
    this.isLoading = true;
    
    this.homeDataSubscription = this.apiService.getData(apiEndPoints.HOME)
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(100), // Prevent rapid calls
        distinctUntilChanged()
      )
      .subscribe({
        next: (res: any) => {
          if (res?.ErrorCode === 0 && res?.Data) {
            this.processHomeData(res.Data);
          }
          this.isLoading = false;
          this.cdr.markForCheck();
        },
        error: (error) => {
          console.error('Failed to load home data:', error);
          this.isLoading = false;
          this.cdr.markForCheck();
        }
      });
  }

  private processHomeData(data: any): void {
    this.homeData = data;
    
    // Process only essential data immediately
    this.processBannerData();
    this.trendingListData = data.top_trending_list;
    this.featuredListData = data.featured_best_list;
    
    // Defer less critical data processing
    this.deferNonCriticalData(data);
  }

  private deferNonCriticalData(data: any): void {
    // Use setTimeout to defer non-critical data processing
    setTimeout(() => {
      if (data) {
        // Process remaining data that's not immediately visible
        Object.assign(this.homeData, {
          trendingCollectionData: data.top_trending_collection,
          menWomenSection: data.for_men_women,
          offerData: data.offer_banner,
          miGoldSection: data.mi_gold_banner,
          perfectBride: data.perfect_bride,
          brands: data.brands_we_love,
          categories: data.hot_categories,
          testimonials: data.testimonials,
          blogs: data.blogs
        });
        this.cdr.markForCheck();
      }
    }, 100);
  }

  private processBannerData(): void {
    if (!this.homeData?.banners?.length) {
      this.processedBanners = this.getDefaultBanners();
      return;
    }

    const videoEntry: BannerItem = {
      id: 0,
      title: 'TT Devassy Jewellery Collection',
      url: 'assets/video/banner/output.m3u8',
      poster: 'https://gziw1jnd2b.ufs.sh/f/5oCdTODIdQkJ3RscXA2m87dPh31O9U4wpY6KNfDvGRWjScCa',
      thumbnail: 'assets/images/banner.png',
      type: 'video',
      redirection_type: 1,
      slug: 'welcome'
    };

    const imageBanners: BannerItem[] = this.homeData.banners.map((banner: any, index: number) => ({
      id: banner.id || index + 1,
      title: banner.title || banner.bannerName || '',
      image: banner.image,
      poster: banner.image ? `${this.imageBase}/${banner.image}` : '',
      type: 'image' as const,
      redirection_type: banner.redirection_type,
      slug: banner.slug
    }));

    this.processedBanners = [videoEntry, ...imageBanners];
  }

  private getDefaultBanners(): BannerItem[] {
    return [{
      id: 0,
      title: 'TT Devassy Jewellery Collection',
      url: 'assets/video/banner/output.m3u8',
      poster: 'assets/images/banner.png',
      type: 'video'
    }];
  }

  // Lazy form initialization
  private initializeForm(): void {
    if (!this.form) {
      this.form = new FormGroup({
        name: new FormControl('', Validators.required),
        description: new FormControl('', Validators.required),
      });
    }
  }

  // Optimized banner click handler
  onBannerClick(banner: BannerItem): void {
    if (banner.redirection_type && banner.slug) {
      this.bannerRedirections(
        banner.redirection_type,
        banner.slug,
        banner.title,
        banner.bannerName,
        banner.bannerCategory
      );
    }
  }

  // Optimized banner redirections
  bannerRedirections(type: any, slug: any, title: any, bannerName?: any, bannerCategory?: any): void {
    // Track banner click if tracking is available
    if (bannerName && bannerCategory && typeof window !== 'undefined' && (window as any).webengage) {
      (window as any).webengage.track('Banner Clicked', {
        'Banner Name': bannerName,
        'Banner Category': bannerCategory,
      });
    }

    // Simplified routing logic
    const routes: { [key: number]: string } = {
      3: '/virtual-shopping',
      4: '/zora-collection',
      5: '/divo-collection',
      6: '/poyem-collection',
      7: '/orlin-collection',
      8: '/book-appointment',
      2: '/gold-schemes',
      1: '/advance-booking',
      9: '/gift-voucher',
      10: '/digital-gold',
      11: '/about',
    };

    if (type === 1) {
      this.router.navigate(['/detail', slug]);
    } else if (type >= 2 && type <= 4) {
      const routeType = type === 2 ? 'watches' : type === 3 ? 'collection' : 'category';
      this.router.navigate(['/listing', routeType, slug]);
    } else if (type === 5 && routes[slug]) {
      this.router.navigateByUrl(routes[slug]);
    }

    if (title === 'Zora') {
      this.router.navigateByUrl('/zora-collection');
    }
  }

  // Optimized wishlist functionality
  addToWishlist(id: any, isfavorite: any): void {
    if (!localStorage.getItem('isLogged')) {
      this.toggleModal();
      return;
    }

    const data = { type: isfavorite ? 0 : 1, productId: id };
    
    this.apiService.postData(apiEndPoints.WISHLIST, data)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res?.ErrorCode === 0) {
            this.loadHomeData();
          }
        },
        error: (error) => console.error('Wishlist update failed:', error)
      });
  }

  // Optimized file upload
  uploadImage(e: any): void {
    const file = e.files?.[0];
    if (!file) return;

    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    
    if (!this.fileExtArray.has(fileExtension)) {
      this.toast.error(`${fileExtension?.toUpperCase()} not supported. Please upload an image file`);
      return;
    }

    this.filedata = file;
    const reader = new FileReader();
    reader.onload = () => {
      this.imageURL = reader.result;
      this.cdr.markForCheck();
    };
    reader.readAsDataURL(file);
    this.isImageUploaded = true;
  }

  // Optimized testimonial submission
  submitTestimonial(): void {
    if (!localStorage.getItem('isLogged')) {
      this.testimonialModal = false;
      this.toggleModal();
      return;
    }

    if (!this.form?.valid) {
      this.isSubmitted = false;
      return;
    }

    if (!this.filedata) {
      this.isImageUploaded = false;
      return;
    }

    const formData = new FormData();
    formData.append('name', this.form.get('name')?.value);
    formData.append('description', this.form.get('description')?.value);
    formData.append('image', this.filedata);

    this.apiService.postTestimonial(apiEndPoints.post_testimonial, formData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res?.errorcode === 0) {
            this.isSubmitted = true;
            this.toast.success('Submitted successfully');
            this.popUpClose();
            this.imageURL = '';
            this.loadHomeData();
          }
        },
        error: (error) => console.error('Testimonial submission failed:', error)
      });
  }

  // Utility methods
  openWhatsapp(): void {
    if (typeof window !== 'undefined') {
      window.open('https://api.whatsapp.com/send?phone=9497916000', '_blank');
    }
  }

  popUp(): void {
    this.initializeForm(); // Lazy initialize form
    this.testimonialModal = !this.testimonialModal;
  }

  popUpClose(): void {
    this.testimonialModal = false;
  }

  toggleModal(): void {
    this.commonService.sendClickEvent();
    this.showModal = true;
  }

  bannerClicked(bannerName: any, bannerCategory: any, url: any): void {
    if (typeof window !== 'undefined' && (window as any).webengage) {
      (window as any).webengage.track('Banner Clicked', {
        'Banner Name': bannerName,
        'Banner Category': bannerCategory,
      });
    }
    this.router.navigate([url]);
  }

  // Getters for template
  get testimonialControls() {
    return this.form?.controls || {};
  }

  // Track by functions for ngFor optimization
  trackByProductId(index: number, item: any): any {
    return item?.id || index;
  }

  trackByIndex(index: number): number {
    return index;
  }
}