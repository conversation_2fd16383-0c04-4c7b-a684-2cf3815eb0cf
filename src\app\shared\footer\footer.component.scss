.jumping-dots-loader {
  width: 30px;
  height: 20px;
  position: absolute;
  transform: translate(-50%, -50%);
  left: 50%;
  top: 50%;
  bottom: 0;
  border-radius: 100%;
  /* position: relative; */
  margin: 0 auto;
}

.jumping-dots-loader span {
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 100%;
  background-color: rgb(250, 246, 247);
  margin: 5px 2px;
}

.jumping-dots-loader span:nth-child(1) {
  animation: bounce 1s ease-in-out infinite;
}

.jumping-dots-loader span:nth-child(2) {
  animation: bounce 1s ease-in-out 0.33s infinite;
}

.jumping-dots-loader span:nth-child(3) {
  animation: bounce 1s ease-in-out 0.66s infinite;
}

@keyframes bounce {

  0%,
  50%,
  100% {
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
  }

  25% {
    -webkit-transform: translateY(-3px);
    -ms-transform: translateY(-3px);
    -o-transform: translateY(-3px);
    transform: translateY(-3px);
  }
}

.footerTop {
  padding: 20px 0;
  border-top: 1px solid #D5DBDE;
  border-bottom: 1px solid #D5DBDE;


  .section {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .socialMedia {
    width: 50%;
  }

  .social {
    display: flex;
  }

  .app {
    display: flex;
    margin-top: 15px;

    a {
      padding-right: 15px;
    }

    img {
      width: 100px;
    }
  }

  .social a {
    color: #fff;
    background: #C6C6C6;
    border-radius: 50%;
    width: 34px;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 25px;
    transition: ease .9s;

    &:hover {
      background: #D2AB66;
      transform: rotate3d(1, 1, 1, 358deg);
      transition: ease .9s;
    }
  }

  .subscribeMail {
    display: flex;
    width: 480px;


    input[type="email"] {
      margin-bottom: 0;
      border: 1px solid #F3F3F3;
      padding-left: 29px;
      width: 100%;
    }

    .primary-button {
      width: 60%;
      position: relative;
    }
  }
}

.footerMenu {
  padding: 50px 0;
  font-size: 14px;
  border-bottom: 1px solid #D5DBDE;

  ul {
    list-style: none;
    padding: 0;
  }

  .section {
    display: flex;
  }

  .logoSection {
    width: 30%;
    padding-right: 40px;


    .getin {
      padding-top: 60px;
    }

    .contact {
      display: flex;
      padding: 5px 0 10px 0;
    }

    .contact span {
      padding-left: 10px;
    }

    .mail {
      display: block;
      margin-top: 15px;
      padding-bottom: 10px;
    }
  }

  h6 {
    font-size: 14px;
    font-family: "Gotham";
    text-transform: uppercase;
    font-weight: 600;
  }

  .menuSection {
    width: 70%;
    display: flex;

    .menuBox {
      width: 25%;
      padding-right: 10px;

      a {
        padding: 5px 10px 5px 0;
      }
    }
  }

}

.partners {
  padding: 40px 0;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    padding: 0 50px;
  }
}

.copyright {
  padding: 15px 0;
  background: #1D1D1D;

  .leftside {
    display: flex;
    align-items: center;

    a {
      margin-top: -9px;
      padding-left: 6px;
    }
  }

  .content {
    font-size: 14px;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

@media screen and (max-width:992px) {
  .footerTop {
    .section {
      flex-wrap: wrap;
    }

    .app {
      margin-top: 0;
    }

    .socialMedia {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding-bottom: 20px;
    }

    .social {
      padding-bottom: 0px;

      a {
        margin: 0 10px;
      }
    }

    .subscribeMail {
      width: 100%;
    }
  }

  .footerMenu {
    padding: 30px 0;

    .section {
      flex-wrap: wrap;
    }

    .logoSection {
      width: 100%;
      text-align: center;
      margin-bottom: 40px;
      padding-right: 0;

      .contact {
        justify-content: center;
      }
    }

    .getin {
      padding-top: 35px;
    }

    .menuSection {
      width: 100%;
      flex-wrap: wrap;

      .menuBox {
        width: 50%;
        text-align: center;
        padding: 10px;
        min-height: max-content;
      }
    }


  }


  .partners {
    padding: 20px 0;

    span {
      padding: 0 20px;
    }
  }

  .copyright {
    .content {
      display: flex;
      flex-direction: column-reverse;
      text-align: center;
    }

    .rightSide {
      padding-bottom: 7px;
    }
  }



}

@media screen and (max-width:480px) {
  .footerTop {
    .socialMedia {
      flex-direction: column;
    }

    .app {
      margin-top: 15px;

      a {
        padding: 0 10px;
      }
    }
  }


}

.footerNew {

  .connectSection {
    padding: 40px 0;
    background-color: #F4F2F0;

    .text {
      font-size: 16px;
    }

    .connect {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      justify-content: space-between;
      max-width: 965px;
      margin: 0 auto;

      .text {
        max-width: 112px;
      }
    }

    .box {
      background-color: #fff;
      border: 1px solid #D5DBDE;
      padding: 27px 20px 17px;
      margin: 0 15px;
      display: flex;
      gap: 12px;
      justify-content: center;
      align-items: center;
      font-size: 18px;
      padding: 36px;

      &:hover {
        img {
          transform: translateX(10px);
          transition: ease 0.8s;
        }
      }

      img {
        transition: ease 0.8s;
        width: 41px;
      }
    }

    h6 {
      margin-top: 15px;
    }
  }
}