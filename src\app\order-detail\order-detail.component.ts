import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { apiEndPoints } from '../ApiEndPoints';
import { environment } from 'src/environments/environment.prod';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-order-detail',
  templateUrl: './order-detail.component.html',
  styleUrls: ['./order-detail.component.scss'],
})
export class OrderDetailComponent implements OnInit {
  order_id: any;
  data: any;
  deliveryAddress: any;
  billingAddress: any;
  productdata: any;
  imageBase: any;
  isCancelled: boolean = false;
  confirmPopup: boolean = false;
  returnPopup: boolean = false;
  windowRef: any = window;

  constructor(
    private route: ActivatedRoute,
    private ApiService: ApiService,
    private taost: ToastrService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.order_id = params['id'];
      this.getOrderDetail();
    });
    this.imageBase = environment.imageBase;
  }

  getOrderDetail() {
    this.ApiService.postData(apiEndPoints.order_details, {
      order_product_id: this.order_id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.data = res?.Data;
        this.deliveryAddress = this.data?.delivery_address;
        this.billingAddress = this.data?.billing_address;
        this.productdata = this.data?.order_item;

        //Webengage Start
        if (
          this.data?.order_status.find((x: any) => x.name == 'Delivered')
            .is_active == 1
        ) {
          this.windowRef.webengage.track('Order Delivered', {
            'Order ID': this.productdata?.order_id,
            'Order Amount': Number(this.productdata?.price),
            'Product Details': [
              {
                'Product ID': this.productdata?.slug,
                'Product Name': this.productdata?.name,
                Quantity: 1,
                MRP: Number(this.productdata?.price),
              },
            ],
          });
        }
        //Webengage End
      }
    });
  }

  cancelOrder() {
    this.confirmPopup = false;
    this.ApiService.postData(apiEndPoints.cancel_order, {
      order_product_id: this.productdata?.order_product_id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.taost.success(res?.Message);
        this.getOrderDetail();

        //webengage start
        this.windowRef.webengage.track('Order Cancelled', {
          'Order ID': this.productdata?.order_id,
          'Order Amount': Number(this.productdata?.price),
          'Product Details': [
            {
              'Product ID': this.productdata?.slug,
              'Product Name': this.productdata?.name,
              Quantity: 1,
              MRP: Number(this.productdata?.price),
            },
          ],
        });
      } else this.taost.error(res?.Message);
    });
  }

  returnOrder() {
    this.returnPopup = false;
    this.ApiService.postData(apiEndPoints.return_order, {
      order_product_id: this.productdata?.order_product_id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.taost.success(res?.Message);
        this.getOrderDetail();
      } else this.taost.error(res?.Message);
    });
  }

  popupConfirm() {
    this.confirmPopup = true;
  }
}
