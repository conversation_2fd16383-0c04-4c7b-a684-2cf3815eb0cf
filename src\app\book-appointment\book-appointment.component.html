<!-- <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="square-jelly-box" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner>

<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif">
</div> -->

<div class="bookAppointment">
  <div class="container">
    <form [formGroup]="bookingForm">
      <div class="title">
        <h1>Book an Appointment</h1>
        <p>Enter your details below and we'll gladly give you a call to fix an appointment at your convenience. A
          specially assigned executive will be waiting for you when you visit our showroom you choose at the appointed
          time.</p>
      </div>
      <div class="form">
        <div class="leftSide">
          <img src="\assets\images\book-appointment.svg" alt="book appointment image" />
        </div>
        <div class="rightSide">
          <div class="field">
            <span>Name</span>
            <input type="text" formControlName="name" />
            <span *ngIf="bf.name.invalid && bf.name.touched">
              <span class="text-danger" *ngIf="bf.name.errors?.required"> Name is required </span>
            </span>
          </div>
          <div class="field">
            <span>Select Store</span>
            <ng-select class="filtername selectOption" [items]="stores" bindLabel="name" bindValue="id"
              [(ngModel)]="selectedStore" [ngModelOptions]="{standalone: true}" [clearable]="false"
              [searchable]="false">
            </ng-select>
            <span class="text-danger" *ngIf="!selectedStore">{{storeValidation}}</span>
          </div>
          <div class="field">
            <span>Mobile Number</span>
            <input type="tel" formControlName="mobile" />
            <span *ngIf="bf.mobile.invalid && bf.mobile.touched">
              <span class="text-danger" *ngIf="bf.mobile.errors?.required"> Mobile Number is required </span>
            </span>
            <span class="text-danger" *ngIf="bf.mobile.errors?.pattern">Please Enter Valid Mobile Number </span>
          </div>
          <div class="field">
            <span>Date</span>
            <input type="date" formControlName="date" />
            <!-- <ngx-datepicker [options]="options" formControlName="date" class="date"></ngx-datepicker> -->
            <span *ngIf="bf.date.invalid && bf.date.touched">
              <span class="text-danger" *ngIf="bf.date.errors?.required"> Date is required </span>
            </span>

            <!-- <ngb-datepicker
            id="dp"
            #dp
            name="datepicker"
            [ngModel]="datetime"
            (ngModelChange)="onDateChange($event, dp)"
          ></ngb-datepicker>
          <button
            class="btn btn-block btn-outline-secondary"
            [disabled]="!datetime?.day"
            type="button"
            (click)="toggleDateTimeState($event)"
          ></button> -->
            <!-- <button class="btn btn-outline-secondary bi bi-calendar3" (click)="d.toggle()" type="button"></button> -->
          </div>
          <div class="field time drop">
            <span>Time Slot</span>
            <div class="timePicker">
              <input [ngxTimepicker]="picker" formControlName="time">
              <ngx-material-timepicker #picker></ngx-material-timepicker>
              <span *ngIf="bf.time.invalid && bf.time.touched">
                <span class="text-danger" *ngIf="bf.time.errors?.required"> Time is required </span>
              </span>
            </div>
          </div>
          <div class="field">
            <span>What do you intend to buy</span>
            <textarea formControlName="lookingFor"></textarea>
          </div>
          <re-captcha formControlName="recaptchaReactive" [siteKey]="recaptcha_key"></re-captcha>
          <div class="submitBtn" (click)="submitBooking()"><input type="submit" value="Submit" /></div>
        </div>
      </div>
    </form>
  </div>
</div>
