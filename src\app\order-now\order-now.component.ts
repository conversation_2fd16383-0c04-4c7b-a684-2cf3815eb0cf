import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from 'src/app/Services/api.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import {
  Component,
  ElementRef,
  NgZone,
  OnInit,
  ViewChild,
} from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { environment } from 'src/environments/environment.prod';
declare var Razorpay: any;

@Component({
  selector: 'app-order-now',
  templateUrl: './order-now.component.html',
  styleUrls: ['./order-now.component.scss'],
})
export class OrderNowComponent implements OnInit {
  @ViewChild('form') form!: ElementRef;
  product_id: any;
  orderForm!: FormGroup;
  isChecked: boolean = false;
  isLoading: boolean = false;
  orderDetails: any;
  encRequest: any;
  accessCode: any;
  ccAvenueUrl: string = environment.ccAvenueProduction;
  ccAvenueBeta: string = environment.ccAvenueStaging;
  checkDomain: boolean = false;
  private rzp: any;

  constructor(
    private route: ActivatedRoute,
    private formbuilder: FormBuilder,
    private ApiService: ApiService,
    private toast: ToastrService,
    private router: Router,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    this.checkDomain = window.location.hostname.includes('beta');
    this.route.paramMap.subscribe((params) => {
      this.product_id = params.get('id');
    });
    this.initOrderForm();
    this.getOrderNowDetails();
  }

  initOrderForm() {
    this.orderForm = this.formbuilder.group({
      name: ['', [Validators.required]],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
    });
  }

  get of() {
    return this.orderForm.controls;
  }

  getOrderNowDetails() {
    this.ApiService.postData(apiEndPoints.ORDER_NOW_DETAILS, {
      productId: this.product_id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) this.orderDetails = res?.Data;
    });
  }

  checkBox() {
    this.isChecked = !this.isChecked;
  }

  submitOrder() {
    if (this.orderForm.invalid) {
      this.orderForm.markAllAsTouched();
      return;
    }

    let data = {
      name: this.orderForm.get('name')?.value,
      email: this.orderForm.get('email')?.value,
      mobile: this.orderForm.get('mobile')?.value,
      productId: this.product_id,
    };

    if (this.isChecked) {
      this.ApiService.postData(apiEndPoints.ORDER_NOW, data).subscribe(
        (res: any) => {
          if (res?.ErrorCode == 0) {
            const data = res?.Data.data;
            // this.accessCode = res?.Data?.accesscode
            // this.encRequest = res?.Data?.encrypted_data
            // setTimeout(() => {
            //   this.form.nativeElement.submit()
            // }, 500)
            const options = {
              key: data.api_key,
              order_id: data.razorpay_order_id,
              amount: data.amount, // amount in paise
              currency: 'INR',
              name: 'TT Devassy',
              description: 'Payment for order',
              handler: (response: any) => {
                this.isLoading = true;
                // Handle success
                this.ApiService.postData('ordernow-advance-payment', {
                  order_id: data.order_id,
                  ...response,
                }).subscribe((res: any) => {
                  this.isLoading = false;
                  if (res?.ErrorCode == 0) {
                    this.router.navigate(['/order-now-success']);
                  } else {
                    this.router.navigate(['/order-now-failed']);
                  }
                });
              },
              prefill: {
                name: this.orderForm.get('name')?.value,
                email: this.orderForm.get('email')?.value,
                contact: this.orderForm.get('mobile')?.value,
              },
              theme: {
                color: '#F37254',
              },
              modal: {
                ondismiss: () => {
                  this.ngZone.run(() => {
                    this.handlePaymentFailure(data.order_id);
                  });
                },
              },
            };

            this.rzp = new Razorpay(options);
            this.rzp.on('payment.failed', (response: any) => {
              this.ngZone.run(() => {
                this.handlePaymentFailure(data.order_id);
              });
            });
            this.rzp.open();
          } else this.toast.error(res?.Message);
        }
      );
    } else this.toast.info('Please Accept Terms&Conditions');
  }

  private handlePaymentFailure(orderId: string) {
    // Close the Razorpay modal if it's open
    if (this.rzp) {
      this.rzp.close();
    }

    this.router.navigateByUrl('/order-now-failed');
  }
}
