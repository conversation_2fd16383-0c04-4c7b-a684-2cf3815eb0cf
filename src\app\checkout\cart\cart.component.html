<!-- <div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div> -->

<div class="cartPage">
  <div class="container">
    <div class="contentSection">
      <div class="leftSide">
        <h1>Cart</h1>
        <span *ngIf="cartData?.products && cartData?.products?.length == 0">No Items Found</span>
        <ng-container *ngFor="let product of cartData?.products">
          <div class="cartItems">
            <div class="image" [routerLink]="['/detail',product.slug]"><img src="{{imageBase}}/{{product?.image}}" alt="product image">
            </div>
            <div class="details">
              <div class="name">{{product?.productName}}</div>
              <div class="rate">₹{{product?.price}}</div>
              <p style="color:red; font-size: 12px; padding-top: 5px;" *ngIf="product?.outOfStock">Product is out of stock</p>
            </div>
            <div class="delete" (click)="confirmationBox(product?.id)"><img src="\assets\images\my-account\delete.svg"
              alt="delete icon"></div>
            <!-- <div class="delete" (click)="removeFromCart(product?.id)"><img src="\assets\images\my-account\delete.svg"
                alt=""></div> -->
            <div class="fav" (click)="addToWishlist(product?.id,product?.isfavorite)"
              [ngClass]="{active: product.isfavorite}">
              <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="wishlist icon">
              <img class="fav-white" src="\assets\images\favorite.png" alt="wishlist icon">
            </div>
          </div>
        </ng-container>
      </div>
      <div class="rightSide">
        <div class="box">
          <div class="promo">
            <div class="head">Promo Code</div>
            <div class="enter">
              <span class="code"><input type="text" placeholder="Enter Promo Code" [(ngModel)]="promocode"></span>
              <span class="primary-button" (click)="applyPromoCode()">Apply</span>
            </div>
          </div>
          <div class="addSpecial">
            <div class="instruction">
              <div class="form-group">
                <input type="checkbox" id="instruction" (change)="onInstruction()">
                <label for="instruction">
                  <div class="add">Add Special Instruction </div>
                  <div class="type"><textarea name="instruction" id="instruction" cols="30" rows="4"
                      placeholder="Type Here" [(ngModel)]="instruction"></textarea></div>
                </label>
              </div>

            </div>
          </div>
          <div class="amounts">
            <div class="name">Subtotal</div>
            <div class="rate">₹{{cartData?.sub_total}}</div>
          </div>
          <div class="amounts">
            <div class="name">Shipping Charge</div>
            <div class="rate">₹{{cartData?.shipping_charge}}</div>
          </div>
          <div class="amounts discount" *ngFor="let item of cartData?.discount_lists">
            <div class="name">{{item?.label}}</div>
            <div class="rate">-₹{{item?.amount}}</div>
          </div>
          <div class="amounts grand">
            <div class="name">Grand Total</div>
            <div class="rate">₹{{cartData?.grand_total}}</div>
          </div>
          <div class="redeem">
            <div class="form-group">
              <input type="checkbox" id="redeem" [checked]="selectedScheme == 'cash' || selectedScheme == 'weight'"
                [disabled]="!cartData?.possible_scheme_balance&&!cartData?.possible_scheme_weight_balance"
                (change)="onScheme($event)">
              <label for="redeem">
                <div class="add">
                  <div class="text">Redeem Using Scheme Balance
                      <div class="info">
                        <img src="\assets\images\wallet\Vector.svg" alt="info icon">
                        <div class="infoTxt">
                          <ul>
                            <li>The scheme balance must not exceed the total amount.</li>
                            <li>To redeem the weight balance, the total purchased weight must be greater than or equal to the weight balance.</li>
                            <li>The scheme balance must be used in its entirety.</li>
                            <li>Other wallets or points cannot be used with this.</li>
                          </ul>
                        </div>
                      </div>
                  </div>
                </div>
              </label>
              <div class="balance">
                <input type="radio" name="scheme" id="1" [disabled]="!cartData?.possible_scheme_balance" value="cash"
                  (change)="onSchemeChange($event)" [checked]="selectedScheme == 'cash'">
                <label for="1">Cash Scheme Balance: ₹{{cartData?.scheme_balance}}</label>
              </div>
              <div class="balance">
                <input type="radio" name="scheme" id="2" [disabled]="!cartData?.possible_scheme_weight_balance"
                  value="weight" (change)="onSchemeChange($event)" [checked]="selectedScheme == 'weight'">
                <label for="2">Weight Scheme Balance : {{cartData?.scheme_weight_balance}} gm</label>
              </div>
            </div>
            <div class="form-group">
              <input type="checkbox" id="main" [disabled]="!cartData?.possible_mainwallet_balance"
                [(ngModel)]="selectedMainWallet" (change)="onMainBalanceChange()">
              <label for="main">
                <div class="add">
                  <div class="text">Main Wallet
                    <div class="info">
                      <img src="\assets\images\wallet\Vector.svg" alt="info icon">
                      <div class="infoTxt">
                        <ul>
                          <li>The main  wallet must be used in its entirety.</li>
                          <li>Cannot  be used with  scheme  balance </li>
                        </ul>
                    </div>
                    </div>
                  </div>
                  <div class="balance">Balance : ₹{{cartData?.wallet_balance}}</div>
                </div>
              </label>
            </div>
            <div class="form-group redeemReward">
              <input type="checkbox" id="reward" [(ngModel)]="selectedRewardPoints" (click)="popUp()" [disabled]="!cartData?.possible_rewardpoints">
              <label for="reward">
                <div class="add">
                  <div class="text">Redeem Using Reward Points
                    <div class="info">
                      <img src="\assets\images\wallet\Vector.svg" alt="info icon">
                      <div class="infoTxt">
                        <ul>
                          <li>Minimum 10,000 Points Required for redemption</li>
                          <li>10000 Points = Rs 100/-</li>
                          <li>Cannot  be used with  scheme  balance.</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                  <div class="balance">Balance : {{cartData?.reward_points_balance}} points</div>
                </div>
              </label>
            </div>
            <div class="primary-button golden" (click)="continue()">Continue to Payment</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>





<div class="referPopup" [ngClass]="Popupredeem?'show':'hide'">
  <div class="box">
    <div class="close" (click)="popUpClose('redeem')"><img src="\assets\images\close-white.svg" alt="close icon"></div>
    <div><input type="text" placeholder="Enter reward points to redeem" [(ngModel)]="redeem_points"></div>
    <div class="button">
      <div class="primary-button white" (click)="popUpClose('redeem')">Cancel</div>
      <div class="primary-button golden" (click)="redeemRewardPoints()">Redeem</div>
    </div>
  </div>
</div>

<div class="referPopup" [ngClass]="confirmPopup?'show':'hide'">
  <div class="box">
    <div class="close" (click)="popUpClose('confirm')"><img src="\assets\images\close-white.svg" alt="close icon"></div>
    <div><p>Are you sure?</p></div>
    <div class="button">
      <div class="primary-button white" (click)="popUpClose('confirm')">No</div>
      <div class="primary-button golden" (click)="removeFromCart()">Yes</div>
    </div>
  </div>
</div>
