import { apiEndPoints } from '../ApiEndPoints';
import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-terms',
  templateUrl: './terms.component.html',
  styleUrls: ['./terms.component.scss']
})
export class TermsComponent implements OnInit {
  data: any;

  constructor(private ApiService:ApiService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.GET_TnC).subscribe((res:any) => {
      if(res?.errorcode == 0) {
        this.data = res?.data
      }
    })
  }

}
