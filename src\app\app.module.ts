import { NgSelectModule } from '@ng-select/ng-select';
import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AngularFireModule } from '@angular/fire';
import { AngularFireAuthModule } from '@angular/fire/auth';
import { AngularFirestoreModule } from '@angular/fire/firestore';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { ToastrModule } from 'ngx-toastr';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { SharedModule } from './shared/shared.module';
import SwiperCore, { Swiper, SwiperOptions, Virtual, Navigation, FreeMode, Pagination } from 'swiper';
import { SwiperComponent } from 'swiper/angular';
SwiperCore.use([Virtual, FreeMode, Navigation, Pagination]);
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SwiperModule } from 'swiper/angular';
import { HomeComponent } from './home/<USER>';
import { AboutComponent } from './about/about.component';
import { FaqComponent } from './faq/faq.component';
import { BookAppointmentComponent } from './book-appointment/book-appointment.component';
import { DatepickerModule } from 'ng2-datepicker';
import { ListingComponent } from './listing/listing.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgxSpinnerModule } from "ngx-spinner";
import { InfiniteScrollModule } from "ngx-infinite-scroll";
// import { NgOtpInputModule } from  'ng-otp-input';
import { NgOtpInputModule } from 'ng-otp-input';
import { DetailComponent } from './detail/detail.component';
import { NgxImageZoomModule } from 'ngx-image-zoom';
import { ZoraCollectionComponent } from './zora-collection/zora-collection.component';
import { ContactComponent } from './contact/contact.component';
import { AdvanceBookingComponent } from './advance-booking/advance-booking.component';
import { BookingComponent } from './booking/booking.component';
import { GoldSchemesComponent } from './gold-schemes/gold-schemes.component';
import { GoldAccountComponent } from './gold-account/gold-account.component';
import { PersonalInformationComponent } from './personal-information/personal-information.component';
import { WishlistComponent } from './wishlist/wishlist.component';
import { MyOrdersComponent } from './my-orders/my-orders.component';
import { OrderDetailComponent } from './order-detail/order-detail.component';
import { ReferalsComponent } from './referals/referals.component';
import { MyGoldSchemesComponent } from './my-gold-schemes/my-gold-schemes.component';
import { GiftCardsComponent } from './gift-cards/gift-cards.component';
import { PersonalizeComponent } from './gift-cards/personalize/personalize.component';
import { WalletComponent } from './wallet/wallet.component';
import { MyAdvanceBookingsComponent } from './my-advance-bookings/my-advance-bookings.component';
import { DesignYourJewelleryComponent } from './design-your-jewellery/design-your-jewellery.component';
import { VirtualShoppingComponent } from './virtual-shopping/virtual-shopping.component';
import { BlogComponent } from './blog/blog.component';
import { BlogDetailsComponent } from './blog-details/blog-details.component';
import { BuybackPolicyComponent } from './buyback-policy/buyback-policy.component';
import { TermsComponent } from './terms/terms.component';
import { CancelationPolicyComponent } from './cancelation-policy/cancelation-policy.component';
import { RefundPolicyComponent } from './refund-policy/refund-policy.component';
import { PrivacyPolicyComponent } from './privacy-policy/privacy-policy.component';
import { InterceptorInterceptor } from './interceptors/interceptor.interceptor';
import { CommonModule, DatePipe } from '@angular/common';
import { OrderNowComponent } from './order-now/order-now.component';
import { NgxSliderModule } from '@angular-slider/ngx-slider';
import { NgChartsModule } from 'ng2-charts';
import { DiamondEducationComponent } from './diamond-education/diamond-education.component';
import { OurCertificationsComponent } from './our-certifications/our-certifications.component';
import { AdvanceBookingPlacedComponent } from './advance-booking-placed/advance-booking-placed.component';
import { AdvanceBookingFailedComponent } from './advance-booking-failed/advance-booking-failed.component';
import { OrderNowSuccessComponent } from './order-now-success/order-now-success.component';
import { OrderNowFailedComponent } from './order-now-failed/order-now-failed.component';
import { OrderNowDetailComponent } from './order-now-detail/order-now-detail.component';
import { GiftCardSuccessComponent } from './gift-card-success/gift-card-success.component';
import { GiftCardFailedComponent } from './gift-card-failed/gift-card-failed.component';
import { GiftCardDetailComponent } from './gift-card-detail/gift-card-detail.component';
import { VirtualTryComponent } from './virtual-try/virtual-try.component';
import { PhoneNumberUtil } from 'google-libphonenumber';
import { RecaptchaFormsModule, RecaptchaModule } from 'ng-recaptcha';

import { GiftVoucherComponent } from './gift-voucher/gift-voucher.component';
import { DigitalGoldComponent } from './digital-gold/digital-gold.component';
import { NotFoundComponent } from './not-found/not-found.component';
import { ServerErrorComponent } from './server-error/server-error.component';
import { RingSizeGuideComponent } from './ring-size-guide/ring-size-guide.component';
import { BangleSizeGuideComponent } from './bangle-size-guide/bangle-size-guide.component';
import { QRCodeModule } from 'angularx-qrcode';
import { ShippingPolicyComponent } from './shipping-policy/shipping-policy.component';
import { ServiceWorkerModule } from '@angular/service-worker';
import { environment } from 'src/environments/environment.prod';
import { CareerComponent } from './career/career.component';  

@NgModule({
  declarations: [
    AppComponent,
    HomeComponent,
    AboutComponent,
    FaqComponent,
    BookAppointmentComponent,
    ListingComponent,
    DetailComponent,
    ZoraCollectionComponent,
    ContactComponent,
    AdvanceBookingComponent,
    BookingComponent,
    GoldSchemesComponent,
    GoldAccountComponent,
    PersonalInformationComponent,
    WishlistComponent,
    MyOrdersComponent,
    OrderDetailComponent,
    ReferalsComponent,
    MyGoldSchemesComponent,
    GiftCardsComponent,
    PersonalizeComponent,
    WalletComponent,
    MyAdvanceBookingsComponent,
    DesignYourJewelleryComponent,
    VirtualShoppingComponent,
    BlogComponent,
    BlogDetailsComponent,
    BuybackPolicyComponent,
    TermsComponent,
    CancelationPolicyComponent,
    RefundPolicyComponent,
    PrivacyPolicyComponent,
    OrderNowComponent,
    DiamondEducationComponent,
    OurCertificationsComponent,
    AdvanceBookingPlacedComponent,
    AdvanceBookingFailedComponent,
    OrderNowSuccessComponent,
    OrderNowFailedComponent,
    OrderNowDetailComponent,
    GiftCardSuccessComponent,
    GiftCardFailedComponent,
    GiftCardDetailComponent,
    VirtualTryComponent,
    GiftVoucherComponent,
    DigitalGoldComponent,
    NotFoundComponent,
    ServerErrorComponent,
    RingSizeGuideComponent,
    BangleSizeGuideComponent,
    ShippingPolicyComponent,
    CareerComponent,
  ],
  imports: [
    BrowserModule,
    AppRoutingModule,
    SharedModule,
    SwiperModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    NgbModule,
    DatepickerModule,
    BrowserAnimationsModule,
    NgxMaterialTimepickerModule,
    NgOtpInputModule,
    NgxImageZoomModule,
    AngularFireModule.initializeApp(environment.firebaseConfig),
    AngularFireAuthModule,
    AngularFirestoreModule,
    ToastrModule.forRoot(),
    CommonModule,
    NgSelectModule,
    NgxSliderModule,
    NgChartsModule,
    NgxPaginationModule,
    NgxSpinnerModule,
    InfiniteScrollModule,
    RecaptchaFormsModule,
    RecaptchaModule,
    QRCodeModule,
    ServiceWorkerModule.register('ngsw-worker.js', {
      enabled: environment.production,
      registrationStrategy: 'registerWhenStable:30000',
      scope: '/'
    })
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    { provide: HTTP_INTERCEPTORS, useClass: InterceptorInterceptor, multi: true },
    PhoneNumberUtil,
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
