.myAccount {
    padding: 40px 0 100px;
}
.sideMenu {
    width: 270px;
}
.contentSection {
    display: flex;

    .contents {
        width: calc(100% - 270px);
        padding-left: 50px;
    }
}

h1 {
    font-size: 44px;
    margin-bottom: 25px;

    @media screen and (max-width:992px) {
        margin-bottom: 10px;
        font-size: 30px;
    }

    @media screen and (max-width:480px) {
        font-size: 25px;
    }
}


.head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    span{font-weight: 600;}
    .add {
        width: auto;
    }
    .add {
        font-size: 14px;
        font-weight: 600;
        position: relative;
        text-align: right;
        cursor: pointer;
        &::before {
            content: "+";
            position: absolute;
            left: -25px;
            top: 1px;
            border: 1px solid #000;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            text-align: end;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

}

.giftSection {
    padding: 30px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    display: flex;
    flex-wrap: wrap;
    margin: 1px;
    .boxes {
        width: 31.33%;
        background-image: url(/assets/images/gift-cards/Frame1.svg);
        background-repeat: no-repeat;
        background-size: cover;
        background-color: #D2AB66;
        min-height: 240px;
        margin: 1%;
        display: flex;
        padding: 25px;
        align-items: flex-end;
        justify-content: flex-start;
        cursor: pointer;
        .content{
            width: 110px;
            color: #fff;
            p{
                padding-top: 25px;
            }
        }
    }

}


@media screen and (max-width: 1260px) {
    .giftSection  .boxes {
        width: 48%;
    }
}
@media screen and (max-width: 992px) {
    .myAccount {
        padding: 20px 0 50px;
      }
    .sideMenu {
        width: 60px;
    }
    .contentSection .contents {
        width: calc(100% - 60px);
    }
}
@media screen and (max-width: 640px) {
    .contentSection {
      flex-direction: column;
      .contents {
        width: 100%;
        padding-left: 0;
        padding-top: 30px;
      }

    }
    .sideMenu {
        width: 100%;
    }

  }
@media screen and (max-width: 480px) {
    .giftSection{
        padding: 20px;
        .boxes {
            width: 100%;
            margin: 5px 0;
        }
    }
}
