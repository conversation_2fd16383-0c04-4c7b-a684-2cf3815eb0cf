import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from 'src/app/Services/api.service';
import { ToastrService } from 'ngx-toastr';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-wallet',
  templateUrl: './wallet.component.html',
  styleUrls: ['./wallet.component.scss']
})
export class WalletComponent implements OnInit {
  mainTab: any = true;
  schemeTab: any = false;
  rewardTab: any = false;
  schemeTab1: any = true;
  schemeTab2: any = false;

  PopupGift: any = false;
  redeemForm!: FormGroup
  data: any;
  isLoading: boolean = false
  windowRef = window

  constructor(
    private fb: FormBuilder, private spinner: NgxSpinnerService,
    private toast: ToastrService,
    private ApiService: ApiService
  ) { }

  ngOnInit(): void {
    this.getWallet()
    this.initRedeemForm()
  }

  getWallet() {
    // this.isLoading = true
    this.ApiService.getData(apiEndPoints.my_wallet).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.data = res?.Data
        // this.isLoading = false
      }
    })
  }

  initRedeemForm() {
    this.redeemForm = this.fb.group({
      gift_id: ['', Validators.required],
      redemption_code: ['', Validators.required]
    })
  }

  redeemGift() {
    if (this.redeemForm.invalid) {
      this.toast.error('Invalid')
    }
    let data = { gift_card_id: this.redeemForm.get('gift_id')?.value, redemption_code: this.redeemForm.get('redemption_code')?.value }

    this.ApiService.postData(apiEndPoints.REDEEM_GIFT, data).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.windowRef.webengage.track('Redeem Card', {
          'Card Id': this.redeemForm.get('gift_id')?.value,
          'Redemption Code': this.redeemForm.get('redemption_code')?.value
        });
        this.toast.success(res?.Message)
        this.popUpClose()
        this.getWallet()
      } else this.toast.error(res?.Message)
    })
  }

  main() {
    this.mainTab = true;
    this.schemeTab = false;
    this.rewardTab = false;
  }

  scheme() {
    this.schemeTab = true;
    this.mainTab = false;
    this.rewardTab = false;
  }

  reward() {
    this.rewardTab = true;
    this.mainTab = false;
    this.schemeTab = false;
  }

  scheme1() {
    this.schemeTab1 = true;
    this.schemeTab2 = false;
  }

  scheme2() {
    this.schemeTab2 = true;
    this.schemeTab1 = false;
  }

  redeemPopUp() {
    this.windowRef.webengage.track('Redeem Gift Card Clicked', {});
    this.PopupGift = !this.PopupGift;
  }

  popUpClose() {
    this.PopupGift = false;
  }
}
