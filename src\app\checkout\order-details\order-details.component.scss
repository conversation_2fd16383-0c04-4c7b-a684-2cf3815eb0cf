.cartPage {
  padding: 40px 0;

  .contentSection {
    display: flex;
    flex-wrap: wrap;
    .leftSide {
      width: calc(100% - 460px);
      padding-right: 25px;
    }

    .rightSide {
      width: 460px;
    }
  }
}
.paging {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;

  .number {
      background: #D6D6D6;
      width: 26px;
      height: 26px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      margin-right: 13px;
      color: #fff;
      justify-content: center;
  }

  .numbering {
      display: flex;
      font-size: 14px;
      align-items: center;
      position: relative;
      padding-right: 130px;
  }

  .number.active {
      background: #D0A962;
  }

  .numbering:last-child {
      padding-right: 0;
  }

  .numbering::before {
      content: "";
      position: absolute;
      right: 10px;
      top: 13px;
      border-top: 1.5px dashed #E1E1E1;
      width: 40%;
      height: 1px;
  }
  .numbering:last-child::before {
      display: none;
  }
  }

.cartItems {
  display: flex;
  align-items: center;
  position: relative;
  padding: 12px 100px 12px 20px;
  border: 1px solid #D5DBDE;
  margin-bottom: 25px;

  .image {
    width: 92px;
    height: 92px;
  }

  .details {
    width: 310px;
    padding-left: 25px;
  }

  .delete {
    position: absolute;
    right: 20px;
    top: 5px;
    cursor: pointer;
    padding: 10px;
  }

  .fav {
    position: absolute;
    right: 55px;
    top: 6px;
    cursor: pointer;
    padding: 10px;
  }

  .name {
    font-size: 14px;
  }

  .rate {
    font-size: 16px;
    font-weight: 600;
  }
}

.box {
  border: 1px solid #D5DBDE;
  padding: 25px;

  .promo {
    font-size: 14px;
    margin-bottom: 25px;
    .head {
      font-weight: 600;
      padding-bottom: 15px;
    }

    .enter {
        display: flex;
        justify-content: space-between;
    }
    .code {
        width: calc(100% - 110px);
        border: 1px solid #D5DBDE;
    }

    .primary-button {
        width: 100px;
    }

    input[type="text"] {
        margin-bottom: 0;
        border: none;
        height: 100%;
    }
  }

    .form-group {
        display: block;
        margin-bottom: 15px;


        input {
            padding: 0;
            height: initial;
            width: initial;
            margin-bottom: 0;
            display: none;
            cursor: pointer;
        }

        label {
            position: relative;
            cursor: pointer;
            font-weight: 400;
            font-size: 13px;
            display: flex;
            flex-direction: column;
        }

        label:before {
            content:'';
            -webkit-appearance: none;
            background-color: transparent;
            border: 1px solid #D5DBDE;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
            padding: 10px;
            display: inline-block;
            position: absolute;
            vertical-align: middle;
            cursor: pointer;
            margin-right: 10px;
        }
        input:checked + label:before {
          background: #D0A962;
          border: 1px solid #D0A962;
        }
        input:checked + label:after {
            content: '';
            display: block;
            position: absolute;
            top: 2px;
            left: 8px;
            width: 6px;
            height: 14px;
            border: solid #fff;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }
        input + label .type{
            display: none;
        }
        input:checked + label .type{
            display: block;

        }
        textarea{
            border: 1px solid #D5DBDE;
            margin-top: 20px;
        }
        .add{
            padding-left: 35px;
            padding-top: 2px;

        }
    }

     .amounts {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        font-size: 14px;
        &.discount{
            .name{
                color: #10347F;
            }
            .rate{
                color: #ED5A5A;
            }
        }
        &.grand{
            padding-top: 3px;
            font-weight: 600;
            font-size: 16px;
            margin-bottom: 25px;
        }
    }

    // .redeem {
    //     .form-group{
    //         border: 1px solid #D5DBDE;
    //         padding: 14px 20px;
    //     }
    //     .text {
    //         font-weight: 600;
    //         font-size: 14px;
    //     }
    //     label::before {
    //         top: 12px;
    //     }

    //     input:checked + label:after {
    //         top: 14px;
    //     }

    // }
    .redeem {
      .form-group{
          border: 1px solid #D5DBDE;
          padding: 14px 20px;
      }
      .text {
          font-weight: 600;
          font-size: 14px;
      }
      label::before {
          top: 12px;
      }

      input:checked + label:after {
          top: 14px;
      }




      .balance {
        font-size: 13px;
        position: relative;
      }

      [type="radio"]:checked + label,
      [type="radio"]:not(:checked) + label
      {
        position: relative;
        padding-left: 38px;
        padding-top: 2px;
        cursor: pointer;
        line-height: 20px;
        display: block;
        color: #666;
        margin-left: 0;
      }
      [type="radio"]:checked + label:before,
      [type="radio"]:not(:checked) + label:before {
        content: '';
        position: absolute;
        right: 0;
        left: auto;
        top: 0;
        width: 17px;
        height: 17px;
        padding: 0;
        border: 2px solid #D2AB66;
        border-radius: 100%;
        background: #fff;
      }
      [type="radio"]:checked + label:after,
      [type="radio"]:not(:checked) + label:after {
        content: '';
        width: 13px;
        height: 13px;
        background: #D2AB66;
        position: absolute;
        top: 6px;
        right: 6px;
        border-radius: 100%;
        -webkit-transition: all 0.2s ease;
        transition: all 0.2s ease;
      }
      [type="radio"]:not(:checked) + label:after {
        opacity: 0;
        -webkit-transform: scale(0);
        transform: scale(0);
      }
      [type="radio"]:checked + label:after {
        opacity: 1;
        -webkit-transform: scale(1);
        transform: scale(1);
      }
      .form-group input[type="radio"]:checked + label:after {
        width: 9px;
        height: 9px;
        right: 14px;
        left: auto;
        border: none;
        transform: none;
        top: 4px;
      }

      .form-group input[type="radio"]:checked + label:before {background: #fff;width: 17px;border: 2px solid #D2AB66;height: 17px;padding: 0;}

  }


}


.redeemReward{
    .add::before {
        content: "";
        position: absolute;
        right: 0;
        background-image: url(/assets/images/checkout/Vector1.svg);
        height: 14px;
        width: 7px;
        top: 15px;
        background-size: contain;
        background-repeat: no-repeat;
    }
}

.referPopup {
    position: fixed;
    width: 100%;
    height: 100%;
    background: #00000040;
    top: 0;
    left: 0;
    z-index: 99;

    .box {
      display: flex;
      flex-direction: column;
      width: 695px;
      margin: 0 auto;
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
      position: fixed;
      top: 50%;
      padding: 30px;
      left: 50%;
      z-index: 9;
      background: #fff;
      transform: translate(-50%, -50%);
      input{
        border: 1px solid #E0E0E0;
        margin-bottom: 20px;
      }
    }

    &.show {
      display: flex !important;
    }

    &.hide {
      display: none;
    }
    .button {
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
        .primary-button {
            width: 200px;
            margin: 0 0 0 20px;
        }
    }

}




@media screen and (max-width:1200px) {

.cartPage .contentSection .leftSide {
    width: 100%;
    padding-right: 0;
}

.cartPage .contentSection .rightSide {
    width: 100%;
}
}
@media screen and (max-width:640px) {
.paging  {
    flex-direction: column;
    max-width: 250px;
    margin: 0 auto;
    .numbering{
      padding-right: 0;
      padding-bottom: 50px;
    }

    .numbering::before {
      border-left: 1.5px dashed #E1E1E1;
      width: 1px;
      height: 30px;
      left: 13px;
      top: 35px;
    }
}
.contentSection .leftSide .box {
    padding: 50px 20px;
}

.sameAs {
    position: relative;
    padding-top: 20px;
}
.form{
    .threeSection .field  {
        width: 100%;
    }

    .twoSection .field {
        width: 100%;
    }
}
.box{
    padding: 20px;

    .form-group {
        padding: 10px;
    }
}
.cartItems {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding-right: 15px;
  .details {
      width: 100%;
      padding-left: 0;
  }
  .delete,.fav {
    padding: 5px;
  }
}
}
