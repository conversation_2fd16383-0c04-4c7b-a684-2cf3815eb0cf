<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" />
</div>

<div class="cartPage">
  <div class="container">
    <div class="head">Order Now</div>
    <div class="contentSection">
      <div class="leftSide">
        <div class="deliveryDetail">
          <div class="box">
            <form [formGroup]="orderForm">
              <div class="form">
                <div class="twoSection">
                  <div class="field">
                    <span>Name</span>
                    <input type="text" formControlName="name" />
                    <span *ngIf="of.name.invalid && of.name.touched">
                      <span class="text-danger" *ngIf="of.name.errors?.required"
                        >Name is required
                      </span>
                    </span>
                  </div>
                  <div class="field">
                    <span>Email Id</span>
                    <input type="email" formControlName="email" />
                    <span *ngIf="of.email.invalid && of.email.touched">
                      <span
                        class="text-danger"
                        *ngIf="of.email.errors?.required"
                      >
                        Email is required
                      </span>
                    </span>
                    <span class="text-danger" *ngIf="of.email.errors?.pattern"
                      >Please Enter Valid Email</span
                    >
                  </div>
                </div>
                <div class="twoSection">
                  <div class="field">
                    <span>Mobile Number</span>
                    <input type="text" formControlName="mobile" />
                    <span *ngIf="of.mobile.invalid && of.mobile.touched">
                      <span
                        class="text-danger"
                        *ngIf="of.mobile.errors?.required"
                      >
                        Mobile Number is required
                      </span>
                    </span>
                    <span class="text-danger" *ngIf="of.mobile.errors?.pattern"
                      >Please Enter Valid Mobile Number</span
                    >
                  </div>
                  <div class="field">
                    <span class="need"
                      >You need to pay 90% of the total amount as advance when
                      you order a product</span
                    >
                    <span class="hereBy">
                      <label
                        ><input
                          type="checkbox"
                          [(ngModel)]="isChecked"
                          [ngModelOptions]="{ standalone: true }"
                          (click)="checkBox()"
                        /><span> I hereby accept the </span></label
                      >
                      <a routerLink="/terms"> Terms&Conditions</a>
                    </span>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
      <div class="rightSide">
        <div class="box">
          <div class="amounts">
            <div class="name">Advance Amount</div>
            <div class="rate">₹{{ orderDetails?.advance }}</div>
          </div>
          <div class="amounts grand">
            <div class="name">Grand Total</div>
            <div class="rate">₹{{ orderDetails?.total }}</div>
          </div>
          <div class="redeem">
            <div class="primary-button golden" (click)="submitOrder()">
              Continue
            </div>
          </div>
        </div>
      </div>
      <!-- <form *ngIf="!checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
        action="{{ccAvenueUrl}}?command=initiateTransaction&enc_request={{encRequest}}&access_code={{accessCode}}&request_type=XML&response_type=XML&version=1.1">
        <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
        <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
      </form>
      <form *ngIf="checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
        action="{{ccAvenueBeta}}?command=initiateTransaction">
        <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
        <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
      </form> -->
    </div>
  </div>
</div>
