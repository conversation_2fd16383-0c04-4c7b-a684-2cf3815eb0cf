import { Component, OnInit } from '@angular/core';
import { ApiService } from '../Services/api.service';
import { apiEndPoints } from '../ApiEndPoints';

@Component({
  selector: 'app-bangle-size-guide',
  templateUrl: './bangle-size-guide.component.html',
  styleUrls: ['./bangle-size-guide.component.scss']
})
export class BangleSizeGuideComponent implements OnInit {
  data:any

  constructor(private apiService: ApiService) { }

  ngOnInit(): void {
    this.apiService.getData(apiEndPoints.bangleSizeGuide).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.data = res?.data
      }
    })
  }

}
