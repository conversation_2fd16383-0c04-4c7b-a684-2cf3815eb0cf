<div class="video-slider-container">
  <div class="video-wrapper">
    <!-- Video Player - Only show for video slides -->
    <video
      #videoPlayerDesktop
      *ngIf="isCurrentSlideVideo() && !isMobile"
      muted
      preload="metadata"
      class="video-player desktop"
      [class.loading]="isLoading"
      [poster]="getCurrentVideo().poster"
    ></video>

    <video
      #videoPlayerMobile
      *ngIf="isCurrentSlideVideo() && isMobile"
      muted
      preload="metadata"
      class="video-player mobile"
      [class.loading]="isLoading"
      [poster]="getCurrentVideo().poster"
    ></video>

    <!-- Image Display - Only show for image slides -->
    <div
      *ngIf="isCurrentSlideImage()"
      class="image-slide"
      (click)="onSlideClick()"
      [attr.alt]="getCurrentSlide()?.title"
    >
      <img
        [src]="getCurrentImageUrl()"
        [alt]="getCurrentSlide()?.title"
        class="slide-image"
        loading="lazy"
      />
    </div>

    <!-- Progress Bar -->
    <!-- <div
      class="progress-bar"
      [style.width.%]="progressPercentage"
      *ngIf="autoSlideEnabled"
    ></div> -->

    <!-- Navigation Arrows -->
    <button
      class="nav-arrow nav-arrow-left"
      (click)="previousSlide()"
      [disabled]="isLoading"
      aria-label="Previous slide"
    >
      <img src="assets/images/arrow-left.svg" alt="previous slide" />
    </button>

    <button
      class="nav-arrow nav-arrow-right"
      (click)="nextSlideAction()"
      [disabled]="isLoading"
      aria-label="Next slide"
    >
      <img src="assets/images/arrow-right.svg" alt="next slide" />
    </button>

    <!-- Slide Type Indicator -->
    <div class="slide-type-indicator">
      <span *ngIf="isCurrentSlideVideo()" class="type-badge video">VIDEO</span>
      <span *ngIf="isCurrentSlideImage()" class="type-badge image">IMAGE</span>
    </div>

    <!-- Next Banner Highlight -->
    <div class="next-banner" (click)="onNextBannerClick()" *ngIf="nextSlide">
      <div class="next-banner-preview">
        <img
          [src]="nextSlide.thumbnail || nextSlide.poster"
          [alt]="nextSlide.name + ' preview'"
        />
        <div class="preview-type-badge">
          <span *ngIf="nextSlide.type === 'video'" class="type-icon">▶</span>
          <span *ngIf="nextSlide.type === 'image'" class="type-icon">🖼</span>
        </div>
      </div>
      <div class="next-banner-content">
        <div class="next-banner-header">
          <span>NEXT</span>
        </div>
        <div class="next-banner-title">{{ nextSlide.title }}</div>

        <!-- Slide Indicators -->
        <div class="slide-indicators">
          <button
            *ngFor="let source of allSources; let i = index"
            class="indicator"
            [class.active]="i === currentSlideIndex"
            [class.video]="source.type === 'video'"
            [class.image]="source.type === 'image'"
            (click)="goToSlide(i)"
            [attr.aria-label]="source.name || source.title"
          ></button>
        </div>
      </div>
    </div>

    <!-- Slide Counter -->
    <div class="slide-counter">
      <span>{{ currentSlideIndex + 1 }} / {{ totalSlides }}</span>
    </div>

    <!-- Click overlay for banner interactions -->
    <div
      class="click-overlay"
      (click)="onSlideClick()"
      [attr.title]="'Click to view ' + getCurrentSlide()?.title"
    ></div>
  </div>
</div>
