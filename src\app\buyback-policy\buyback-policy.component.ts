import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';

@Component({
  selector: 'app-buyback-policy',
  templateUrl: './buyback-policy.component.html',
  styleUrls: ['./buyback-policy.component.scss']
})
export class BuybackPolicyComponent implements OnInit {
  data: any;

  constructor(private ApiService:ApiService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.BUYBACK_POLICY).subscribe((res:any) => {
      if(res?.errorcode == 0) {
        this.data = res?.data
      }
    })
  }

}
