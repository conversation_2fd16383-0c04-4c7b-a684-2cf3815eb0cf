.booking {
    padding: 40px 0 0px;

    h1 {
      font-size: 44px;
      margin-bottom: 25px;
    }

    .paging {
      display: flex;
      justify-content: center;


      .number {
        background: #D6D6D6;
        width: 26px;
        height: 26px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        margin-right: 13px;
        color: #fff;
        justify-content: center;
      }

      .numbering {
        display: flex;
        font-size: 14px;
        align-items: center;
        position: relative;
        padding-right: 130px;
        cursor: pointer;
      }

      .number.active {
        background: #D0A962;
      }

      .numbering:last-child {
        padding-right: 0;
      }

      .numbering::before {
        content: "";
        position: absolute;
        right: 30px;
        top: 13px;
        border-top: 1.5px dashed #E1E1E1;
        width: 20%;
        height: 1px;
      }
      .numbering:last-child::before {
        display: none;
      }
    }
}
.field .text-danger{bottom: -3px;}
.field .dob{bottom: -17px;}
.preview{
  max-width: 200px;
  max-height: 200px;
  width: 120px;
  height: 120px;
  object-fit: cover;
  object-position: top;
}

.registerPopup {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  .box {
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    padding: 30px;
    text-align: center;
    max-width: 434px;
    margin: 0 auto;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 9;
    background: #fff;
    transform: translate(-50%, -50%);
  }

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }
  .head {
      text-align: center;
      font-size: 20px;
      font-weight: 600;
      padding-bottom: 10px;
      display: block;
    }

    p {
      text-align: center;
      font-size: 14px;
      margin-bottom: 15px;
    }

    .field {
      width: 100%;
    }

    .check {
      text-align: center;
      margin-bottom: 10px;
      margin-top: 35px;

      span {
        padding-left: 10px;
        font-size: 13px;
      }

      a {
        color: #CB9F52;
        text-decoration: underline;
      }
    }


    input[type=text],
    input[type=url],
    input[type=number],
    input[type=email],
    input[type=password],
    input[type=number],
    select,
    textarea,
    input[type=date] {
      border: 1px solid #D5DBDE;
      padding: 20px 16px;
    }

    .bottom {
      width: 100%;
      font-size: 12px;
      padding-top: 30px;

      .resend {
        cursor: pointer;
        color: #D2AB66;
      }
    }


  .close {
    position: absolute;
    right: -34px;
    top: -18px;
    cursor: pointer;
    opacity: unset;
    padding: 10px;
  }


}


.bookingForm {
    margin: 60px auto;
    max-width: 774px;

    .head {
      text-align: center;
      font-size: 24px;
      font-weight: 500;
      margin-bottom: 30px;
    }

    .sections {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      flex-wrap: wrap;
    }

    .leftSide {
      width: 49%;
    }

    .rightSide {
      width: 49%;

    }

    .field {
      .leftSide {
        display: flex;
        width: 50%;
      }

      input,
      textarea {
        border: 1px solid #c8c4c4;
      }

      textarea {
        width: 100%;
        height: 136px;
        margin-bottom: 8px;
      }

      .rightSide {
        display: flex;
        width: 50%;
        justify-content: flex-end;

        .filtername {
          display: flex;
          align-items: center;
        }
      }

      .name {
        color: #787878;
        font-size: 14px;
        padding-bottom: 6px;
      }

      .filtername {
        padding: 12px 20px;
        border: 1px solid #c8c4c4;
        margin-right: 15px;
        font-size: 14px;
        cursor: pointer;
        width: 100%;
        height: 47px;
        position: relative;
        margin-bottom: 15px;

        img {
          padding-right: 10px;
        }
      }

      .drop {
        display: none;
      }

      .selectOption {

        span {
          padding-right: 5px;
        }

        .selectMenu {
          font-size: 14px;
          color: #000;
          position: relative;
          padding-right: 25px;

          &.sort {
            &::before {
              content: "";
              background-image: url(/assets/images/angle-down.svg);
              position: absolute;
              right: 0px;
              top: 3px;
              width: 20px;
              height: 20px;
            }
          }
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          position: absolute;
          opacity: 0;
          box-shadow: 0px 2px 2px #D5DBDE;
          background: #fff;
          width: 100%;
          left: 0px;
          top: 49px;
          z-index: -1;

          &.active {
            z-index: 3;
            opacity: 1;
            transition: all linear .2s;
            z-index: 1;
          }

          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: -12px;
            width: 100%;
            height: 20px;

          }

          li {
            padding: 0px 15px;

            &:hover {
              color: #D2AB66;
            }
          }
        }

      }
    }

    .primary-button {
      max-width: 278px;
    }

    .box {
      flex-direction: column;
      display: flex;
      padding: 24px 30px;
      justify-content: center;
      border: 1px dashed #C9C9C9;
      margin-top: 27px;
    }

    .rateOn {
      font-size: 16px;
      padding-bottom: 5px;


    }

    .rt {
      font-size: 16px;
      font-weight: 600;
      padding-bottom: 10px;
    }
    .box.finalDetails {
      display: flex;
      margin-bottom: 60px;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: flex-start;
      .rates {
        width: 50%;
    }
    }

  .hereBy {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    input[type="checkbox"] {
      margin-right: 10px;
      margin-top: 1px;
    }
    a{
      color: #D0A962;
      padding-left: 4px;
    }
  }
  .buttons {
    display: flex;
    justify-content: center;
    margin-top: 30px;
    .primary-button{
      margin: 0 10px;
    }
  }

  .uploading {
    width: 120px;
    height: 120px;
    border: 1px solid #D5DBDE;
    position: relative;
    margin-bottom: 30px;
    &::before{
        content: "";
        background-image: url(/assets/images/gold-scheme/upload.svg);
        width: 35px;
        height: 34px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        background-repeat: no-repeat;
        background-size: contain;
    }
    input[type="file"] {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        outline: none;
        opacity: 0;
    }
    .side {
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
      color: #A6A6A6;
      font-size: 14px;
      width: 100%;
      text-align: center;
    }
  }
  &.uploadingSection {
    padding: 40px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    .field .name{
      width: 45%;
      font-weight: 600;
    }
    .photos{
      margin-top: 20px;
      .uploading{
        margin-right: 25px;
      }
    }
    .field + .field{
      border-top: 1px dashed #c8c4c4;
      margin-top: 20px;
      padding-top: 30px;
    }
  }

}

.country {
  position: relative;
  select {
    position: absolute;
    left: 0;
    width: 80px;
    margin-bottom: 0;
    height: 47px;
    top: 0;
    z-index: 1;
    border: none !important;
    padding: 0 10px !important;
}

input[type=number] {
  padding-left: 90px !important;
}
}


  .helpiline {
    margin-top: 100px;
    background: #D2AB66;
    text-align: center;
    padding: 15px 0;
    font-size: 25px;
    color: #fff;


    .content {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    a {
      display: flex;
      align-items: center;
      color: #fff;
      margin-left: 30px;

      img {
        padding-right: 12px;
      }
    }
  }


.calender .ng-untouched {
    display: block;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 24px;
      height: 24px;
      z-index: -1;
      right: 10px;
      top: 10px;
      // background-image: url(/assets/images/calander.svg);
      background-repeat: no-repeat;
    }
  }

.datepicker-container.datepicker-default {
width: 100%;
}

[type="radio"]:checked,
[type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
}
[type="radio"]:checked + label,
[type="radio"]:not(:checked) + label
{
    position: relative;
    padding-left: 28px;
    cursor: pointer;
    line-height: 20px;
    display: inline-block;
    color: #666;
}
[type="radio"]:checked + label:before,
[type="radio"]:not(:checked) + label:before {
    content: '';
    position: absolute;
    left: 0;
    top: 2px;
    width: 16px;
    height: 16px;
    border: 2px solid #D2AB66;
    border-radius: 100%;
    background: #fff;
}
[type="radio"]:checked + label:after,
[type="radio"]:not(:checked) + label:after {
    content: '';
    width: 8px;
    height: 8px;
    background: #D2AB66;
    position: absolute;
    top: 6px;
    left: 4px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
}
[type="radio"]:not(:checked) + label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
}
[type="radio"]:checked + label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
}

.radioBtn {
    padding-bottom: 12px;
    align-items: center;
    width: 45%;
    .checkbox+.checkbox, .radio+.radio {
        margin-top: 10px;
    }
    .radio {
        margin: 0 50px 13px 0px;
        color: #000;
        font-size: 14px;
    }

    label.radio-label {
        color: #000;
    }
}


@media screen and (max-width:992px) {
  .booking  {
    padding: 30px 0;

    h1 {
      font-size: 30px;
      margin-bottom: 10px;
    }

    .paging  {
      flex-direction: column;
      max-width: 250px;
      margin: 0 auto;
      .numbering {
        padding-right: 0;
        padding-bottom: 50px;
      }

      .numbering::before {
        border-left: 1.5px dashed #E1E1E1;
        width: 1px;
        height: 30px;
        left: 13px;
        top: 35px;
      }
    }

  }
  .bookingForm {
    margin: 0px auto;
  }

  .helpiline {
    margin-top: 20px;
    .content {
      flex-wrap: wrap;
      font-size: 12px;
    }

    a {
      margin-left: 20px;
      img {
        width: 27px;
      }
    }
  }

}

@media screen and (max-width:640px) {
  .bookingForm {
    .leftSide , .rightSide {
      width: 100%;
    }
    &.uploadingSection{
      .field{
        flex-wrap: wrap;
        .name {
          width: 100%;
          margin-bottom: 5px;
        }
      }
      .photos{
        flex-wrap: wrap;
        .radioBtn {
          width: 100%;
        }
        .uploading{
          margin-right: 10px;
          margin-bottom: 15px;
        }
      }
    }


  }
  .radioBtn .radio{
    width: 100%;
  }
}

@media screen and (max-width:480px) {
  .booking {
    h1 {
      font-size: 25px;
    }
  }
}
