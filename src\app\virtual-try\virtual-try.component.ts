import { Component, Input, OnInit, Renderer2 } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-virtual-try',
  templateUrl: './virtual-try.component.html',
  styleUrls: ['./virtual-try.component.scss']
})
export class VirtualTryComponent implements OnInit {
  @Input() psku!: string;

  constructor(private renderer: Renderer2, private route: ActivatedRoute) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.psku = params['tryon_id']
    })
    const script = this.renderer.createElement('script');
    this.renderer.setAttribute(script, 'src', 'https://camweara.com/integrations/camweara_api.js');
    this.renderer.setAttribute(script, 'onload', `loadTryOnButton({
      psku:'${this.psku}',
      page:'product',
      company:'Ttdevassyjewellery',
      nonTryOnProductsReport:'true',
      buynow:{enable:'true'},
      appendButton:{class:'try'},
      MBappendButton:{class:'try'},
      styles:{
      tryonbutton:{backgroundColor:'white',color:'black'},
      tryonbuttonHover:{backgroundColor:'black',color:'white',border:'none',},
      MBtryonbutton:{width:'100%',borderRadius:'25px'}
      },
      tryonButtonData:{text:'Virtual try',faIcon:'fa fa-camera'},
    });`);
    this.renderer.appendChild(document.body, script);
  }

}
