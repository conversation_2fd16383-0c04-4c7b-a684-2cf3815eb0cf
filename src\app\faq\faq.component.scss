.faqSection {
  padding: 0 0 100px;
  overflow: hidden;
  position: relative;

  .faq {
    padding: 0 0 20px;
    max-width: 800px;
    margin: 0 auto;
  }

  .circleBlue {
    position: absolute;
    left: -50px;
    top: 30px;
    z-index: -1;
  }

  .dots {
    position: absolute;
    right: 0;
    top: 100px;
  }

  // accordion
  button.faq-accordion {
    cursor: pointer;
    padding: 20px 70px 20px 20px;
    width: 100%;
    border: none;
    text-align: left;
    outline: none;
    transition: 0.4s;
    background-color: transparent;
    font-size: 16px;
    position: relative;
    font-weight: 600;
  }

  button.faq-accordion::before {
    content: "";
    position: absolute;
    right: 20px;
    top: 0;
  }

  /*button not active*/
  button.faq-accordion:after {
    content: "";
    background-image: url(/assets/images/plus.svg);
    background-repeat: no-repeat;
    background-size: contain;
    width: 30px;
    height: 30px;
    position: absolute;
    right: 20px;
    top: 16px;
  }

  /* minus button */
  button.faq-accordion.active:after {
    background-image: url(/assets/images/minus.svg);
  }

  .option {
    display: block;
    padding-bottom: 15px;
  }

  .course-panel {
    padding: 0px 18px 0 0px;
    background-color: transparent;
    max-height: 0;
    overflow: hidden;
    width: 0;
    line-height: 1.6em;
    letter-spacing: 0.4px;
    font-weight: 400;
    font-style: normal;

    p {
      padding: 0 50px 30px 20px;
      font-size: 14px;
    }

    &.open-accordion {
      max-height: max-content;
      width: 100%;
    }
  }

  .drops {
    margin-bottom: 20px;
    border: 1px solid #DDDDDD;
    border-radius: 10px;
  }

  .drops:last-child .course-panel {
    border: none;
  }

  .secondSec {
    display: none;
    border-top: 1px solid #14264B26;
  }

  .secondSec.show {
    display: block;
  }

  .viewMore {
    width: 280px;
    margin: 40px auto 20px;
    display: block;

    img {
      transform: rotate(90deg);
      margin-left: 10px;
    }
  }

  .viewMore.hide {
    display: none;
  }

  h1 {
    font-size: 44px;
    margin-bottom: 25px;
  }
}


@media screen and (max-width: 992px) {
  .faqSection {
    padding: 0 0 30px;
  }

  .faqSection h1 {
    font-size: 30px;
    margin-bottom: 10px;
  }
}
@media screen and (max-width: 640px) {
  .faqSection button.faq-accordion {
    font-size: 14px;
    padding: 15px 60px 15px 15px;
  }

  .faqSection .course-panel p {
    padding: 0 40px 20px 15px;
  }
  .faqSection button.faq-accordion:after{
    top: 11px;
  }
}

@media screen and (max-width: 480px) {
  .faqSection h1 {
    font-size: 25px;
  }
}
