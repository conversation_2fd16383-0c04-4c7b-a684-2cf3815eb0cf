import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CommonService {
  constructor() {}
  private modal = new Subject<any>();
  private login = new Subject<any>();
  private logout = new Subject<any>();
  private wishlist = new Subject<any>();
  private cart = new Subject<any>();
  private authorizationModal = new BehaviorSubject<boolean>(false);
  private isMobile = new BehaviorSubject<boolean>(false);
  sendClickEvent() {
    this.modal.next();
  }

  getClickEvent(): Observable<any> {
    return this.modal.asObservable();
  }

  sendLoginEvent() {
    this.login.next();
  }

  getLoginEvent(): Observable<any> {
    return this.login.asObservable();
  }

  sendLogoutEvent() {
    this.logout.next();
  }

  getLogoutEvent(): Observable<any> {
    return this.logout.asObservable();
  }

  sendWishlistEvent() {
    this.wishlist.next();
  }

  getWishlistEvent(): Observable<any> {
    return this.wishlist.asObservable();
  }

  sendCartEvent() {
    this.cart.next();
  }

  getCartEvent(): Observable<any> {
    return this.cart.asObservable();
  }

  getAuthorization() {
    this.authorizationModal.next(true);
  }

  getAuthorizationEvent(): Observable<any> {
    return this.authorizationModal.asObservable();
  }

  checkMobile(value: boolean) {
    this.isMobile.next(value);
  }
  checkMobileEvent() {
    return this.isMobile.asObservable();
  }
}
