import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from '../ApiEndPoints';
import { Response } from '../shared/models/common-model';
import { contact } from '../shared/models/contact.model';
import { DomSanitizer } from '@angular/platform-browser';

@Component({
  selector: 'app-contact',
  templateUrl: './contact.component.html',
  styleUrls: ['./contact.component.scss'],
})
export class ContactComponent implements OnInit {
  contactForm!: FormGroup;
  isChecked: boolean = false;
  zoom: number = 12;
  contactData: contact[] = [];
  constructor(
    private fb: FormBuilder,
    private ApiService: ApiService,
    private toast: ToastrService
  ) {}

  ngOnInit(): void {
    this.initContactForm();
    this.getContactDetails();
  }

  initContactForm() {
    this.contactForm = this.fb.group({
      name: ['', Validators.required],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      message: ['', Validators.required],
    });
  }

  get cf() {
    return this.contactForm.controls;
  }

  mapClicked($event: MouseEvent) {
    // this.markers.push({
    //   lat: $event.coords.lat,
    //   lng: $event.coords.lng,
    //   draggable: true,
    // });
  }

  getContactDetails() {
    this.ApiService.getContactDetails(apiEndPoints.store_details).subscribe({
      next: (response: Response<contact[]>) => {
        if (response.ErrorCode === 0) {
          this.contactData = response.Data;
        }
      },
    });
  }

  submitData() {
    if (this.contactForm.invalid) {
      this.contactForm.markAllAsTouched();
      return;
    }

    if (this.isChecked) {
      this.ApiService.postData(
        apiEndPoints.CONTACT_US,
        this.contactForm.value
      ).subscribe((res: any) => {
        if (res?.errorcode == 0) {
          this.isChecked = false;
          this.toast.success('Submitted Successfully');
          this.contactForm.reset();
        } else this.toast.error(res?.message);
      });
    } else this.toast.error('Verify the check box');
  }
}
