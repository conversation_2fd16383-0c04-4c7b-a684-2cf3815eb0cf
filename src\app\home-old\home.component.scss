// ::ng-deep{

//     .digitalWear #tryonButton{
//       background: #CE9599;
//       color: white;
//       border-radius: 0%;
//       height: 75px;
//       width:215px

//    }

//   .digitalWear #tryonButton:hover {
//     background-color:white;
//     color: #CE9599;

//   }
// }
.slideItems {
  p.description {
    font-size: 15px;
  }
}
.slideItems {
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  a {
    display: block;
  }
}

.specification {
  background: #fffbf3;
  padding: 40px 0;

  .contents {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .box {
    display: flex;
    flex-direction: column;
    padding: 30px 0;
    align-items: center;
    width: 110px;

    &:hover img {
      transform: rotate(360deg);
      transition: ease 0.8s;
    }
  }

  .text {
    text-align: center;
    padding-top: 15px;
    font-size: 14px;
    font-family: "Libre Bodoni";
  }
}

.topTrending {
  padding-top: 100px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .imageSection {
    width: 65%;
    display: flex;
  }

  .image {
    width: max-content;
  }

  .image.first {
    padding-top: 50px;

    &:hover img {
      transform: translateY(10px);
      transition: all linear 0.8s;
    }

    img {
      transition: all linear 0.8s;
    }
  }

  .image.second {
    padding-bottom: 50px;
    padding-left: 50px;

    &:hover img {
      transform: translateY(10px);
      transition: all linear 0.8s;
    }

    img {
      transition: all linear 0.8s;
    }
  }

  .content {
    width: 30%;
  }
}

.forMenWomen {
  padding: 100px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 -7px;

  .for {
    height: 562px;
    position: relative;
    margin: 0 7px;
    width: 50%;
    overflow: hidden;

    &:hover {
      .image {
        transform: scale(1.1);
        transition: all linear 0.8s;
      }

      h2 img {
        transform: translateX(10px);
        transition: all linear 0.8s;
      }
    }

    .image {
      transition: all linear 0.8s;
      height: 100%;
      object-fit: cover;
      width: 100%;
      object-position: top;
    }

    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 0;
      background: linear-gradient(
        180deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0.35) 100%
      );
    }
  }

  h2 {
    position: absolute;
    bottom: 12%;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    width: 100%;
    text-align: center;
    z-index: 1;

    img {
      transition: all linear 0.8s;
    }

    span {
      padding-right: 10px;
    }
  }
}

.trendingSection {
  background-color: #fffbf3;
  padding: 100px 0;

  .slide {
    position: relative;
    display: flex;
    align-items: center;
    //height: 52px;
    width: 100%;
    justify-content: center;
    bottom: -20px;
  }

  .slideItems {
    text-align: center;
    background: #ffffff;
    box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.06);
    margin: 0px 8px;
    height: 100%;

    &:hover {
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);

      .productImage {
        img {
          transform: translateY(-10px);
          transition: ease 0.7s;
        }
      }
    }

    .productImage {
      padding: 30px 30px 26px;
      overflow: hidden;

      img {
        transition: ease 0.9s;
        max-height: 206px;
        object-fit: cover;
      }
    }
  }

  .productName {
    font-size: 14px;
    padding: 0 30px 15px;
    line-height: 20px;
  }

  .price {
    font-size: 14px;
    font-weight: 600;
    padding: 0 30px 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    .offer {
      padding: 0 5px;
    }

    .old {
      text-decoration: line-through;
      padding: 0 5px;
      color: #b3b3b3;
    }
  }

  .ar {
    position: absolute;
    top: 50px;
    width: 23px;
    left: 40px;
    cursor: pointer;
    z-index: 2;
  }

  .wishlist {
    z-index: 2;
    position: absolute;
    right: 40px;
    display: flex;
    top: 50px;
    cursor: pointer;

    .fav-gold {
      display: none;
    }

    &.active .fav-gold {
      display: block;
    }

    &.active .fav {
      display: none;
    }
  }
}

.brandsLove {
  padding: 100px 0 150px;

  .slide {
    position: relative;
    display: flex;
    align-items: center;
    //height: 52px;
    width: 100%;
    justify-content: center;
    bottom: 0;
    margin-top: 20px;
  }

  .slideItems {
    width: 100%;
    height: 100%;

    &:hover img {
      transform: translate3d(10px, 10px, 10px);
      transition: all linear 0.8s;
    }

    img {
      transition: all linear 0.8s;
    }
  }

  .brandImage {
    position: absolute;
    bottom: 30px;
    left: 50%;
    width: 50%;
    transform: translateX(-50%);
  }
}

.bookGold {
  display: block;
  position: relative;

  // &::before {
  //   content: "";
  //   background: rgba(159, 113, 30, 0.67);
  //   width: 100%;
  //   height: 100%;
  //   position: absolute;
  // }

  .image {
    width: 100%;
    height: 100%;
  }

  .content {
    display:none;
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    text-align: center;
    color: #fff;
    max-width: 302px;

    h2 {
      color: #fff;
      margin-bottom: 30px;
    }

    .book {
      background: #fff;
      color: #d2ab66;
      padding: 18px 70px;

      &:hover {
        background: #d2ab66;
        color: #fff;
      }
    }
  }
}

.digitalWear {
  margin: 100px 0;
  background-image: url(/assets/images/digital-bg.svg);
  background-repeat: no-repeat;
  background-size: 100% 110%;
  overflow: hidden;

  .digitalImg:hover {
    transform: translateY(50px);
    transition: all linear 0.9s;
  }

  .digitalImg {
    transition: all linear 0.7s;
    width: calc(100% - 368px);
  }

  .digital {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .content {
    width: 368px;

    h2 {
      margin-bottom: 25px;
    }

    p {
      margin-bottom: 30px;
    }
  }

  .digitalBtn {
    background: #ce9599;
    padding: 18px 40px;
    color: #fff;

    &:hover {
      background: #fff;
      color: #ce9599;
    }
  }
}

.hotCategory {
  padding-bottom: 100px;

  .slide {
    position: relative;
    display: flex;
    align-items: center;
    //height: 52px;
    width: 100%;
    justify-content: center;
    bottom: 0;
    margin-top: 20px;
  }

  .slideItems {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;

    img {
      transition: all linear 0.8s;
    }
  }

  .name {
    position: absolute;
    right: 10px;
    left: auto;
    bottom: 10px;
    color: #fff;
    font-size: 20px;
    z-index: 1;
    font-family: "Libre Bodoni";
  }

  .slideItems::before {
    content: "";
    position: absolute;
    width: 0;
    height: 100%;
    background: #00000085;
    z-index: 1;
    right: 0;
    transition: ease 0.5s;
    top: 0;
  }

  .slideItems:hover::before {
    width: 100%;
    transition: ease 0.5s;
  }
}

.perfectBride {
  display: flex;

  .image:hover:before {
    background: #00000080;
    width: 100%;
    height: 100%;
    transition: ease 0.5s;
  }

  .image:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
  }

  .leftSection {
    width: 70%;
    display: flex;
    flex-direction: column;

    .top {
      display: flex;
      justify-content: space-between;
      padding-bottom: 15px;
      height: 50%;

      .image {
        width: 32.33%;
        margin-right: 1%;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .bottom {
      display: flex;
      height: 50%;

      .image {
        &.image_2 {
          width: 32.33%;
          margin-right: 1%;
          position: relative;
        }

        img {
          width: 100%;
          height: 100%;
        }

        &.image_1 {
          width: calc(100% - 34.33%);
          margin-right: 1%;
          position: relative;
        }
      }
    }

    .name {
      position: absolute;
      right: 7%;
      bottom: 7%;
      color: #fff;
      font-family: "LibreBodoni-Regular";
    }
  }

  .rightSection {
    width: 30%;

    .image {
      height: 100%;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.clientSay {
  padding: 100px 0 0;
  .heading {
    justify-content: space-between;
    h2 {
      width: 70%;
    }
  }
  .slide {
    position: relative;
    display: flex;
    align-items: center;
    bottom: 0;
    width: 200px;
    margin: 0 auto 30px;
    justify-content: center;
  }

  .slideItems {
    text-align: center;
  }

  .image {
    width: 93px;
    height: 93px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
  }

  .name {
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 10px;
  }

  .date {
    color: #858585;
    font-size: 14px;
    padding-bottom: 30px;
  }

  .testimonialBtn {
    max-width: 270px;
    margin: 0 auto;
  }
}

.miSection {
  margin-top: 100px;
  display: flex;

  .leftImage {
    width: 55%;
    height: 346px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: top;
    }
  }

  .rightContent {
    width: 45%;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px 60px;
    color: #25675d;
    background-image: url(/assets/images/mi-bg.svg);
    background-repeat: no-repeat;
    background-size: cover;

    h2 {
      color: #25675d;
    }
  }

  .join {
    background: #25675d;
    color: #fff;
    padding: 16px 40px;
    width: 230px;
    font-size: 16px;
    text-align: center;
    margin-top: 15px;

    &:hover {
      background: #225a52;
    }
  }
}

.featured {
  .container {
    padding-right: 0;
  }

  .slideItems {
    text-align: center;
    background: #ffffff;
    box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.06);
    margin: 20px 8px;

    &:hover {
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);

      .productImage {
        img {
          transform: translateY(-10px);
          transition: ease 0.7s;
        }
      }
    }

    .productImage {
      padding: 30px 30px 0;
      overflow: hidden;

      img {
        transition: ease 0.9s;
        max-height: 308px;
        object-fit: cover;
      }
    }
  }

  .productName {
    font-size: 14px;
    padding: 0 30px 15px;
    line-height: 20px;
  }

  .price {
    font-size: 14px;
    font-weight: 600;
    padding: 0 30px 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;

    .offer {
      padding: 0 5px;
    }

    .old {
      text-decoration: line-through;
      padding: 0 5px;
      color: #b3b3b3;
      font-size: 12px;
    }
  }

  .ar {
    position: absolute;
    top: 50px;
    width: 23px;
    left: 40px;
    cursor: pointer;
    z-index: 2;
  }

  .wishlist {
    z-index: 2;
    position: absolute;
    right: 40px;
    display: flex;
    top: 50px;
    cursor: pointer;

    .fav-gold {
      display: none;
    }

    &.active .fav-gold {
      display: block;
    }

    &.active .fav {
      display: none;
    }
  }

  .leftSection {
    width: 20%;
    position: relative;
  }

  .rightSection {
    width: 80%;
  }

  .contents {
    display: flex;
    align-items: center;
  }
}

.collectionSec {
  display: flex;
  justify-content: space-between;
  padding-top: 100px;

  .box {
    width: 50%;
    text-align: center;
    padding: 35px 35px 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    img {
      width: 100%;
    }
  }

  .head {
    margin: 0 auto 50px;
    width: 90%;

    h2 {
      font-size: 34px;
    }
  }

  p {
    color: #1d1d1d;
  }
}

.special {
  display: block;
  position: relative;
  margin-top: 100px;
  height: 450px;
  position: relative;

  h2 {
    color: #fff;
    margin-bottom: 25px;
  }

  video {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    top: 0;
    left: 0;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .content {
    margin: 0 auto;
    width: 290px;
    text-align: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .explore {
    width: 240px;
    background: #fff;
    padding: 16px 0 13px;
    color: #000;
    font-weight: bold;
  }
}

.blogSec {
  padding-bottom: 50px;
  padding-top: 100px;

  .blogSection {
    display: flex;
    justify-content: space-between;
    margin: 0 -7px;

    .blogs {
      width: 25%;
      padding: 0 7px;
      padding-bottom: 40px;
    }

    .blogImage {
      width: 100%;
    }

    .featuredIcon {
      position: absolute;
      left: 0;
      top: 20px;
    }

    .image {
      position: relative;
      padding-bottom: 20px;
    }

    .date {
      color: #1d1d1d66;
      padding-bottom: 12px;
      font-size: 12px;
    }

    .text {
      padding-bottom: 12px;
      font-size: 16px;
      font-weight: 600;
    }

    .name {
      font-size: 14px;
    }
  }

  .view {
    display: block;
    text-align: center;

    a {
      background: #000;
      color: #fff;
      padding: 18px;
      width: 200px;
      font-size: 16px;
    }
  }
}

.WhatsGoing {
  padding-top: 100px;

  .head {
    display: flex;
    align-items: center;
    font-size: 30px;
    font-weight: 600;
    padding-bottom: 33px;

    span {
      padding-left: 10px;
    }
  }
  .social {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .socialMedia {
      padding-bottom: 60px;
      width: 30%;
      &:nth-child(2) {
        width: 40%;
      }

      &:nth-child(3) {
        width: 26%;
      }
      iframe {
        height: 337px;
        ._10b4 {
          max-height: 100% !important;
        }

        ._2lqg {
          height: 100% !important;
        }
      }
    }
  }
}

.connectSection {
  padding: 40px 0;

  .connect {
    display: flex;
    justify-content: space-between;
    margin: 0 -15px;
  }

  .box {
    width: 33.33%;
    text-align: center;
    border: 1px solid #d5dbde;
    padding: 27px 20px 17px;
    margin: 0 15px;

    &:hover {
      img {
        transform: translateX(10px);
        transition: ease 0.8s;
      }
    }

    img {
      transition: ease 0.8s;
    }
  }

  h6 {
    margin-top: 15px;
  }
}

.testimonialPopup {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  .box {
    display: flex;
    max-width: 820px;
    width: 95%;
    margin: 0 auto;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 9;
    background: #fff;
    transform: translate(-50%, -50%);
    overflow: hidden;
  }

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }

  .content {
    padding: 55px 40px 40px;
    width: 100%;
  }

  .field {
    input,
    textarea {
      border: 1px solid #c8c4c4;
    }

    textarea {
      width: 100%;
      height: 136px;
      margin-bottom: 8px;
    }
    .name {
      color: #787878;
      font-size: 14px;
      padding-bottom: 6px;
      font-weight: 600;
    }

    .filtername {
      padding: 12px 20px;
      border: 1px solid #c8c4c4;
      margin-right: 15px;
      font-size: 14px;
      cursor: pointer;
      width: 100%;
      height: 47px;
      position: relative;
      margin-bottom: 15px;

      img {
        padding-right: 10px;
      }
    }

    .drop {
      display: none;
    }

    .selectOption {
      span {
        padding-right: 5px;
      }

      .selectMenu {
        font-size: 14px;
        color: #000;
        position: relative;
        padding-right: 25px;

        &.sort {
          &::before {
            content: "";
            background-image: url(/assets/images/angle-down.svg);
            position: absolute;
            right: 0px;
            top: 3px;
            width: 20px;
            height: 20px;
          }
        }
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        position: absolute;
        opacity: 0;
        box-shadow: 0px 2px 2px #d5dbde;
        background: #fff;
        width: 100%;
        left: 0px;
        top: 49px;
        z-index: -1;

        &.active {
          z-index: 3;
          opacity: 1;
          transition: all linear 0.2s;
          z-index: 1;
        }

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: -12px;
          width: 100%;
          height: 20px;
        }

        li {
          padding: 0px 15px;

          &:hover {
            color: #d2ab66;
          }
        }
      }
    }
  }

  .uploading {
    width: 120px;
    height: 120px;
    border: 1px solid #d5dbde;
    position: relative;
    margin-bottom: 30px;

    &::before {
      content: "";
      background-image: url(/assets/images/gold-scheme/upload.svg);
      width: 35px;
      height: 34px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-repeat: no-repeat;
      background-size: contain;
    }

    input[type="file"] {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      outline: none;
      opacity: 0;
    }
  }

  .submitBtn {
    text-align: right;

    .primary-button {
      max-width: 151px;
    }
  }

  p {
    text-align: center;
    font-size: 14px;
    margin-bottom: 15px;
  }

  .field {
    width: 100%;
  }

  input[type="text"],
  input[type="url"],
  input[type="tel"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  select,
  textarea,
  input[type="date"] {
    border: 1px solid #d5dbde;
    padding: 20px 16px;
  }

  input[type="tel"] {
    padding-left: 90px !important;
  }
  .close {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    opacity: unset;
    padding: 10px;
    z-index: 1;

    img {
      filter: invert(100%);
    }
  }
}

@media screen and (max-width: 992px) {
  .title {
    padding-bottom: 20px;

    h2 {
      margin-bottom: 10px;
    }
  }

  .specification {
    padding: 0;

    .container {
      padding-right: 0;
    }

    .box {
      img {
        width: 40px;
      }
    }
  }

  .topTrending {
    padding-top: 50px;
    flex-direction: column;

    .imageSection {
      width: 100%;
      margin-bottom: 40px;
    }

    .image.second {
      padding-left: 10px;
    }

    .content {
      width: 100%;
      text-align: center;
    }
  }

  .forMenWomen {
    padding: 50px 0;
    flex-direction: column;

    .for {
      width: 100%;
      margin-bottom: 20px;
      height: auto;
    }

    h2 {
      bottom: 4%;
    }
  }

  .trendingSection {
    padding: 50px 0;
  }

  .trendingSection .swiper,
  .brandsLove .swiper,
  .hotCategory .swiper,
  .clientSay .swiper {
    padding-bottom: 20px;
  }

  .brandsLove {
    padding: 50px 0 40px;
  }

  .bookGold {
    .content {
      width: 90%;
    }

    .image {
      object-fit: cover;
    }
  }

  .digitalWear {
    margin: 50px 0;
    background-size: cover;
    .digital {
      flex-wrap: wrap;
    }

    .content {
      width: 100%;
      margin-bottom: 50px;
      text-align: center;
    }

    .digitalImg {
      width: 100%;
    }
  }

  .offSection {
    margin-bottom: 50px;

    .image {
      object-fit: cover;
      object-position: left;
    }

    .content {
      width: 100%;
      left: 20px;
      font-size: 12px;
    }
  }

  .clientSay {
    padding: 50px 0 0;
  }

  .hotCategory {
    padding-bottom: 50px;
  }

  .miSection {
    margin-top: 50px;
    flex-wrap: wrap;

    .leftImage {
      width: 100%;
      height: auto;
    }

    .rightContent {
      width: 100%;
      padding: 40px;
    }
  }

  .featured {
    margin-top: 50px;
    padding: 50px 0;

    .contents {
      flex-wrap: wrap;
    }

    .leftSection {
      width: 100%;
    }

    .rightSection {
      width: 100%;
    }
  }

  .collectionSec {
    padding-top: 50px;
    flex-wrap: wrap;

    .box {
      width: 100%;
      padding: 20px 20px 20px;
    }

    .head {
      width: 100%;
      margin-bottom: 30px;

      h2 {
        font-size: 28px;
      }
    }
  }

  .special {
    margin-top: 50px;
  }

  .hotCategory .name {
    font-size: 25px;
  }

  .blogSec {
    padding-top: 50px;

    .blogSection {
      flex-wrap: wrap;
      .blogImage {
        aspect-ratio: 1.2;
        object-fit: cover;
      }
      .image {
        padding-bottom: 10px;
      }
      .text {
        font-size: 12px;
      }
      .blogs {
        width: 50%;
        padding-bottom: 20px;
      }
      .featuredIcon {
        width: 50px;
        top: 10px;
      }
    }
  }

  .WhatsGoing {
    padding-top: 50px;

    .head {
      font-size: 20px;
      padding-bottom: 20px;
    }

    .social {
      flex-direction: column;
      align-items: center;
      .socialMedia {
        width: 70%;
        padding-bottom: 30px;
        &:nth-child(2),
        &:nth-child(3) {
          width: 100%;
        }
        iframe {
          height: 312px;
        }
      }
    }
  }

  .connectSection {
    .box {
      padding: 17px 8px 10px;
    }
  }
}

h1 {
  font-size: 44px;
}

@media screen and (max-width: 992px) {
  h1 {
    font-size: 30px;
  }
  .specification {

    .box {
      padding: 10px;
      width: 100%;  
      img {
        width: 20px;
      }
    }
    .text {
      font-size: 12px;
    }
  }
}

@media screen and (max-width: 768px) {
  .testimonialPopup.show {
    display: flex;
    flex-direction: column;
    width: 100%;

    .leftSide {
      display: none;
    }

    .check {
      margin-bottom: 10px;
    }

    .rightSide {
      width: 100%;
      padding: 20px;
    }

    .box {
      max-width: 100%;
      width: 80%;
    }
  }
  .specification {
    display: none;
  }

}

@media screen and (max-width: 480px) {
  .perfectBride .leftSection .name {
    font-size: 12px;
  }
  .WhatsGoing .social .socialMedia {
    width: 100%;
  }
  .connectSection .connect {
    flex-wrap: wrap;
  }
  .connectSection .box {
    margin: 5px;
    width: 100%;
  }

  h1 {
    font-size: 25px;
  }
}
