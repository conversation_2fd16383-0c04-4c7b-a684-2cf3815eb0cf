<!-- Optimized Video Player Component -->
<app-video-player
  [bannerData]="processedBanners"
  [imageBase]="imageBase"
  [autoSlideEnabled]="videoPlayerAutoSlide"
  [slideInterval]="videoPlayerSlideInterval"
  (bannerClick)="onBannerClick($event)"
  loading="eager"
>
</app-video-player>

<!-- Specification Section -->
<section class="specification" aria-label="Features">
  <div class="container">
    <div class="contents">
      <swiper [config]="specificationSlider" loading="lazy">
        <ng-template swiperSlide>
          <div class="box">
            <img
              src="assets/images/leading-brands.svg"
              alt="Leading Brands Confidence"
              loading="lazy"
              width="40"
              height="40"
            />
            <div class="text">Leading Brands Confidence</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img
              src="assets/images/certified.svg"
              alt="Certified Diamonds"
              loading="lazy"
              width="40"
              height="40"
            />
            <div class="text">Certified Diamonds</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img
              src="assets/images/easy-return.svg"
              alt="Easy Returns"
              loading="lazy"
              width="40"
              height="40"
            />
            <div class="text">Easy Returns</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img
              src="assets/images/free-shipping.svg"
              alt="Free Insured Shipping"
              loading="lazy"
              width="40"
              height="40"
            />
            <div class="text">Free Insured Shipping</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img
              src="assets/images/lifetime-exchange.svg"
              alt="Life Time Exchange"
              loading="lazy"
              width="40"
              height="40"
            />
            <div class="text">Life Time Exchange</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img
              src="assets/images/guaranteed.svg"
              alt="Guaranteed Buy back"
              loading="lazy"
              width="40"
              height="40"
            />
            <div class="text">Guaranteed Buy back</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img
              src="assets/images/customisation.svg"
              alt="Customisation"
              loading="lazy"
              width="40"
              height="40"
            />
            <div class="text">Customisation</div>
          </div>
        </ng-template>
        <ng-template swiperSlide>
          <div class="box">
            <img
              src="assets/images/bis.svg"
              alt="BIS Hallmarked 100% HUID ornaments"
              loading="lazy"
              width="40"
              height="40"
            />
            <div class="text">BIS Hallmarked 100% HUID ornaments</div>
          </div>
        </ng-template>
      </swiper>
    </div>
  </div>
</section>

<!-- Top Trending Collections -->
<section class="container" aria-label="Top Trending Collections">
  <div class="topTrending" *ngIf="homeData?.trendingCollectionData">
    <h2>Top Trending Collections</h2>
    <div class="imageSection">
      <a
        (click)="
          bannerRedirections(
            homeData.trendingCollectionData?.redirection_type,
            homeData.trendingCollectionData?.top_trending_first_slug,
            ''
          )
        "
        class="image first"
        rel="preload"
      >
        <img
          src="assets/images/wedding-collection.png"
          alt="Wedding Collection"
          loading="lazy"
          width="300"
          height="200"
        />
      </a>
      <a
        (click)="
          bannerRedirections(
            homeData.trendingCollectionData?.redirection_type,
            homeData.trendingCollectionData?.top_trending_second_slug,
            ''
          )
        "
        class="image second"
        rel="preload"
      >
        <img
          src="assets/images/light-weight-collection.png"
          alt="Light Weight Collection"
          loading="lazy"
          width="300"
          height="200"
        />
      </a>
    </div>
  </div>
</section>

<!-- For Men/Women Section -->
<section
  class="forMenWomen"
  aria-label="Collections for Men and Women"
  *ngIf="homeData?.menWomenSection"
>
  <a
    (click)="
      bannerRedirections(
        homeData.menWomenSection?.redirection_type,
        homeData.menWomenSection?.for_men_slug,
        '',
        homeData.menWomenSection?.menImageName,
        homeData.menWomenSection?.menImageCategory
      )
    "
    class="for"
    rel="preload"
  >
    <img
      class="image"
      [src]="imageBase + '/' + homeData.menWomenSection?.for_men_image"
      [alt]="homeData.menWomenSection?.for_men_title"
      loading="lazy"
      width="400"
      height="300"
    />
    <h4>
      <span>{{ homeData.menWomenSection?.for_men_title }}</span>
      <img
        src="assets/images/arrow.svg"
        alt=""
        loading="lazy"
        width="16"
        height="16"
      />
    </h4>
  </a>
  <a
    (click)="
      bannerRedirections(
        homeData.menWomenSection?.redirection_type,
        homeData.menWomenSection?.for_women_slug,
        '',
        homeData.menWomenSection?.womenImageName,
        homeData.menWomenSection?.womenImageCategory
      )
    "
    class="for"
    rel="preload"
  >
    <img
      class="image"
      [src]="imageBase + '/' + homeData.menWomenSection?.for_women_image"
      [alt]="homeData.menWomenSection?.for_women_title"
      loading="lazy"
      width="400"
      height="300"
    />
    <h4>
      <span>{{ homeData.menWomenSection?.for_women_title }}</span>
      <img
        src="assets/images/arrow.svg"
        alt=""
        loading="lazy"
        width="16"
        height="16"
      />
    </h4>
  </a>
</section>

<!-- Trending Section -->
<section
  class="trendingSection"
  style="background-image: url('assets/images/top-trending-bg.png')"
  aria-label="Trending Products"
  *ngIf="trendingListData?.products?.length"
>
  <div class="container">
    <h2>{{ trendingListData?.title }}</h2>
    <div class="trendings">
      <swiper [config]="trendSlide">
        <ng-template
          swiperSlide
          *ngFor="
            let product of trendingListData?.products;
            trackBy: trackByProductId
          "
        >
          <div class="slideItems">
            <button
              class="wishlist"
              [class.active]="product?.isfavorite"
              (click)="addToWishlist(product?.id, product?.isfavorite)"
              [attr.aria-label]="
                product?.isfavorite ? 'Remove from wishlist' : 'Add to wishlist'
              "
            >
              <img
                class="fav"
                src="assets/images/favorite.png"
                alt=""
                loading="lazy"
                width="20"
                height="20"
              />
              <img
                class="fav-gold"
                src="assets/images/favorite-gold.png"
                alt=""
                loading="lazy"
                width="20"
                height="20"
              />
            </button>
            <a class="productImage" [routerLink]="['/detail', product?.slug]">
              <img
                [src]="imageBase + '/' + product?.image"
                [alt]="product?.productName"
                class="img-fluid"
                loading="lazy"
                width="200"
                height="200"
              />
            </a>
            <a [routerLink]="['/detail', product?.slug]" class="productName">{{
              product?.productName
            }}</a>
            <a [routerLink]="['/detail', product?.slug]" class="price">
              <div class="offer"><span>₹</span> {{ product?.price }}</div>
              <div class="old" *ngIf="product?.actualPrice">
                <span>₹</span>{{ product?.actualPrice }}
              </div>
            </a>
          </div>
        </ng-template>
      </swiper>
    </div>
    <div class="slide">
      <button
        class="swiper-button-prev"
        id="trend-Prev"
        aria-label="Previous trending item"
      ></button>
      <button
        class="swiper-button-next"
        id="trend-Next"
        aria-label="Next trending item"
      ></button>
    </div>
  </div>
</section>

<!-- Brands Section -->
<section
  class="container"
  aria-label="Brands We Love"
  *ngIf="homeData?.brands?.items?.length"
>
  <div class="brandsLove">
    <div class="title">
      <h2>{{ homeData.brands?.title }}</h2>
      <p>{{ homeData.brands?.description }}</p>
    </div>
    <swiper [config]="brandSlide">
      <ng-template
        swiperSlide
        *ngFor="let item of homeData.brands?.items; trackBy: trackByProductId"
      >
        <a
          (click)="
            bannerRedirections(
              item?.redirection_type,
              item?.slug,
              item?.title,
              item?.imageName,
              item?.imageCategory
            )
          "
          class="slideItems"
          rel="preload"
        >
          <img
            [src]="imageBase + '/' + item?.image"
            [alt]="item?.title || 'Brand'"
            class="img-fluid"
            loading="lazy"
            width="150"
            height="100"
          />
        </a>
      </ng-template>
    </swiper>
    <div class="slide">
      <button
        class="swiper-button-prev"
        id="category-Prev"
        aria-label="Previous brand"
      ></button>
      <button
        class="swiper-button-next"
        id="category-Next"
        aria-label="Next brand"
      ></button>
    </div>
  </div>
</section>

<!-- Advance Booking Banner -->
<a
  (click)="
    bannerRedirections(
      homeData?.advance_booking_banner?.redirection_type,
      homeData?.advance_booking_banner?.slug,
      '',
      homeData?.bannerName,
      homeData?.advance_booking_banner?.bannerCategory
    )
  "
  class="bookGold"
  *ngIf="homeData?.advance_booking_banner"
  rel="preload"
>
  <img
    class="image mobile"
    [src]="imageBase + '/' + homeData?.advance_booking_banner?.mobile_image"
    alt="Advance booking mobile banner"
    loading="lazy"
    width="375"
    height="200"
  />
  <img
    class="image desktop"
    [src]="imageBase + '/' + homeData?.advance_booking_banner?.image"
    alt="Advance booking desktop banner"
    loading="lazy"
    width="1200"
    height="300"
  />
  <div class="content">
    <h2>{{ homeData?.advance_booking_banner?.title }}</h2>
    <span class="book">{{
      homeData?.advance_booking_banner?.button_text
    }}</span>
  </div>
</a>

<!-- Digital Wear Section -->
<section
  class="digitalWear"
  aria-label="Digital Wear Experience"
  *ngIf="homeData?.digital_wear_banner"
>
  <div class="container">
    <div class="digital">
      <div class="content">
        <h2>{{ homeData?.digital_wear_banner?.title }}</h2>
        <p>{{ homeData?.digital_wear_banner?.description }}</p>
        <button class="digitalBtn">Start Digital Wear</button>
      </div>
      <img
        class="digitalImg"
        [src]="imageBase + '/' + homeData?.digital_wear_banner?.image"
        [alt]="homeData?.digital_wear_banner?.title"
        loading="lazy"
        width="400"
        height="300"
      />
    </div>
  </div>
</section>

<!-- Offer Section -->
<section
  class="container"
  aria-label="Special Offers"
  *ngIf="homeData?.offerData"
>
  <a
    (click)="
      bannerRedirections(
        homeData.offerData?.redirection_type,
        homeData.offerData?.slug,
        '',
        homeData.offerData?.bannerName,
        homeData.offerData?.bannerCategory
      )
    "
    class="offSection"
    rel="preload"
  >
    <img
      class="image"
      [src]="imageBase + '/' + homeData.offerData?.image"
      [alt]="homeData.offerData?.bannerName || 'Special offer'"
      loading="lazy"
      width="800"
      height="400"
    />
  </a>
</section>

<!-- Hot Categories -->
<section
  class="container"
  aria-label="Hot Categories"
  *ngIf="homeData?.categories?.items?.length"
>
  <div class="title">
    <h2>{{ homeData.categories?.title }}</h2>
  </div>
  <div class="hotCategory">
    <swiper [config]="hotCategorySlide">
      <ng-template
        swiperSlide
        *ngFor="
          let item of homeData.categories?.items;
          trackBy: trackByProductId
        "
      >
        <a
          (click)="
            bannerRedirections(item?.redirection_type, item?.slug, item?.title)
          "
          class="slideItems"
          rel="preload"
        >
          <img
            [src]="imageBase + '/' + item?.image"
            [alt]="item?.title"
            class="img-fluid"
            loading="lazy"
            width="200"
            height="200"
          />
          <div class="name">{{ item?.title }}</div>
        </a>
      </ng-template>
    </swiper>
    <div class="slide">
      <button
        class="swiper-button-prev"
        id="category-Prev"
        aria-label="Previous category"
      ></button>
      <button
        class="swiper-button-next"
        id="category-Next"
        aria-label="Next category"
      ></button>
    </div>
  </div>
</section>

<!-- Perfect Bride Section -->
<section
  class="container"
  aria-label="Perfect Bride Collection"
  *ngIf="homeData?.perfectBride?.items?.length"
>
  <div class="title">
    <h2>{{ homeData.perfectBride?.title }}</h2>
    <p>{{ homeData.perfectBride?.description }}</p>
  </div>
  <div class="perfectBride">
    <div class="leftSection">
      <div class="top">
        <ng-container
          *ngFor="
            let item of homeData.perfectBride?.items;
            trackBy: trackByProductId
          "
        >
          <a
            (click)="
              bannerRedirections(
                item?.redirection_type,
                item?.slug,
                item?.title
              )
            "
            class="image"
            *ngIf="item?.sequence >= 1 && item?.sequence <= 3"
            rel="preload"
          >
            <img
              [src]="imageBase + '/' + item?.image"
              [alt]="item?.title"
              loading="lazy"
              width="200"
              height="200"
            />
            <span class="name">{{ item.title }}</span>
          </a>
        </ng-container>
      </div>
      <div class="bottom">
        <ng-container
          *ngFor="
            let item of homeData.perfectBride?.items;
            trackBy: trackByProductId
          "
        >
          <a
            (click)="
              bannerRedirections(
                item?.redirection_type,
                item?.slug,
                item?.title
              )
            "
            class="image"
            [class]="item?.sequence === 5 ? 'image_2' : 'image_1'"
            *ngIf="item?.sequence === 5 || item?.sequence === 6"
            rel="preload"
          >
            <img
              [src]="imageBase + '/' + item?.image"
              [alt]="item?.title"
              loading="lazy"
              width="200"
              height="200"
            />
            <span class="name">{{ item.title }}</span>
          </a>
        </ng-container>
      </div>
    </div>
    <div class="rightSection">
      <ng-container
        *ngFor="
          let item of homeData.perfectBride?.items;
          trackBy: trackByProductId
        "
      >
        <a
          (click)="
            bannerRedirections(item?.redirection_type, item?.slug, item?.title)
          "
          class="image"
          *ngIf="item?.sequence === 4"
          rel="preload"
        >
          <img
            [src]="imageBase + '/' + item?.image"
            [alt]="item?.title"
            loading="lazy"
            width="400"
            height="500"
          />
        </a>
      </ng-container>
    </div>
  </div>
</section>

<!-- Testimonials Section -->
<section
  class="container"
  aria-label="Customer Testimonials"
  *ngIf="homeData?.testimonials?.items?.length"
>
  <div class="clientSay">
    <div class="title">
      <h2>{{ homeData.testimonials?.title }}</h2>
    </div>
    <swiper [config]="clientSlide">
      <ng-template
        swiperSlide
        *ngFor="
          let item of homeData.testimonials?.items;
          trackBy: trackByProductId
        "
      >
        <div class="slideItems">
          <p class="description">
            <img
              src="assets/images/quote-1.svg"
              alt=""
              class="quote-start"
              loading="lazy"
              width="20"
              height="20"
            />
            {{ item?.description }}
            <img
              src="assets/images/quote-1.svg"
              alt=""
              class="quote-end"
              loading="lazy"
              width="20"
              height="20"
            />
          </p>
          <div class="desig">
            <div class="image">
              <img
                [src]="item?.image"
                [alt]="item?.name"
                loading="lazy"
                width="60"
                height="60"
              />
            </div>
            <div class="item">
              <div class="name">{{ item?.name }}</div>
              <div class="date">{{ item?.date }}</div>
            </div>
          </div>
        </div>
      </ng-template>
    </swiper>
    <div class="slide">
      <button
        class="swiper-button-prev"
        id="client-Prev"
        aria-label="Previous testimonial"
      ></button>
      <button
        class="swiper-button-next"
        id="client-Next"
        aria-label="Next testimonial"
      ></button>
    </div>
    <div class="testimonialBtn">
      <button class="primary-button" (click)="popUp()">
        Submit your Testimonial
      </button>
    </div>
  </div>
</section>

<!-- MI Gold Section -->
<section
  class="container"
  aria-label="MI Gold Scheme"
  *ngIf="homeData?.miGoldSection"
>
  <a
    (click)="
      bannerClicked(
        homeData.miGoldSection?.bannerName,
        homeData.miGoldSection?.bannerCategory,
        '/gold-schemes'
      )
    "
    class="miSection"
    rel="preload"
  >
    <div class="leftImage">
      <img
        [src]="imageBase + '/' + homeData.miGoldSection?.image"
        [alt]="homeData.miGoldSection?.title"
        loading="lazy"
        width="400"
        height="300"
      />
    </div>
    <div class="rightContent">
      <h2>{{ homeData.miGoldSection?.title }}</h2>
      <p>{{ homeData.miGoldSection?.description }}</p>
      <span class="join">{{ homeData.miGoldSection?.button_text }}</span>
    </div>
  </a>
</section>

<!-- Featured Section -->
<section
  class="featured"
  aria-label="Featured Products"
  *ngIf="featuredListData?.products?.length"
>
  <div class="container">
    <div class="contents">
      <div class="leftSection">
        <h2>{{ featuredListData?.title }}</h2>
        <p>{{ featuredListData?.description }}</p>
        <div class="slide">
          <button
            class="swiper-button-prev"
            id="category-Prev"
            aria-label="Previous featured item"
          ></button>
          <button
            class="swiper-button-next"
            id="category-Next"
            aria-label="Next featured item"
          ></button>
        </div>
      </div>
      <div class="rightSection">
        <div class="trendings">
          <swiper [config]="featuredSlide">
            <ng-template
              swiperSlide
              *ngFor="
                let product of featuredListData?.products;
                trackBy: trackByProductId
              "
            >
              <div class="slideItems">
                <button
                  class="wishlist"
                  [class.active]="product?.isfavorite"
                  (click)="addToWishlist(product?.id, product?.isfavorite)"
                  [attr.aria-label]="
                    product?.isfavorite
                      ? 'Remove from wishlist'
                      : 'Add to wishlist'
                  "
                >
                  <img
                    class="fav"
                    src="assets/images/favorite.png"
                    alt=""
                    loading="lazy"
                    width="20"
                    height="20"
                  />
                  <img
                    class="fav-gold"
                    src="assets/images/favorite-gold.png"
                    alt=""
                    loading="lazy"
                    width="20"
                    height="20"
                  />
                </button>
                <a
                  class="productImage"
                  [routerLink]="['/detail', product?.slug]"
                >
                  <img
                    [src]="imageBase + '/' + product?.image"
                    [alt]="product?.productName"
                    class="img-fluid"
                    loading="lazy"
                    width="200"
                    height="200"
                  />
                </a>
                <a
                  [routerLink]="['/detail', product?.slug]"
                  class="productName"
                  >{{ product?.productName }}</a
                >
                <a [routerLink]="['/detail', product?.slug]" class="price">
                  <div class="offer"><span>₹</span> {{ product?.price }}</div>
                  <div class="old" *ngIf="product?.actualPrice">
                    <span>₹</span> {{ product?.actualPrice }}
                  </div>
                </a>
              </div>
            </ng-template>
          </swiper>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Collection Section -->
<section
  class="collectionSec"
  aria-label="Collections"
  *ngIf="homeData?.traditional_collection || homeData?.everyday_diamonds"
>
  <a
    (click)="
      bannerRedirections(
        homeData?.traditional_collection?.traditional_redirection_type,
        homeData?.traditional_collection?.slug,
        '',
        homeData?.traditional_collection?.imageName,
        homeData?.traditional_collection?.imageCategory
      )
    "
    class="box"
    *ngIf="homeData?.traditional_collection"
    rel="preload"
  >
    <div class="head">
      <h2>{{ homeData?.traditional_collection?.traditional_title }}</h2>
      <p>{{ homeData?.traditional_collection?.traditional_subtitle }}</p>
    </div>
    <img
      [src]="
        imageBase + '/' + homeData?.traditional_collection?.traditional_image
      "
      [alt]="homeData?.traditional_collection?.traditional_title"
      loading="lazy"
      width="400"
      height="300"
    />
  </a>
  <a
    (click)="
      bannerRedirections(
        homeData?.everyday_diamonds?.everyday_redirection_type,
        homeData?.everyday_diamonds?.slug,
        '',
        homeData?.everyday_diamonds?.imageName,
        homeData?.everyday_diamonds?.imageCategory
      )
    "
    class="box"
    *ngIf="homeData?.everyday_diamonds"
    rel="preload"
  >
    <div class="head">
      <h2>{{ homeData?.everyday_diamonds?.everyday_title }}</h2>
      <p>{{ homeData?.everyday_diamonds?.everyday_subtitle }}</p>
    </div>
    <img
      [src]="imageBase + '/' + homeData?.everyday_diamonds?.everyday_image"
      [alt]="homeData?.everyday_diamonds?.everyday_title"
      loading="lazy"
      width="400"
      height="300"
    />
  </a>
</section>

<!-- Special Video Banner -->
<a
  class="special"
  (click)="
    bannerRedirections(
      homeData?.video_banner?.redirection_type,
      homeData?.video_banner?.slug,
      homeData?.video_banner?.title
    )
  "
  *ngIf="homeData?.video_banner"
  rel="preload"
>
  <video
    width="100%"
    height="100%"
    playsinline
    preload="metadata"
    [muted]="true"
    [autoplay]="true"
    [loop]="true"
    class="ng-star-inserted"
    [src]="imageBase + '/' + homeData?.video_banner?.video"
    [attr.aria-label]="homeData?.video_banner?.title"
  ></video>
  <div class="content">
    <h2>{{ homeData?.video_banner?.title }}</h2>
    <span class="explore">{{ homeData?.video_banner?.button_text }}</span>
  </div>
</a>

<!-- Blog Section -->
<section
  class="blogSec"
  aria-label="Latest Blogs"
  *ngIf="homeData?.blogs?.length"
>
  <div class="container">
    <div class="title">
      <h2>Blogs</h2>
    </div>
    <div class="blogSection">
      <a
        [routerLink]="['/blog-details', item?.slug]"
        class="blogs"
        *ngFor="let item of homeData.blogs; trackBy: trackByProductId"
        rel="preload"
      >
        <div class="image">
          <img
            class="featuredIcon"
            src="assets/images/featured.svg"
            alt="Featured"
            *ngIf="item?.is_featured == 1"
            loading="lazy"
            width="30"
            height="30"
          />
          <img
            class="blogImage"
            [src]="imageBase + '/' + item?.image"
            [alt]="item?.title"
            loading="lazy"
            width="300"
            height="200"
          />
        </div>
        <div class="content">
          <div class="text">{{ item?.title }}</div>
        </div>
      </a>
    </div>
    <span class="view"><a routerLink="/blog">View All</a></span>
  </div>
</section>

<!-- Connect Section -->
<section class="connectSection" aria-label="Connect with Us">
  <div class="container">
    <div class="title">
      <h2>Connect with us</h2>
    </div>
    <div class="connect">
      <button (click)="openWhatsapp()" class="box">
        <img
          src="assets/images/whatsapp-connect.svg"
          alt="WhatsApp"
          loading="lazy"
          width="40"
          height="40"
        />
        <h6>Connect on whatsapp</h6>
      </button>
      <a routerLink="/book-appointment" class="box">
        <img
          src="assets/images/book.svg"
          alt="Book Appointment"
          loading="lazy"
          width="40"
          height="40"
        />
        <h6>Book an Appointment</h6>
      </a>
      <a routerLink="/virtual-shopping" class="box">
        <img
          src="assets/images/videocall.svg"
          alt="Video Call"
          loading="lazy"
          width="40"
          height="40"
        />
        <h6>Start a video call</h6>
      </a>
    </div>
  </div>
</section>

<!-- Testimonial Popup -->
<div
  class="testimonialPopup"
  [class.show]="testimonialModal"
  [class.hide]="!testimonialModal"
  *ngIf="testimonialModal"
>
  <div class="box">
    <button
      class="close"
      (click)="popUpClose()"
      aria-label="Close testimonial form"
    >
      <img
        src="assets/images/close-white.svg"
        alt=""
        loading="lazy"
        width="20"
        height="20"
      />
    </button>
    <div class="content">
      <form [formGroup]="form" *ngIf="form">
        <div class="field">
          <label class="name" for="testimonial-name">Name</label>
          <input type="text" id="testimonial-name" formControlName="name" />
          <div
            style="color: red"
            *ngIf="
              testimonialControls['name']?.errors &&
              (!isSubmitted || testimonialControls['name']?.touched)
            "
          >
            Name is required
          </div>
        </div>
        <div class="field">
          <label class="name" for="testimonial-description">Description</label>
          <textarea
            id="testimonial-description"
            cols="30"
            rows="3"
            formControlName="description"
          ></textarea>
          <div
            style="color: red"
            *ngIf="
              testimonialControls['description']?.errors &&
              (!isSubmitted || testimonialControls['description']?.touched)
            "
          >
            Description is required
          </div>
        </div>
        <div class="field">
          <label class="name" for="testimonial-image"
            >Upload image (png or jpg)*</label
          >
          <span class="uploading">
            <input
              type="file"
              id="testimonial-image"
              (change)="uploadImage($event.target)"
              accept="image/*"
            />
            <div>
              <img
                [src]="imageURL"
                alt="Uploaded testimonial image"
                *ngIf="imageURL"
                loading="lazy"
              />
            </div>
          </span>
          <div style="color: red" *ngIf="!isImageUploaded">
            Image is required
          </div>
        </div>
      </form>
      <div class="submitBtn">
        <button class="primary-button golden" (click)="submitTestimonial()">
          Submit
        </button>
      </div>
    </div>
  </div>
</div>
