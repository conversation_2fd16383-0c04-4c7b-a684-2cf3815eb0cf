import { environment } from './../../environments/environment.prod';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Response } from '../shared/models/common-model';
import { contact, otp, verifyOtp } from '../shared/models/contact.model';
import { brand } from '../shared/models/brands.model';
import { giftVoucher } from '../shared/models/gift-voucher.model';
import { advanceBooking } from '../shared/models/advance-booking.model';
import { goldScheme, requestOTP } from '../shared/models/gold-scheme.model';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  constructor(private http: HttpClient) {}

  getData(url: any) {
    return this.http.get(environment.apiUrl + url);
  }

  postData(url: any, data: any) {
    return this.http.post(environment.apiUrl + url, data);
  }

  postTestimonial(url: any, data: any) {
    return this.http.post(environment.apiUrl + url, data);
  }

  getContactDetails(url: string) {
    return this.http.get<Response<contact[]>>(environment.apiUrl + url);
  }

  getBrandDetails(url: string, id: number) {
    return this.http.post<Response<brand>>(environment.apiUrl + url, {
      brand: id,
    });
  }

  getGiftCardDetails(url: string) {
    return this.http.get<Response<giftVoucher>>(environment.apiUrl + url);
  }

  getAdvanceBookingData(url: string) {
    return this.http.get<Response<advanceBooking>>(environment.apiUrl + url);
  }

  getGoldSchemeData(url: string) {
    return this.http.get<Response<goldScheme>>(environment.apiUrl + url);
  }

  verfifyMobileNumber(url: string, data: any) {
    return this.http.post<Response<otp>>(environment.apiUrl + url, data);
  }

  verifyOTP(url: string, data: any) {
    return this.http.post<Response<verifyOtp>>(environment.apiUrl + url, data);
  }

  requestOTP(url:string,data:any){
    return this.http.post<Response<requestOTP>>(environment.apiUrl+url,data)
  }
}
