// ::ng-deep{
//   .ar #tryonButton{
//    display: block;
//     bottom: 40px;
//     left: 50%;
//     transform: translateX(-50%);
//     position: absolute;

//   }
// }
.detailSection {
  .section {
    display: flex;
    padding-bottom: 80px;

    .product {
      width: 402px;
    }

    .productImage {
      width: 100%;
      height: 438px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      .ar {
        // position: absolute;
        top: 20px;
        width: 100%;
        left: 0;
        cursor: pointer;
        z-index: 2;
        height: 100%;

        .info {
          opacity: 0;
          position: absolute;
          width: max-content;
          font-size: 12px;
          color: #fff;
          // background: #DAB572;
          top: 2px;
          left: 50px;
          padding: 7px 10px;

          &:before {
            content: "";
            position: absolute;
            // border: 22px solid #DAB572;
            left: -23px;
            border-top: 12px solid transparent;
            border-left: 12px solid transparent;
            border-bottom: 12px solid transparent;
            top: 5px;
            z-index: -1;
          }
        }

        &:hover .info {
          transition: all linear 0.5s;
          opacity: 1;
        }
      }

      .wishlist {
        z-index: 2;
        position: absolute;
        right: 20px;
        display: flex;
        top: 20px;
        cursor: pointer;

        .fav-gold {
          display: none;
        }

        &.active .fav-gold {
          display: block;
        }

        &.active .fav {
          display: none;
        }
      }

      .share {
        position: absolute;
        right: 20px;
        top: 60px;

        .socials {
          display: none;
        }

        &:hover {
          .socials {
            display: flex;
            position: absolute;
            flex-direction: column;
            position: absolute;
            top: 30px;

            a {
              padding: 5px;
            }
          }
        }
      }
    }

    .productDetail {
      padding-left: 70px;
      width: calc(100% - 402px);

      .products .rate {
        font-size: 12px;
      }

      .productsSimilar.cost .rate {
        font-size: 18px;
      }

      .productName {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
      }

      .productCode {
        font-size: 16px;
        margin-bottom: 20px;
      }

      .details {
        font-size: 16px;
        margin-bottom: 15px;

        span {
          color: #dab572;
          padding-left: 5px;
          cursor: pointer;
          font-weight: 500;
        }

        .readMore {
          color: #dab572;
          text-decoration: underline;
        }
      }

      .weight {
        font-weight: 500;
        text-transform: capitalize;
        font-size: 16px;
        margin-bottom: 40px;
        font-weight: 600;

        span {
          padding: 0 20px;
        }
      }

      .price {
        display: flex;

        .offer {
          padding-right: 20px;
        }

        .rate {
          font-size: 24px;
          font-weight: 600;
        }

        .old {
          text-decoration-line: line-through;
          color: #505050;
          padding-top: 5px;
          font-size: 20px;
        }
      }
      .incl {
        font-size: 12px;
        margin-bottom: 30px;
      }

      .deliveryOption {
        .dOption {
          padding-bottom: 10px;
        }

        .pincode {
          padding-right: 20px;
          min-width: 330px;
          form {
            display: flex;
            align-items: center;
            position: relative;
            .text-danger {
              position: absolute;
              bottom: -17px;
              font-size: 13px;
              width: 100%;
              left: 0;
            }
          }
        }

        input[type="tel"] {
          border: 1px solid #d5dbde;
          margin-bottom: 0;
        }

        .check {
          background: #1d1d1d;
          border: 1px solid #1d1d1d;
          color: #fff;
          cursor: pointer;
          padding: 9px 20px;
        }

        .out {
          font-size: 12px;

          a {
            color: #dab572;
          }
        }
      }

      .addtoCart {
        width: 235px;
        margin-top: 40px;
      }

      .priceBreakUp {
        margin-top: 30px;
        border-top: 1px solid #d5dbde;
        cursor: pointer;
        border-bottom: 1px solid #d5dbde;

        .head {
          display: flex;
          padding-top: 16px;
          padding-bottom: 15px;
          justify-content: space-between;
        }

        .priceBreaks {
          padding-top: 8px;
          height: 0;
          overflow: hidden;
          opacity: 0;
          transition: ease 0.9s;
        }

        .priceBreaks.active {
          height: 100%;
          overflow: visible;
          opacity: 1;
          transition: ease 0.6s;
        }

        .pricebreak {
          font-weight: 600;
          padding-left: 15px;
          font-size: 17px;
        }

        .plus {
          border: 1px solid #1d1d1d;
          padding: 0 6px;
          color: #1d1d1d;
        }
        .break {
          border: 1px solid #e1e1e1;
          text-align: center;
          padding: 15px;
          height: 100%;
        }

        .total {
          text-align: right;
          padding: 20px 0;
        }
      }

      .customDesign {
        margin: 30px 0 70px;
        display: flex;
        align-items: center;
        background: linear-gradient(
          180deg,
          rgba(255, 248, 233, 0) 0%,
          #fff8e9 100%
        );
        padding: 40px 45px 55px 50px;

        .image {
          width: 50%;
          padding-right: 35px;
        }

        .text {
          width: 50%;
          text-align: center;

          .head {
            font-size: 24px;
            padding-bottom: 20px;
            color: #1d1d1d;
            font-weight: 600;
          }

          p {
            padding-bottom: 40px;
            color: #1d1d1d;
          }
        }
      }
    }
  }

  .productData {
    margin-bottom: 80px;

    .heading {
      font-size: 24px;
      padding-bottom: 30px;
      font-weight: 600;
    }

    .box {
      background: #fffbf3;
      padding: 20px;
    }

    .head {
      color: #d0a861;
      font-weight: 600;
      border-bottom: 1px solid #0000001a;
      padding-bottom: 10px;
      margin-bottom: 20px;
      font-size: 16px;
    }

    .fields {
      padding-bottom: 10px;
      font-size: 14px;
      display: flex;

      span:first-child {
        width: 150px;
      }

      span:last-child {
        width: calc(100% - 150px);
      }
    }

    .sections {
      border-bottom: 1px solid #0000001a;
      padding-bottom: 10px;
      margin-bottom: 10px;
    }

    .more {
      color: #d0a861;
      font-weight: 600;
      padding-top: 5px;
      font-size: 16px;
      cursor: pointer;

      .less {
        display: none;
      }

      &.show {
        .more {
          display: none;
        }

        .less {
          display: block;
        }
      }
    }
  }

  .matchingProducts {
    box-shadow: 2px 2px 22px rgb(0 0 0 / 6%);
    padding: 40px;
  }

  .productsSimilar {
    display: flex;
    padding-top: 50px;
    padding-bottom: 40px;

    .products {
      width: 30.33%;
      text-align: center;
      position: relative;

      img {
        max-height: 150px;
        width: 100%;
        object-fit: cover;
        margin-bottom: 15px;
      }
    }

    .products.addon {
      margin-left: 40px;

      &:before {
        content: "+";
        position: absolute;
        left: -30px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 40px;
        color: #1d1d1d;
      }
      &.total {
        &:before {
          content: "=";
        }
      }
    }

    .check {
      position: absolute;
      top: 5px;
      right: 20px;
      //background: #D2AB66;
      color: #fff;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      input {
        padding: 0;
        height: initial;
        width: initial;
        margin-bottom: 0;
        display: none;
        cursor: pointer;
      }

      label {
        position: relative;
        cursor: pointer;
        font-weight: 400;
        font-size: 13px;
        display: flex;
        flex-direction: column;
      }

      label:before {
        content: "";
        -webkit-appearance: none;
        background-color: transparent;
        border: 1px solid #d5dbde;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05),
          inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
        padding: 10px;
        display: inline-block;
        position: absolute;
        vertical-align: middle;
        cursor: pointer;
        margin-right: 10px;
        border-radius: 50%;
      }
      input:checked + label:before {
        background: #d2ab66;
        border: 1px solid #d2ab66;
      }
      input:checked + label:after {
        content: "";
        display: block;
        position: absolute;
        top: 2px;
        left: 8px;
        width: 6px;
        height: 14px;
        border: solid #fff;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
      }
      input + label .type {
        display: none;
      }
      input:checked + label .type {
        display: block;
      }
      textarea {
        border: 1px solid #d5dbde;
        margin-top: 20px;
      }
    }

    &.cost {
      .products {
        text-align: left;
      }

      .rate {
        font-weight: 600;
        padding-top: 2px;
        color: #1d1d1d;
      }

      .item {
        color: #1d1d1d;
      }

      .products.addon {
        padding-left: 8%;
      }
    }
  }

  .dashed {
    position: relative;
    margin-bottom: 30px;

    &:before {
      position: absolute;
      content: "";
      top: 0;
      left: 0;
      width: 100%;
      height: 1px;
      background-image: linear-gradient(
        to right,
        transparent 50%,
        #0000004d 30%
      );
      background-size: 40px 100%;
    }
  }

  .add {
    text-align: right;

    .primary-button {
      max-width: 240px;
    }
  }

  .similarProducts {
    margin-bottom: 80px;

    .products {
      display: flex;
    }

    .slideItems {
      text-align: center;
      background: #ffffff;
      box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.06);
      margin: 20px 8px;
      width: 25%;
      position: relative;

      &:hover {
        box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);

        .productImage {
          img {
            transform: translateY(-10px);
            transition: ease 0.7s;
          }
        }
      }

      .productImage {
        padding: 30px 30px 0;
        overflow: hidden;

        img {
          transition: ease 0.9s;
        }
      }
    }

    .productName {
      font-size: 14px;
      padding: 0 30px 15px;
      line-height: 20px;
    }

    .price {
      font-size: 14px;
      font-weight: 600;
      padding: 0 30px 30px;
      display: flex;
      justify-content: center;

      .offer {
        padding: 0 5px;
      }

      .old {
        text-decoration: line-through;
        padding: 0 5px;
        color: #b3b3b3;
      }
    }

    .ar {
      position: absolute;
      top: 50px;
      width: 23px;
      left: 40px;
      cursor: pointer;
      z-index: 2;
    }

    .wishlist {
      z-index: 2;
      position: absolute;
      right: 40px;
      display: flex;
      top: 50px;
      cursor: pointer;

      .fav-gold {
        display: none;
      }

      &.active .fav-gold {
        display: block;
      }

      &.active .fav {
        display: none;
      }
    }
  }
}
.mobile-product-image {
  width: 100%;
  height: auto;
  cursor: pointer;
}

.fullscreen-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;

  .popup-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .image-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: pan-y pinch-zoom;
    user-select: none;

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      transform: translateZ(0); // Hardware acceleration
      will-change: transform; // Optimization for animations
      touch-action: none; // Prevent browser handling of touch events
    }
  }
  
  .close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    z-index: 1001;
    cursor: pointer;
  }

  .navigation-buttons {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: 20px;

    .nav-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      padding: 10px 15px;
      border-radius: 50%;
      cursor: pointer;

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
  }

  .image-counter {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 14px;
    background: rgba(0, 0, 0, 0.5);
    padding: 5px 10px;
    border-radius: 15px;
  }
}

@media (min-width: 768px) {
  .mobile-product-image {
    display: none;
  }
}

.bookEnquire {
  display: flex;
  margin: 0px -20px 120px;
  justify-content: space-between;

  h3 {
    color: #fff;
    margin-bottom: 20px;
    z-index: 1;
  }

  p {
    margin-bottom: 30px;
    z-index: 1;
  }

  a {
    width: 50%;
    display: flex;
    margin: 0 20px;
    background-repeat: no-repeat;
    background-size: cover;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: #fff;
    text-align: center;
    padding: 50px 7%;
    position: relative;

    &:first-child:before {
      content: "";
      background-color: #25675dcc;
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    &:hover:first-child:before {
      background-color: #25675dd8;
      transition: ease 0.3s;
    }

    &:last-child:before {
      content: "";
      background: rgba(159, 113, 30, 0.67);
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }

    &:hover:last-child:before {
      background: rgba(159, 114, 30, 0.774);
      transition: ease 0.3s;
    }
  }

  .join {
    color: #29635a;
    background-color: #fff;
    padding: 18px 70px;
    z-index: 1;
  }

  .book {
    color: #d2ab66;
    background-color: #fff;
    padding: 18px 70px;
    z-index: 1;
  }
}

.collapsed {
  overflow: hidden;
}

.enquirePopup {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }

  .box {
    width: 90%;
    max-width: 788px;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    position: fixed;
    padding: 30px;
    top: 50%;
    left: 50%;
    z-index: 9;
    background: #fff;
    transform: translate(-50%, -50%);
    border-radius: 12px;

    textarea {
      border: 1px solid #e0e0e0;
    }

    input[type="tel"] {
      border: 1px solid #e0e0e0;
    }

    .buttons {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 15px;

      .cancel {
        border: 1px solid #000;
        padding: 16px 50px;
        margin-right: 20px;
        cursor: pointer;
      }

      .submit {
        background: linear-gradient(90.18deg, #d2ab66 -3.26%, #cb9f52 99.23%);
      }

      input[type="submit"] {
        padding: 16px 50px;
        background: linear-gradient(90.18deg, #d2ab66 -3.26%, #cb9f52 99.23%);
        border-color: #cb9f52;
      }
    }
  }
}

.PopupProduct {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }

  .productImage {
    display: flex;
    margin: 0 auto;
    width: 90%;
    max-width: 1250px;
    position: fixed;
    padding: 30px;
    top: 50%;
    left: 50%;
    z-index: 9;
    transform: translate(-50%, -50%);
    border-radius: 12px;
    background: #fff;
  }

  .productSlide {
    width: 50%;
  }

  lib-ngx-image-zoom {
    width: 50%;
  }

  .productsimg {
    display: flex;
    flex-wrap: wrap;
    width: 30%;

    span {
      width: 100px;
      height: 100px;
      margin-bottom: 20px;

      img {
        height: 100%;
        width: 100%;
        object-fit: cover;
        object-position: top;
      }
    }
  }
}

.add {
  padding-left: 35px;
  padding-top: 2px;
}

h1 {
  display: none;
}

@media screen and (max-width: 1360px) {
  .detailSection {
    .pin {
      display: block !important;
    }

    .pincode {
      width: 100%;
      padding-bottom: 20px;
    }

    .out {
      width: 100%;
    }

    .similarProducts .products {
      flex-wrap: wrap;
      justify-content: space-between;
    }

    .similarProducts .slideItems {
      width: 48%;
      margin: 1%;
    }
  }
}

@media (min-width: 992px) and (max-width: 1260px) {
  .detailSection .section .productDetail .customDesign {
    display: flex;
    flex-direction: column;
    padding: 20px;

    .image {
      width: 40%;
      padding-right: 0;
      padding-bottom: 20px;
    }

    .text {
      width: 100%;
    }
  }
}

@media screen and (max-width: 1200px) {
  .detailSection {
    .section .product {
      width: 100%;
    }

    .section .productDetail {
      width: 100%;
      padding-left: 0;
      padding-top: 30px;
    }

    .section {
      display: flex;
      flex-direction: column;
    }

    .similarProducts {
      margin-bottom: 50px;
    }
  }

  .bookEnquire {
    display: flex;
    flex-wrap: wrap;
    margin: 0px -20px 0;
    a {
      width: 100%;
      margin-bottom: 20px;
    }
  }
}

@media screen and (max-width: 640px) {
  .similarProducts .slideItems {
    width: 10%;
    margin: 1% 0;
  }

  .detailSection {
    .productData {
      margin-bottom: 40px;
    }
    .matchingProducts {
      padding: 20px;
    }
    .similarProducts .slideItems {
      width: 100%;
    }
    .productsSimilar {
      overflow: scroll;
      width: 100%;
      padding: 20px 0;
      .products {
        width: 130px;
        min-width: 130px;
      }
      &::-webkit-scrollbar {
        width: 2px;
      }
    }
    .section {
      padding-bottom: 40px;
      .productDetail .customDesign {
        display: flex;
        flex-wrap: wrap;
        padding: 30px 25px 55px 25px;
        margin: 20px 0 40px;
        .image {
          width: 100%;
          padding-right: 0;
          margin-bottom: 20px;
        }
        .text {
          width: 100%;
        }
      }
    }
  }
}
