// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,

  ccAvenueProduction: 'https://secure.ccavenue.com/transaction/transaction.do',
  ccAvenueStaging: 'https://test.ccavenue.com/transaction/transaction.do',

  // apiUrl : 'https://ttdevassy.s416.previewbay.com/api/',
  // imageBase :'https://ttdevassy.s416.previewbay.com/storage',

  apiUrl: 'https://admin.ttdevassyjewellery.com/api/',
  imageBase: 'https://admin.ttdevassyjewellery.com/storage',

  // apiUrl: 'https://admin.beta.ttdevassyjewellery.com/api/',
  //  imageBase: 'https://admin.beta.ttdevassyjewellery.com/storage',
  
  recaptcha_key: '6LdDujsmAAAAAOgOBdTiV2S-1a8vnNHqpYO8QEa9',

  firebaseConfig: {
    apiKey: 'AIzaSyD1mIJIBYOEVRGxehY6iHh1KWvefY92-wk',
    authDomain: 'tt-devassy-5f806.firebaseapp.com',
    projectId: 'tt-devassy-5f806',
    storageBucket: 'tt-devassy-5f806.appspot.com',
    messagingSenderId: '279050454083',
    appId: '1:279050454083:web:8dba9286dcb1df70fb80f6',
    measurementId: 'G-QK9EL6M05V',
  },

  gtmScript: `
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-CF9YFQF7PJ');
`,

  tagScript:`<script async src="https://www.googletagmanager.com/gtag/js?id=G-CF9YFQF7PJ"></script>`

};

/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
