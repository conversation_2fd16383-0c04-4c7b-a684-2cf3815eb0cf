<div class="myAccount">
  <div class="container">
    <div class="title">
      <h1>My Account</h1>
    </div>
    <div class="contentSection">
      <div class="sideMenu">
        <app-sidebar></app-sidebar>
      </div>
      <div class="contents">
        <div class="head">
          <span>My Orders</span>
          <!-- <div class="filtername selectOption" (click)="showDropdown('filter')">
                        <div class="selectMenu sort">
                            <div class="selected">{{ filter }}</div>
                        </div>
                        <ul class="list" [ngClass]="{ active: activefilter == true }">
                            <li
                            *ngFor="let filter of filtername"
                            (click)="applyFilter(filter, 'filter')"
                            >
                            {{ filter }}
                            </li>
                        </ul>
                    </div> -->
        </div>

        <!-- Empty State -->
        <div
          *ngIf="!isLoading && (!orders || orders.length === 0)"
          class="empty-state"
        >
          <div class="icon">
            <!-- You can use your preferred icon here -->
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="48"
              height="48"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path
                d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"
              ></path>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <path d="M16 10a4 4 0 0 1-8 0"></path>
            </svg>
          </div>
          <h3>No Orders Yet</h3>
        </div>

        <a
          (click)="redirection(item)"
          class="orders"
          *ngFor="let item of orders"
        >
          <span class="image"
            ><img
              src="{{ imageBase }}/{{ item?.thumbnail }}"
              alt="product image"
          /></span>
          <div class="orderDetail">
            <div class="orderId">Order Id : {{ item?.order_id }}</div>
            <div class="item">{{ item?.name }}</div>
            <div class="price">₹{{ item?.price }}</div>
          </div>
          <div class="quantity">Qty : 1</div>
          <div class="status">
            <div
              class="statusIn"
              [ngClass]="{
                green:
                  item?.order_status?.slug == 'placed' ||
                  item?.order_status?.slug == 'delivered' ||
                  item?.order_status?.slug == 'shipped' ||
                  item?.order_status?.slug == 'packed',
                red:
                  item?.order_status?.slug == 'cancelled' ||
                  item?.order_status?.slug == 'returned' ||
                  item?.order_status?.slug == 'return_requested',
                orange: item?.order_status?.slug == 'ordered',
                white: item?.order_status?.slug == 'inprogress'
              }"
            >
              <span class="color"></span>
              {{ item?.order_status?.name }}
            </div>
            <div class="statusDetail">
              {{ item?.order_status?.description }}
            </div>
          </div>
        </a>
        <!-- <a href="/order-detail" class="orders">
                    <span class="image"><img src="\assets\images\Rectangle 23.png" alt=""></span>
                    <div class="orderDetail">
                        <div class="orderId">Order Id : 0544301R40</div>
                        <div class="item">TT GOLD ANTIQUE EARRING 22KT - 0000553055</div>
                        <div class="price">₹47,447.00</div>
                    </div>
                    <div class="quantity">Qty : 1</div>
                    <div class="status">
                        <div class="statusIn red"><span class="color "></span> Cancelled</div>
                        <div class="statusDetail">As per your request,
                            your item has been cancelled</div>
                    </div>
                </a> -->
      </div>
    </div>
  </div>
</div>
