.blogContent {
  display: flex;

  .leftSide {

    width: calc(100% - 330px);
    padding-right: 40px;


    .topSection {
      color: #000;
    }

    .topImage {
      position: relative;
      margin-bottom: 30px;
    }

    .blogImage {
      width: 100%;
      object-position: top;
    }

    .icon {
      position: absolute;
      left: 0;
      top: 30px;
    }

    .subHead {
      font-size: 16px;
      border-bottom: 1px solid #D2AB66;
      padding-bottom: 10px;
      display: inline-block;
      margin-bottom: 30px;
      padding-right: 9px;
      position: relative;
      & + .subHead{
        &::after{
          content: ",";
          position: absolute;
          left: -9px;
        }
        &:last-child::after{
          content: "";
        }

      }
  }

    h6 {
      font-size: 20px;
      text-transform: capitalize;
      margin-bottom: 20px;
    }

    h1 {
      text-transform: capitalize;
      font-size: 44px;

      @media screen and (max-width: 992px) {
        font-size: 30px;
      }

      @media screen and (max-width: 480px) {
        text-transform: capitalize;
        font-size: 25px;
      }
    }

    h2 {
      text-transform: capitalize;
    }

    p {
      margin-bottom: 20px;
    }

    .bottom {
      margin: 25px 0 20px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #787878;

      .tt {
        padding-right: 20px;
      }
    }

    .comment {
      display: flex;
      align-items: center;
    }

    .comment span {
      padding-left: 7px;
    }

    .share {
      border-top: 1px solid rgba(0, 0, 0, 0.14);
      padding: 20px 0;

      span {
        font-size: 16px;
        padding-right: 10px;
        font-weight: 600;
      }

      a {
        padding: 0 13px;
      }
    }



  }

  .rightSide {
    width: 330px;

    .search {
      position: relative;
      width: 100%;

      .searching {
        position: absolute;
        right: 6px;
        top: 5px;
        padding: 10px;
        font-size: 12px;
        color: #1D1D1D;
        cursor: pointer;
      }

      input[type="text"] {
        font-size: 12px;
        padding-left: 0;
        margin-bottom: 40px;
        color: #BBBBBB;
      }
    }

    .recent {
      padding-bottom: 45px;

      h6 {
        font-size: 20px;
        margin-bottom: 20px;
      }

      .post {
        margin-bottom: 15px;
      }

      .postname {
        font-size: 16px;
      }

      .date {
        font-size: 14px;
        color: #787878;
      }
    }

    .category {

      h6 {
        font-size: 20px;
        margin-bottom: 20px;
      }

      .cate {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        margin-bottom: 15px;
      }
    }

  }
}

.relatedPost {
  margin: 80px 0;

  .related {
    display: flex;
    padding-top: 50px;
    margin-right: -15px;
  }

  .box {
    margin-right: 15px;
    color: #000;
  }

  .topImage {
    position: relative;
    max-height: 272px;
    margin-bottom: 20px;
  }

  .icon {
    position: absolute;
    top: 20px;
    left: 0;
  }

  .date {
    color: #1D1D1D66;
    font-size: 12px;
    padding-bottom: 12px;
  }

  .subHead {
    font-family: 'Libre Bodoni';
    font-size: 16px;
    padding-bottom: 8px;
  }

  .name {
    font-size: 14px;
  }

}

.commentSection {
  max-width: 70%;
  margin-bottom: 100px;

  .commentHead {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 40px;
    border-bottom: 1px solid #D9D9D9;

    .count {
      padding-left: 7px;
    }
  }

  .comments {
    display: flex;
    border-bottom: 1px solid #D9D9D9;
    padding: 20px 0;

    .image {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      margin-right: 20px;
      overflow: hidden;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .name {
      font-size: 16px;
      font-weight: 600;
      padding-bottom: 2px;
    }

    p {
      font-size: 14px;
    }

    .date {
      color: #BBBBBB;
      font-size: 14px;
      margin-top: 10px;
    }

  }

}

.leaveReply {
  max-width: 70%;
  margin-bottom: 100px;

  textarea {
    border: 1px solid #E0E0E0;
    margin-top: 50px;
    margin-bottom: 40px;
    color: #ADADAD;
  }

  .button {
    display: flex;
    justify-content: flex-end;
  }

  .primary-button {
    width: 260px;
  }
}


@media screen and (max-width:992px) {

  .blogContent {
    .leftSide {
      width: 100%;
      padding-right: 0;
    }

    .rightSide {
      display: none;
    }

  }

  .relatedPost {
    margin: 20px 0;

    .related {
      padding-top: 20px;
      flex-wrap: wrap;
    }

    .box {
      width: 48.3%;
      margin-bottom: 30px;
    }
  }

  .commentSection {
    max-width: 100%;
    margin-bottom: 40px;

    .commentHead {
      padding-bottom: 25px;
    }

    .comments .image {
      width: 40px;
      height: 40px;
    }

    .comments .details {
      width: calc(100% - 40px);
    }
  }

  .leaveReply {
    max-width: 100%;
    margin-bottom: 40px;

    textarea {
      margin: 20px 0;
    }
  }

}

@media screen and (max-width:640px) {
  .relatedPost {
    .related {
      margin-right: 0;
    }

    .box {
      width: 100%;
      margin-right: 0;
    }
  }

}
