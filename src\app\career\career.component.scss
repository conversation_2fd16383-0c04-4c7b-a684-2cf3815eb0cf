.whyworkWithUs {
  margin: 100px 0;

  .content {
    display: grid;
    grid-template-columns: 1fr 1fr;

    .box {
      padding-top: 40px;
      margin-top: 40px;
      border-top: 1px solid#BDBDBD;

      &:nth-child(odd) {
        padding-right: 20px;
      }

      &:nth-child(even) {
        padding-left: 20px;
      }

      &:nth-child(1),
      &:nth-child(2) {
        padding-top: 0;
        margin-top: 40px;
        border: none;
      }

      h6 {
        font-family: "Gotham";
        font-weight: 500;
      }

      p {
        font-size: 16px;
      }
    }

    .icon {
      margin-bottom: 22px;
      width: 80px;
      aspect-ratio: 1;
    }
  }
}

.careerOpening {
  margin: 100px 0;

  h2 {
    text-align: center;
    margin-bottom: 30px;
  }

  .tabs {
    display: flex;
    justify-content: center;
    gap: 40px;
    margin-bottom: 44px;
    font-weight: 500;

    .tab {
      cursor: pointer;
      color: #121212;
      font-weight: 400;
      text-align: center;

      &.active {
        color: #10347f;
        font-weight: 500;
      }
    }
  }

  .job-cards {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 22px;

    h6 {
      font-family: "Gotham";
      margin-bottom: 15px;
    }

    .job-card {
      background: white;
      padding: 30px 24px;
      border-left: 7px solid #10347f;
      box-shadow: 0px 4px 10px 0px #00000026;
      width: 100%;
    }

    .apply-btn {
      font-size: 16px;
      background-color: #d2ab66;
      color: white;
      border: none;
      padding: 10px 20px;
      cursor: pointer;

      &:hover {
        background-color: #1d1d1d;
      }
    }
  }
}

.careerPopup {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  .box {
    display: flex;
    max-width: 728px;
    width: 95%;
    margin: 0 auto;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 9;
    background: #fff;
    transform: translate(-50%, -50%);
    overflow: hidden;
    border-radius: 16px;
    padding: 40px;

    h5 {
      font-family: "Gotham";
      font-weight: 500;
      margin-bottom: 40px;
    }
  }

  .content {
    width: 100%;
    max-height: 95vh;
    overflow-y: auto;
  }

  .form {
    .grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
    }

    label {
      color: #787878;
      font-weight: 500;
      font-family: "Gotham";
      line-height: normal;
      font-size: 14px;
    }

    input[type="text"],
    input[type="url"],
    input[type="tel"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    select,
    textarea,
    input[type="date"] {
      border: 1px solid #d5dbde;
      padding: 14px 15px 14px 12px;
      margin-bottom: 30px;
    }

    .apply {
      display: flex;
      justify-content: flex-end;
    }

    .apply-btn {
      font-size: 16px;
      background-color: #d2ab66;
      color: white;
      border: none;
      padding: 13px 20px;
      cursor: pointer;

      &:hover {
        background-color: #1d1d1d;
      }
    }
  }

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }

  .close {
    position: absolute;
    right: 40px;
    top: 28px;
    cursor: pointer;
    opacity: unset;
    padding: 10px;
    z-index: 1;

    img {
      filter: invert(100%);
    }
  }

  .upload-container {
    display: flex;
    align-items: center;
    gap: 16px;
    border: 1px dashed #ccc;
    border-radius: 10px;
    padding: 12px 16px;
    background: white;
    transition: border-color 0.3s;
    margin-bottom: 30px;

    &.dragover {
      border-color: #000;
    }

    .upload-icon {
      min-width: 42px;
      width: 42px;
      height: 42px;
      background: #f5f5f5;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .upload {
      display: flex;
      align-items: center;
      gap: 16px;
      width: 72%;
    }

    .upload-label {
      display: flex;
      align-items: center;
      gap: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #000;
      width: 100%;
      max-width: 352px;
      border-bottom: 1px solid #e5e7eb;
      padding-bottom: 10px;
      cursor: pointer;
      margin-bottom: 0;
    }

    .upload-btn {
      background-color: #1d1d1d;
      color: white;
      padding: 16px 20px;
      border: none;
      cursor: pointer;
      font-weight: 500;
      font-size: 16px;
    }

    .upload-info {
      display: flex;
      flex-direction: column;
      font-size: 16px;
      margin-left: auto;
      color: #000;
      width: 209px;
    }

    .fileup {
      display: inline;
    }

    .click-upload {
      font-weight: 500;
      cursor: pointer;
    }

    .upload-note {
      font-size: 14px;
    }
  }
}

@media screen and (max-width: 1024px) {
  .whyworkWithUs {
    margin: 50px 0;

    .content {
      .box {
        padding-top: 20px;
        margin-top: 20px;

        &:nth-child(odd) {
          padding-right: 10px;
        }

        &:nth-child(even) {
          padding-left: 10px;
        }

        &:nth-child(1),
        &:nth-child(2) {
          margin-top: 20px;
        }

        p {
          font-size: 14px;
        }
      }

      .icon {
        width: 40px;
      }
    }
  }

  .careerOpening {
    margin: 50px 0;

    .tabs {
      gap: 20px;
    }
  }
}

@media screen and (max-width: 768px) {
  .careerPopup {
    .box {
      h5 {
        margin-bottom: 20px;
      }
    }

    .upload-container {
      flex-direction: column;
      align-items: flex-start;

      .upload-info {
        margin-left: 0;
      }

      .upload {
        width: 100%;
      }

      .upload-label {
        max-width: 100%;
      }

      .upload-info {
        width: 100%;
      }
    }
  }

  .careerOpening {
    .tabs {
      justify-content: start;
      gap: 10px;
      margin-bottom: 24px;
      overflow: auto;

      .tab {
        width: 100%;
        min-width: max-content;
        padding: 10px;
      }
    }
  }
}

@media screen and (max-width: 640px) {
  .careerOpening {
    .job-cards {
      grid-template-columns: 1fr;
    }
  }

  .whyworkWithUs {
    .content {
      grid-template-columns: 1fr;

      .box:nth-child(even),
      .box:nth-child(odd) {
        padding-left: 0;
        padding-right: 0;
      }

      .box:nth-child(2) {
        padding-top: 20px;
        margin-top: 20px;
        border-top: 1px solid #bdbdbd;
      }
    }
  }

  .careerPopup {
    .close {
      top: 10px;
      right: 10px;
    }

    .box {
      padding: 20px;
    }

    .form {
      input[type="text"],
      input[type="url"],
      input[type="tel"],
      input[type="email"],
      input[type="password"],
      input[type="number"],
      select,
      textarea,
      input[type="date"] {
        margin-bottom: 15px;
        padding: 8px 15px 10px 12px;
      }
    }

    .upload-container {
      margin-bottom: 15px;
    }
  }
}

// Empty States
.empty-state {
  padding: 3rem 1rem;
  text-align: center;
  color: #6c757d;

  .empty-jobs-icon {
    color: #dee2e6;
    margin-bottom: 1rem;

    svg {
      width: 64px;
      height: 64px;
    }
  }

  h4 {
    color: #495057;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
  }

  p {
    font-size: 1rem;
    line-height: 1.6;
    margin: 0;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
  }

  // Specific empty states
  &.no-benefits {
    background-color: #f8f9fa;
    border-radius: 8px;
    margin: 2rem 0;
  }

  &.no-jobs {
    min-height: 300px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  &.no-category-jobs {
    padding: 2rem 1rem;
    background-color: #fdfdfe;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin-top: 2rem;
  }
}

// Form Validation Styles
.form {
  .required {
    color: #dc3545;
    // margin-left: 3px;
    font-weight: 600;
  }

  // Error messages
  .error-message {
    color: #dc3545;
    font-size: 1rem;
    // margin-top: 0.375rem;
    display: flex;
    align-items: flex-start;
    line-height: 1.4;
    position: absolute;
    bottom: 10px;

    &::before {
      content: "⚠";
      margin-right: 0.375rem;
      font-size: 0.875rem;
      flex-shrink: 0;
    }
  }

  // Character count
  .character-count {
    font-size: 1rem;
    color: #6c757d;
    text-align: right;
    // margin-top: 0.25rem;

    &.warning {
      color: #fd7e14;
    }

    &.danger {
      color: #dc3545;
    }
  }
}

// File Upload Validation
.upload-container {
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
  background-color: #fdfdfe;

  &.error {
    border-color: #dc3545;
    background-color: #fff5f5;

    .upload-note {
      color: #dc3545;
    }
  }

  &.dragover {
    border-color: #007bff;
    background-color: #f0f8ff;
  }

  .upload-note {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.5rem;

    .required {
      color: #dc3545;
      font-weight: 600;
    }
  }

  // File error message
  .error-message {
    margin-top: 1rem;
    padding: 0.5rem 1rem;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    color: #721c24;
    font-size: 0.8rem;
    display: inline-block;

    &::before {
      content: "❌";
      margin-right: 0.5rem;
    }
  }
}

// Success states
.form .field {
  input.valid,
  textarea.valid {
    border-color: #28a745;
    background-color: #f8fff9;

    &:focus {
      border-color: #28a745;
      box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
  }

  .success-message {
    color: #28a745;
    font-size: 0.75rem;
    margin-top: 0.375rem;

    &::before {
      content: "✓";
      margin-right: 0.375rem;
      font-weight: bold;
    }
  }
}

// Loading states for empty content
.loading-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  color: #6c757d;

  .spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Error state for sections
.section-error {
  padding: 2rem;
  text-align: center;
  background-color: #fff5f5;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  margin: 1rem 0;

  .error-icon {
    font-size: 2rem;
    color: #dc3545;
    margin-bottom: 1rem;
  }

  h4 {
    color: #721c24;
    margin-bottom: 0.5rem;
  }

  p {
    color: #856464;
    margin-bottom: 1rem;
  }

  .retry-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover {
      background-color: #c82333;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .empty-state {
    padding: 2rem 1rem;

    h4 {
      font-size: 1.25rem;
    }

    .empty-jobs-icon svg {
      width: 48px;
      height: 48px;
    }
  }

  .upload-container {
    padding: 1.5rem 1rem;
  }

  .form .error-message {
    font-size: 0.7rem;
  }
}
