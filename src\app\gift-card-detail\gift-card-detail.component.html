<div class="orderDetail">
    <div class="container">
      <h1>My Order</h1>
      <div class="head">My Order </div>
      <div class="contentSec">
        <div class="sideBar">
          <div class="field">Gifting Person Details</div>
          <div class="address">
            <div>{{deliveryAddress?.name}}</div>
            <div>{{deliveryAddress?.email}}</div>
            <div>{{deliveryAddress?.mobile}}</div>
          </div>
          <div class="line"></div>
          <div class="field">Billing Address</div>
          <div class="address">
            <div>{{billingAddress?.billing_name}}</div>
            <div>{{billingAddress?.billing_address}}</div>
            <div>{{billingAddress?.billing_city}}-{{billingAddress?.billing_pincode}}</div>
            <div>{{billingAddress?.billing_state}}, {{billingAddress?.billing_country}} </div>
            <div>Mobile Number : {{billingAddress?.billing_mobile}}</div>
          </div>
          <div class="line"></div>
          <div class="field">Delivery Details</div>
          <div class="address">
            Order Id : {{productdata?.Order_id}}
          </div>
          <div class="line"></div>
          <div class="field">Payment Details</div>
          <div class="address" *ngFor="let item of data?.amount_data">
            {{item?.label}}: ₹ {{item?.amount}}
          </div>
        </div>
        <div class="content">
          <div class="orders">
            <span class="image"><img src="{{imageBase}}/{{productdata?.thumbnail}}" alt="gift card image"></span>
            <div class="orderDetails">
              <div class="orderId">Order Id : {{productdata?.Order_id}}</div>
              <div class="item">{{productdata?.name}}</div>
              <div class="price">₹{{productdata?.price}}</div>
            </div>
            <div class="bottom">
              <div class="statusAs">
                <div class="stat">
                  <span class="color"></span>
                  <div class="rightSide">
                    <div class="date">{{productdata?.status?.name}} on {{productdata?.date}}</div>
                  </div>
                </div>
              </div>
            </div>
            <!-- <div class="button" *ngIf="data?.cancellation_possible == 1" (click)="cancelOrder()">Cancel item</div> -->
          </div>
          <!-- delivered -->
          <!-- <div class="orderedStatus"> -->
            <!-- <div class="statusBar">
              <div class="sections"
                [ngClass]="{'active': item.is_active == 1,'cancelled' : item.slug == 'cancelled' || item.slug == 'returned'}"
                *ngFor="let item of data?.statuses">
                <div class="text">{{item?.name}}</div>
                <div class="line"></div>
                <div class="date">
                  <div *ngIf="item.date != null">{{item?.date}}</div>
                  <div *ngIf="item.time != null">{{item?.time}}</div>
                </div>
              </div>
            </div> -->
            <!-- <div class="bottom"> -->
              <!-- <div class="itemStatus">
                <div> Your item has been Assembling </div>
                <div class="return" (click)="returnOrder()" *ngIf="data?.return_possible == 1"><img
                    src="\assets\images\my-account\return.svg" alt=""> Return</div>
              </div> -->
              <!-- <div class="statusAs">
                <div class="stat">
                  <span class="color"></span>
                    <div class="rightSide">
                      <div class="date">Delivery Expected by Feb 2 2022</div>
                    </div>
                </div>
              </div> -->
            <!-- </div> -->
          <!-- </div> -->
        </div>
    </div>
  </div>
