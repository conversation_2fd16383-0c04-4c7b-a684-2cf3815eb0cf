<!-- Banner Section -->
<div class="bannerSection" *ngIf="!isLoading && !pageError">
  <img
    [src]="pageContent?.banner?.image || ''"
    [alt]="pageContent?.banner?.title || 'banner image'"
    class="img-fluid w-full"
  />
</div>

<!-- Page Error State -->
<div *ngIf="pageError" class="error-container text-center py-5">
  <div class="alert alert-danger">
    <h4>Unable to Load Page Content</h4>
    <p>{{ pageError }}</p>
    <button class="btn btn-primary" (click)="retryLoadPageContent()">
      Try Again
    </button>
  </div>
</div>

<!-- Why Work With Us Section -->
<div class="whyworkWithUs" *ngIf="!isLoading && !pageError">
  <div class="container">
    <h2>Why Work With Us</h2>

    <!-- Benefits Content -->
    <div class="content" *ngIf="hasPageContent()">
      <div
        *ngFor="let benefit of pageContent?.benefitsHeading?.benefits"
        class="box"
      >
        <div class="boxes">
          <img [src]="benefit.icon" alt="icons" class="icon" />
          <h6>{{ benefit.title }}</h6>
          <p>{{ benefit.description }}</p>
        </div>
      </div>
    </div>

    <!-- Empty State for Benefits -->
    <div *ngIf="!hasPageContent()" class="empty-state text-center py-4">
      <p class="text-muted">No benefits information available at the moment.</p>
    </div>
  </div>
</div>

<!-- Career Openings Section -->
<div class="careerOpening">
  <div class="container">
    <h2>Current Openings</h2>

    <!-- Jobs Error State -->
    <div *ngIf="jobsError" class="alert alert-warning">
      <p>{{ jobsError }}</p>
      <button class="btn btn-outline-primary btn-sm" (click)="retryLoadJobs()">
        Retry Loading Jobs
      </button>
    </div>

    <!-- Tabs -->
    <div class="tabs" *ngIf="tabs?.length && !jobsError">
      <div
        *ngFor="let tab of tabs"
        (click)="activeTab = tab.id"
        [class.active]="activeTab === tab.id"
        class="tab"
      >
        {{ tab.label }}
      </div>
    </div>

    <!-- Job Cards -->
    <div class="job-cards" *ngIf="hasJobsAvailable() && !jobsError">
      <div *ngFor="let job of jobData[activeTab]" class="job-card">
        <h6>{{ job.title }}</h6>
        <button class="apply-btn" (click)="openModal(job.title, job.id)">
          Apply Now
        </button>
      </div>
    </div>

    <!-- Empty State for Jobs -->
    <div
      *ngIf="
        !hasJobsAvailable() && !jobsError && tabs.length === 0 && !isLoading
      "
      class="empty-state text-center py-5"
    >
      <div class="empty-jobs-icon mb-3">
        <svg
          width="64"
          height="64"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="1.5"
        >
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
          <line x1="8" y1="21" x2="16" y2="21"></line>
          <line x1="12" y1="17" x2="12" y2="21"></line>
        </svg>
      </div>
      <h4>No Current Openings</h4>
      <p class="text-muted">
        We don't have any job openings at the moment. Please check back later
        for new opportunities.
      </p>
    </div>

    <!-- Empty State for Active Tab -->
    <div
      *ngIf="tabs.length > 0 && !jobData[activeTab]?.length && !jobsError"
      class="empty-state text-center py-4"
    >
      <p class="text-muted">
        No positions available in this category at the moment.
      </p>
    </div>
  </div>
</div>

<!-- Application Modal -->
<div class="careerPopup career" [ngClass]="careerModal ? 'show' : 'hide'">
  <div class="box">
    <div class="close" (click)="popUpClose()">
      <img src="assets/images/close-white.svg" alt="Close" />
    </div>
    <div class="content">
      <h5>Apply for {{ selectedJobTitle || "Position" }}</h5>

      <form
        [formGroup]="applyForm"
        (ngSubmit)="onSubmit()"
        class="form"
        novalidate
      >
        <!-- Name and Email Row -->
        <div class="grid">
          <div class="field">
            <label>Name <span class="required">*</span></label>
            <input
              type="text"
              formControlName="name"
              [class.error]="hasError('name')"
              placeholder="Enter your full name"
            />
            <div class="error-message" *ngIf="hasError('name')">
              {{ getErrorMessage("name") }}
            </div>
          </div>
          <div class="field">
            <label>Email <span class="required">*</span></label>
            <input
              type="email"
              formControlName="email"
              [class.error]="hasError('email')"
              placeholder="Enter your email address"
            />
            <div class="error-message" *ngIf="hasError('email')">
              {{ getErrorMessage("email") }}
            </div>
          </div>
        </div>

        <!-- Phone and Qualification Row -->
        <div class="grid">
          <div class="field">
            <label>Phone Number <span class="required">*</span></label>
            <input
              type="tel"
              formControlName="phone"
              [class.error]="hasError('phone')"
              placeholder="Enter 10-digit phone number"
            />
            <div class="error-message" *ngIf="hasError('phone')">
              {{ getErrorMessage("phone") }}
            </div>
          </div>
          <div class="field">
            <label>Qualification <span class="required">*</span></label>
            <input
              type="text"
              formControlName="qualification"
              [class.error]="hasError('qualification')"
              placeholder="Enter your highest qualification"
            />
            <div class="error-message" *ngIf="hasError('qualification')">
              {{ getErrorMessage("qualification") }}
            </div>
          </div>
        </div>

        <!-- File Upload Section -->
        <div
          class="upload-container"
          [class.dragover]="isDragOver"
          [class.error]="fileError"
          (dragover)="onDragOver($event)"
          (dragleave)="onDragLeave($event)"
          (drop)="onDrop($event)"
        >
          <div class="upload">
            <div class="upload-icon">
              <img src="assets/images/career/upload.svg" alt="upload icon" />
            </div>

            <label class="upload-label">
              <input
                type="file"
                #fileInput
                hidden
                accept=".pdf"
                (change)="onFileSelected($event)"
              />
              <button
                class="upload-btn"
                type="button"
                (click)="fileInput.click()"
              >
                Choose File
              </button>
              <span class="file-name" [class.selected]="fileName">
                {{ fileName || "No file chosen" }}
              </span>
            </label>
          </div>
          <div class="upload-info">
            <div class="fileup">
              <span class="click-upload" (click)="fileInput.click()">
                Click to Upload
              </span>
              or Drag and Drop
            </div>
            <div class="upload-note">
              (PDF format only, Max. file size: 25 MB)
              <span class="required">*</span>
            </div>
          </div>

          <!-- File Error Message -->
          <div class="error-message" *ngIf="fileError">
            {{ fileError }}
          </div>
        </div>

        <!-- Message Field -->
        <div>
          <div class="field">
            <label>Message (Optional)</label>
            <textarea
              rows="4"
              formControlName="message"
              [class.error]="hasError('message')"
              placeholder="Tell us why you're interested in this position..."
            ></textarea>
            <div class="error-message" *ngIf="hasError('message')">
              {{ getErrorMessage("message") }}
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="apply">
          <button type="submit" class="apply-btn" [disabled]="isSubmitting">
            <span
              *ngIf="isSubmitting"
              class="spinner-border spinner-border-sm me-2"
            ></span>
            {{ isSubmitting ? "Submitting..." : "Apply Now" }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
