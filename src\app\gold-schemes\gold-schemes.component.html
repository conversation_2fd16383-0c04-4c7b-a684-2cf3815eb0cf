<div
  class="adBanner"
  [ngStyle]="{ 'background-image': 'url(' + firstBanner + ')' }"
>
  <div class="content">
    <h1>{{ goldSchemeData?.scheme_title }}</h1>
    <p>{{ goldSchemeData?.scheme_description }}</p>
    <div class="buttons">
      <span (click)="loginCheck()" class="primary-button golden"
        >Open new account</span
      >
      <a (click)="schemePopUp()" class="primary-button white"
        >Login to existing account</a
      >
    </div>
  </div>
</div>

<div class="container">
  <div class="howItWorks">
    <div class="title">
      <h2>How it works</h2>
      <p>3 easy steps to purchase the jewellery of your dreams</p>
    </div>
    <div class="contents">
      <div class="box">
        <img src="\assets\images\advance-booking\Group 21755.svg" alt="icon" />
        <div class="step">Easy Payment Options</div>
        <p>Pick a scheme that suits you and pay fixed monthly Instalments.</p>
      </div>
      <div class="box">
        <img src="\assets\images\gold-scheme\Group 21727.svg" alt="icon" />
        <div class="step">Enjoy Benefits</div>
        <p>Get up to 100% BONUS based on your 1st Instalments.</p>
      </div>
      <div class="box">
        <img src="\assets\images\gold-scheme\Group 21726.svg" alt="icon" />
        <div class="step">Redeem to Shop</div>
        <p>
          Simply use your accumulated amount to make your purchase online and
          offline.
        </p>
      </div>
    </div>
  </div>
</div>
<!-- <div class="container">
  <div class="schemePlans">
    <div class="title">
      <h2>Scheme plans</h2>
      <p>Lorem ipsum dolor sit amet, consectetur
        adipiscing elit Massa morbi malesuada mattis.</p>
    </div>
    <div class="schemes">
      <div class="box" *ngFor="let item of features">
        <h6>{{item?.scheme_name}}</h6>
        <p>{{item?.description}}</p>
        <ul *ngFor="let point of item?.points | slice:0:3">
          <li>{{point}}</li>
        </ul>
      </div>
    </div>
  </div>
</div>
<div class="container" (click)="bannerRedirections(banner1?.redirection_type,banner1?.redirection_id)">
  <div class="bannerAd "><img src="{{imageBase}}/{{banner1?.image}}" alt=""></div>
</div> -->
<div class="justDo">
  <div class="container">
    <div class="section" [innerHTML]="goldSchemeData?.scheme_content">
      <!-- <h2>Just Choose it & Own it!</h2>
      <p>Choose your investment plan, and own the jewellery you desire. MIGOLD offers a unique scheme to save money on
        purchases through monthly advance payments starting at Rs 1000.</p> -->
    </div>
    <!-- <div class="section">
      <h2>The MiGold Schemes</h2>
      <p>MIGOLD offers three schemes - two investment plans with 11 months duration and one with 24 months. If you are
        selecting a plan for 11 months, there are two options to get the benefits - by cash or by weight.</p>
      <p>For the cash plan, you can invest in multiples of thousands, and the range is between Rs 1000 and Rs 16,000. If
        you pay Rs 1000, at the end of 11 months, TT Devassy Jewellery will add a bonus of Rs 1000; If your investment
        is Rs 2000, the addition by the jeweller will be Rs 2000. The aggregate amount can be redeemed for purchasing
        any jewellery of your choice.</p>
      <p>The By Weight plan with 11 months duration is open for investments starting from Rs 5000 and going up to one
        lakh. The monthly payment will be converted into gold based on its rate. On the 12th month, TT Devassy Jewellery
        will put in the equivalent of the monthly payment in gold. So, this scheme is the same as buying gold every
        month for a whole year. The added advantage of the By Weight scheme is that it will protect the customer from
        the price hike as their investment has already been converted to gold.</p>
      <p>The 24 months scheme is highly beneficial for those planning big purchases, The monthly payment is Rs 10,000
        and above in its multiples, and you can redeem the amount on the 25th, 26th or 27th month with a host of
        benefits. On the 25th month, we will add 150 per cent of the monthly payment (Rs 15000 for a customer who pays
        Rs 10,000 monthly). This bonus will go up to 200 per cent in the 26th month, and 225 per cent in the 27th month.
      </p>
      <p>MIGOLD is an easy advance payment scheme with multiple options to buy gold and save money!</p>
      <a routerLink="/gold-account">Click here to enrol in this scheme now!</a>
    </div> -->
    <!-- <div class="section">
      <h2>Why Invest In MiGold?</h2>
      <p>You have many golden reasons to choose MiGold!</p>
      <ul>
        <li>Pay small amounts in advance and redeem the aggregate at the end of the scheme</li>
        <li>It offers several options and a range of payments starting from Rs 1000</li>
        <li>This scheme helps you save every month for a later purchase</li>
        <li>It has the benefit of avoiding gold price hikes to an extent</li>
        <li>Zero risk of storing gold at your home as your precious gold will be in our safe custody</li>
        <li>Get fabulous incentives or bonuses on the completion of your term period.</li>
      </ul>
    </div> -->
    <div class="howTo" [innerHTML]="goldSchemeData?.scheme_content2">
      <!-- <h4>How To Join?</h4>
      <a routerLink="/gold-account"><span>Just click here</span> to join or walk into T. T. Devassy Jewellery showrooms in Kochi or Kunnamkulam.</a> -->
    </div>
  </div>
</div>

<div class="schemeCalculator">
  <div class="container">
    <div class="title">
      <h2>Scheme Calculator</h2>
    </div>
    <div class="calculator">
      <div class="leftSide">
        <canvas
          baseChart
          [type]="'pie'"
          [datasets]="pieChartDatasets"
          [labels]="pieChartLabels"
          [options]="pieChartOptions"
          [plugins]="pieChartPlugins"
          [legend]="pieChartLegend"
        >
        </canvas>
      </div>
      <div class="rightSide">
        <label>Choose a plan</label>
        <span class="rate plan">
          <ng-select
            class="filtername selectOption"
            [items]="plans"
            bindLabel="name"
            bindValue="id"
            [(ngModel)]="selectedPlan"
            [clearable]="false"
            [searchable]="false"
            (change)="getAmounts()"
          >
          </ng-select>
        </span>
        <label>Select amount</label>
        <span class="rate ratecheck">
          <ng-select
            class="filtername selectOption"
            [clearable]="false"
            [searchable]="false"
            [(ngModel)]="selectedAmount"
          >
            <ng-option
              value="{{ amount }}"
              *ngFor="let amount of schemeAmounts?.amounts"
            >
              {{ amount }}
            </ng-option>
          </ng-select>
          <div class="check" (click)="getSchemeCalculation()">Check</div>
        </span>
        <div class="sections">
          <div class="text">
            <span class="color" style="background-color: #d2ab66"></span>
            <span class="name">
              <div>Your total payment</div>
              <div>(11 installments)</div>
            </span>
          </div>
          <div class="amount">₹{{ calculation?.total_payment }}</div>
        </div>
        <div class="sections">
          <div class="text">
            <span class="color" style="background-color: #d9d9d9"></span>
            <span class="name">Customer Bonus</span>
          </div>
          <div class="amount">₹{{ calculation?.bonus }}</div>
        </div>
        <div class="sections total">
          <div class="text">
            <div class="head">
              <div class="get">You can buy jewellery worth:</div>
              <div class="month">({{ calculation?.buy_worth_label }})</div>
            </div>
          </div>
          <div class="amount">₹{{ calculation?.buy_worth }}</div>
        </div>
      </div>
    </div>
    <div class="rateBox">
      <ng-container *ngFor="let data of calculation?.boxes">
        <div class="box">
          <div class="heading">{{ data.title }}</div>
          <div class="sections">
            <div class="text">
              <div class="name">
                Your Total Payment <br />
                ({{ data?.installment_label }})
              </div>
            </div>
            <div class="amount">₹{{ data?.box_total_payment }}</div>
          </div>
          <div class="sections">
            <div class="text">
              <span class="name">Customer Bonus</span>
            </div>
            <div class="amount">₹{{ data?.box_bonus }}</div>
          </div>
          <div class="sections total">
            <div class="text">
              <div class="head">
                <div class="get">Total</div>
              </div>
            </div>
            <div class="amount">₹{{ data?.box_total }}</div>
          </div>
        </div>
      </ng-container>
      <!-- <div class="box">
        <div class="heading">Redeem on 13th Month</div>
        <div class="sections">
          <div class="text">
            <span class="name">Customer Bonus</span>
          </div>
          <div class="amount">₹500</div>
        </div>
        <div class="sections">
          <div class="text">
            <span class="name">Customer Bonus</span>
          </div>
          <div class="amount">₹500</div>
        </div>
        <div class="sections total">
          <div class="text">
            <div class="head">
              <div class="get">Total </div>
            </div>
          </div>
          <div class="amount">₹11500</div>
        </div>
      </div> -->
    </div>
  </div>
</div>

<div
  class="container"
  (click)="
    bannerRedirections(banner2?.redirection_type, banner2?.redirection_id?banner2?.redirection_id:'')
  "
>
  <div class="bannerAd">
    <img src="{{ imageBase }}/{{ banner2?.image }}" alt="banner image" />
  </div>
</div>

<!-- <div class="justDo">
  <div class="container">
    <div class="section">
      <h2>Just Choose it & Own it!</h2>
      <p>Choose your investment plan, and own the jewellery you desire. MIGOLD offers a unique scheme to save money on
        purchases through monthly advance payments starting at Rs 1000.</p>
    </div>
    <div class="section">
      <h2>The MiGold Schemes</h2>
      <p>MIGOLD offers three schemes - two investment plans with 11 months duration and one with 24 months. If you are
        selecting a plan for 11 months, there are two options to get the benefits - by cash or by weight.</p>
      <p>For the cash plan, you can invest in multiples of thousands, and the range is between Rs 1000 and Rs 16,000. If
        you pay Rs 1000, at the end of 11 months, TT Devassy Jewellery will add a bonus of Rs 1000; If your investment
        is Rs 2000, the addition by the jeweller will be Rs 2000. The aggregate amount can be redeemed for purchasing
        any jewellery of your choice.</p>
      <p>The By Weight plan with 11 months duration is open for investments starting from Rs 5000 and going up to one
        lakh. The monthly payment will be converted into gold based on its rate. On the 12th month, TT Devassy Jewellery
        will put in the equivalent of the monthly payment in gold. So, this scheme is the same as buying gold every
        month for a whole year. The added advantage of the By Weight scheme is that it will protect the customer from
        the price hike as their investment has already been converted to gold.</p>
      <p>The 24 months scheme is highly beneficial for those planning big purchases, The monthly payment is Rs 10,000
        and above in its multiples, and you can redeem the amount on the 25th, 26th or 27th month with a host of
        benefits. On the 25th month, we will add 150 per cent of the monthly payment (Rs 15000 for a customer who pays
        Rs 10,000 monthly). This bonus will go up to 200 per cent in the 26th month, and 225 per cent in the 27th month.
      </p>
      <p>MIGOLD is an easy advance payment scheme with multiple options to buy gold and save money!</p>
      <a href="#">Click here to enrol in this scheme now!</a>
    </div>
    <div class="section">
      <h2>Why Invest In MiGold?</h2>
      <p>You have many golden reasons to choose MiGold!</p>
      <ul>
        <li>Pay small amounts in advance and redeem the aggregate at the end of the scheme</li>
        <li>It offers several options and a range of payments starting from Rs 1000</li>
        <li>This scheme helps you save every month for a later purchase</li>
        <li>It has the benefit of avoiding gold price hikes to an extent</li>
        <li>Zero risk of storing gold at your home as your precious gold will be in our safe custody</li>
        <li>Get fabulous incentives or bonuses on the completion of your term period.</li>
      </ul>
    </div>
    <div class="howTo">
      <h4>How To Join?</h4>
      <a href="#">Just click here to join or walk into T. T. Devassy Jewellery showrooms in Kochi or Kunnamkulam.</a>
    </div>

  </div>
</div> -->

<div class="faqSection">
  <div class="container">
    <div class="title">
      <h2>FAQ</h2>
    </div>
    <div class="faq">
      <div class="firstSec">
        <div class="drops" *ngFor="let item of faqs; let i = index">
          <button
            class="faq-accordion"
            (click)="showAccordion(i + 1)"
            [ngClass]="{ active: selectedIndex == i + 1 }"
          >
            {{ item?.question }}
          </button>
          <div
            class="course-panel"
            [ngClass]="{ 'open-accordion': selectedIndex == i + 1 }"
          >
            <p>{{ item?.answer }}</p>
          </div>
        </div>
        <!-- <div class="drops">
          <button class="faq-accordion" (click)="showAccordion(2)" [ngClass]="{'active': selectedIndex == 2}">How do I
            unsubscribe from The emails?</button>
          <div class="course-panel" [ngClass]="{'open-accordion': selectedIndex == 2}">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Feugiat ultrices tellus vitae viverra dolor,
              quis id dictum diam. r sit amet, csectetur adipiscing elit. Feu</p>
          </div>
        </div>
        <div class="drops">
          <button class="faq-accordion" (click)="showAccordion(3)" [ngClass]="{'active': selectedIndex == 3}">Lorem
            ipsum dolor sit amet, consectetur adipiscing eli</button>
          <div class="course-panel" [ngClass]="{'open-accordion': selectedIndex == 3}">
            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Feugiat ultrices tellus vitae viverra dolor,
              quis id dictum diam. r sit amet, csectetur adipiscing elit. Feu</p>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</div>

<div class="helpiline">
  <div class="container">
    <div class="content">
      <span>Help Line Number</span>
      <a href="callto:+91 9497 916 000"
        ><img src="\assets\images\advance-booking\Vector.svg" alt="phone icon" />+91 9497
        916 000</a
      >
    </div>
  </div>
</div>
