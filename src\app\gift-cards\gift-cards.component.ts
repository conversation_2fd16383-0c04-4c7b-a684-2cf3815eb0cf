import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';

@Component({
  selector: 'app-gift-cards',
  templateUrl: './gift-cards.component.html',
  styleUrls: ['./gift-cards.component.scss']
})
export class GiftCardsComponent implements OnInit {
  data: any;

  constructor(private ApiService: ApiService, private toast: ToastrService) { }

  ngOnInit(): void {
    this.getGiftCard()
  }

  getGiftCard() {
    this.ApiService.getData(apiEndPoints.GIFT_CARD).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.data = res?.Data
      } else this.toast.error(res?.Message)
    })
  }

}
