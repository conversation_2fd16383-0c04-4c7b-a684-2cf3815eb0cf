<ngx-spinner
  bdColor="rgba(0, 0, 0, 0.8)"
  size="medium"
  color="#fff"
  type="square-jelly-box"
  [fullScreen]="true"
>
  <div class="spinner-content">
    <p style="color: white; font-size: 20px; font-weight: 500; margin-bottom: 8px;">
      {{ paymentMessage || 'Processing payment...' }}
    </p>
    <p style="color: white; font-size: 14px;">
      Please do not refresh page or go back
    </p>
  </div>
</ngx-spinner>

<div class="myAccount">
  <div class="container">
    <div class="title">
      <h1>My Account</h1>
    </div>
    <div class="contentSection">
      <div class="sideMenu">
        <app-sidebar></app-sidebar>
      </div>
      <!-- gold scheme -->
      <div class="contents" *ngIf="schemeList">
        <div class="head">
          <span>My Gold Schemes</span>
          <div class="add" (click)="showAddAccount()">Add Account</div>
        </div>
        <div class="goldSchemes" *ngFor="let item of mySchemes">
          <div class="id">Id : {{ item?.scheme_id }}</div>
          <div class="data">
            <div class="leftSide">
              <div class="box">
                <div class="field">
                  <div class="text">Scheme Holder Name :</div>
                  <div class="name">{{ item?.scheme_holder_name }}</div>
                </div>
                <div class="field">
                  <div class="text">Scheme Name :</div>
                  <div class="name">{{ item?.scheme_name }}</div>
                </div>
              </div>
              <div class="box">
                <div class="field">
                  <div class="text">Mobile Number :</div>
                  <div class="name">{{ item?.mobile_number }}</div>
                </div>
                <div class="field">
                  <div class="text">Last paid date:</div>
                  <div class="name">{{ item?.last_paid_date }}</div>
                </div>
              </div>
            </div>
            <div class="rightSide">
              <div class="primary-button white" (click)="viewDetail(item?.id)">
                View Details
              </div>
              <!-- <div class="primary-button golden" *ngIf="item?.paynow_available" (click)="upiModal(item?.id)">Pay Now</div> -->
              <div
                class="primary-button golden"
                *ngIf="item?.paynow_available"
                (click)="payNow(item?.id)"
              >
                Pay Now
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- gold scheme details -->
      <div class="contents" *ngIf="viewDetails">
        <div class="head"><span>Gold Schemes details</span></div>
        <div class="goldSchemes">
          <div class="data">
            <div class="leftSide">
              <div class="box">
                <div class="field">
                  <div class="text">Scheme Holder Name :</div>
                  <div class="name">
                    {{ schemeDetails?.scheme_holder_name }}
                  </div>
                </div>
                <div class="field">
                  <div class="text">Address :</div>
                  <div class="name">{{ schemeDetails?.address }}</div>
                </div>
              </div>
              <div class="box">
                <div class="field">
                  <div class="text">Scheme Id :</div>
                  <div class="name">{{ schemeDetails?.scheme_id }}</div>
                </div>
                <div class="field">
                  <div class="text">Mobile Number :</div>
                  <div class="name">{{ schemeDetails?.mobile_number }}</div>
                </div>
              </div>
              <div class="box">
                <div class="field">
                  <div class="text">Scheme Name :</div>
                  <div class="name">{{ schemeDetails?.scheme_name }}</div>
                </div>
                <div class="field">
                  <div class="text">Last paid date:</div>
                  <div class="name">{{ schemeDetails?.last_paid_date }}</div>
                </div>
              </div>
              <div class="box">
                <div class="field">
                  <div class="text">Opened Date :</div>
                  <div class="name">{{ schemeDetails?.opened_date }}</div>
                </div>
                <div class="field">
                  <div class="text">Maturity Date:</div>
                  <div class="name">{{ schemeDetails?.maturity_date }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="transactionDetails">
            <div class="heading">Transaction Details</div>
            <table class="rwd-table">
              <thead>
                <tr>
                  <th>Sl No</th>
                  <th>Date</th>
                  <th>Payment Type</th>
                  <th>Installment</th>
                  <th *ngIf="schemeDetails?.is_weight_plan">
                    Alloted Gold Weight
                  </th>
                  <th *ngIf="schemeDetails?.is_weight_plan">Gold Rate</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of schemeDetails?.transactions">
                  <td data-th="Supplier Code">
                    {{ item?.sl_no }}
                  </td>
                  <td data-th="Supplier Name">
                    {{ item?.date }}
                  </td>
                  <td data-th="Invoice Number">
                    {{ item?.Payment_type }}
                  </td>
                  <td data-th="Invoice Date">₹{{ item?.Installment }}</td>
                  <td
                    *ngIf="schemeDetails?.is_weight_plan"
                    data-th="Invoice Date"
                  >
                    {{ item?.gold_weight }}gm
                  </td>
                  <td
                    *ngIf="schemeDetails?.is_weight_plan"
                    data-th="Invoice Date"
                  >
                    ₹{{ item?.gold_rate }}
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="button">
          <div class="primary-button white" (click)="back()">Back</div>
          <div
            class="primary-button golden"
            *ngIf="schemeDetails?.paynow_available"
            (click)="payNow(schemeDetails?.id)"
          >
            Pay Now
          </div>
          <div
            class="primary-button golden"
            *ngIf="schemeDetails?.redeem_available"
            (click)="redeemScheme()"
          >
            Redeem
          </div>
        </div>
      </div>
      <!-- add account -->
      <div class="contents" *ngIf="addAccount">
        <div class="head"><span>Add Account</span></div>
        <div class="goldSchemes addAccount">
          <div class="data">
            <div class="field leftField">
              <label for="">scheme id</label>
              <input type="text" [(ngModel)]="schemeId" />
              <span class="text-danger" *ngIf="!schemeId">{{
                validation
              }}</span>
            </div>
            <div class="field rightField" *ngIf="showOtp">
              <label>OTP</label>
              <input type="text" [(ngModel)]="schemeOtp" />
            </div>
          </div>
        </div>
        <div class="button">
          <div class="primary-button white" (click)="back()">Back</div>
          <div
            class="primary-button golden"
            *ngIf="!showOtp"
            (click)="getExisting()"
          >
            Get OTP
          </div>
          <div class="primary-button golden" *ngIf="showOtp" (click)="verify()">
            Verify
          </div>
        </div>
      </div>
      <div id="recaptcha-container"></div>
    </div>
  </div>
</div>

<div class="registerPopup otp" *ngIf="PopupPaymentOtp">
  <div class="box">
    <div class="close" (click)="popUpClose('otp')">
      <img src="\assets\images\close-white.svg" alt="close icon" />
    </div>
    <div class="head">OTP</div>
    <p>We have send an OTP to the scheme holders Mobile number</p>
    <ng-otp-input
      (onInputChange)="onOtpChange($event)"
      [config]="otpInputConfig"
    ></ng-otp-input>
    <div class="field" (click)="schemeOtpVerify()">
      <input type="submit" value="Verify OTP" />
    </div>
    <div class="bottom">
      <div class="resend">Resend OTP</div>
    </div>
  </div>
</div>

<!-- <div class="registerPopup otp" *ngIf="PopupUpi">
  <div class="box">
    <div class="close" (click)="popUpClose('upi')"><img src="\assets\images\close-white.svg" alt="close icon"></div>
    <div class="head">UPI</div>
    <div><input type="text" placeholder="Enter your upi id " [(ngModel)]="upiId"></div>
    <div class="d-flex mb-3 recieve">
      <img src="\assets\images\wallet\Vector.svg" alt="info icon">
      <span>you will recieve a collect request </span>
    </div>
    <div class="field" (click)="payNow()"><input type="submit" value="Continue"></div>
  </div>
</div> -->

<div class="registerPopup otp" *ngIf="PopupUpi">
  <div class="box">
    <div class="close" (click)="popUpClose('upi')">
      <img src="\assets\images\close-white.svg" alt="close icon" />
    </div>

    <div class="payment-content">
      <h2 class="title">Scan & Pay</h2>

      <p class="description">
        {{ schemeUpi?.msg ? schemeUpi?.msg + ". " : "" }}
        Simply scan the QR code using your favorite payment app . Enjoy a quick
        and hassle-free payment experience
      </p>

      <div class="qr-container">
        <qrcode
          [qrdata]="upiIntentUrl"
          [width]="256"
          [errorCorrectionLevel]="'M'"
        ></qrcode>
      </div>
    </div>
  </div>
</div>

<!-- <img src="../../assets/images/coming_soon.png" style=" display: block;
margin-left: auto;
margin-right: auto;
width: 50%;"> -->
