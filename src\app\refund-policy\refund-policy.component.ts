import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';

@Component({
  selector: 'app-refund-policy',
  templateUrl: './refund-policy.component.html',
  styleUrls: ['./refund-policy.component.scss']
})
export class RefundPolicyComponent implements OnInit {
  data: any;

  constructor(private ApiService:ApiService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.REFUND_POLICY).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.data = res?.data
      }
    })

  }

}
