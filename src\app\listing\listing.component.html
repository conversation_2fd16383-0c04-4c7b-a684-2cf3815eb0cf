<!-- <ngx-spinner bdColor="rgba(0, 0, 0, 0.8)" size="medium" color="#fff" type="square-jelly-box" [fullScreen]="true">
  <p style="color: white"> Loading... </p>
</ngx-spinner> -->

<!-- <div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div> -->

<div class="smallBanner"
  (click)="bannerRedirections(banners?.web_top_banner?.redirection_type,banners?.web_top_banner?.slug,banners?.web_top_banner?.title)">
  <img src="{{imageBase}}/{{banners?.web_top_banner?.image}}" alt="banner image">
</div>

<div class="container">
  <div class="breadCrumb">
    <a routerLink="" class="link">Home</a>
    <a class="link">{{data?.name}}</a>
    <!-- <div class="link">Earrings</div> -->
  </div>
</div>

<div class="listingSection">
  <div class="container">
    <div class="heading">
      <h1>{{data?.name|titlecase}}</h1>
      <span class="sub">({{data?.totalCount}} Designs)</span>
    </div>
    <div class="filters">
      <div class="leftSide">
        <span class="filtername selectOption">Price
          <ul class="list priceRange">
            <li><ngx-slider #slider [(value)]="minValue" [(highValue)]="maxValue" [options]="options"
                (userChangeEnd)="priceRanges()"></ngx-slider></li>
          </ul>
        </span>
        <!-- <span class="filtername selectOption  selectPurity">
          <span>Product Type</span>
          <ul class="list">
            <div class="filterValue">
              <div class="form-group" *ngFor="let type of productTypes; let i = index">
                <input type="checkbox" id="{{i}}" (click)="categoryFilter(type.id)">
                <label for="{{i}}">
                  <div class="add">
                    <span>{{type.name}}</span>
                  </div>
                </label>
              </div>
            </div>
          </ul>
        </span> -->
        <span class="filtername selectOption  selectPurity" *ngIf="type!=2">
          <span>Gold Purity</span>
          <ul class="list">
            <div class="filterValue">
              <div class="form-group" *ngFor="let item of gold_purity; let i = index">
                <input type="checkbox" id="{{'gold_purity_' + i}}" [ngModel]="purityValues.includes(item.name)" (click)="purityFilter(item.name)">
                <label for="{{'gold_purity_' + i}}">
                  <div class="add">
                    <span>{{item.name}}</span>
                  </div>
                </label>
              </div>
            </div>
          </ul>
        </span>
        <!-- <span class="filtername">More Filters</span> -->
      </div>
      <div class="rightSide">
        <a class="filtername" (click)="shipFilter()"><img src="\assets\images\delivery.svg" alt="delivery icon"><span>Next Day Shipping</span> </a>
        <div class="filtername selectOption">
          <span>Sort By : </span>
          <div class="selectMenu sort">
            <div class="selected">{{sortType}}</div>
          </div>
          <ul class="list">
            <li *ngFor="let filter of sortValues" (click)="applyFilter(filter?.value,filter?.title)">{{filter?.title}}
            </li>
          </ul>
        </div>
      </div>
    </div>
    <div class="productsListing search-results" infiniteScroll [infiniteScrollDistance]="2" [infiniteScrollThrottle]="50" (scrolled)="onScroll()">
      <span *ngIf="productData && productData.length == 0">No Products Found</span>
      <ng-container
        *ngFor="let product of productData; let i = index">
        <div class="items">
          <div class="ar" id="digital-{{i}}">
            <!-- <img src="\assets\images\ar.png" alt="" /> -->
          </div>
          <div class="wishlist" (click)="addToWishlist(product.id,product.isfavorite)"
            [ngClass]="{active: product.isfavorite}">
            <img class="fav" src="\assets\images\favorite.png" alt="wishlist icon" />
            <img class="fav-gold" src="\assets\images\favorite-gold.png" alt="wishlist icon" />
          </div>
          <a class="productImage" [routerLink]="['/detail',product.slug]">
            <img *ngIf="!product?.image" src="/assets/images/Tt-Loader.gif" alt="tt logo" class="img-fluid" />
            <img *ngIf="product?.image" src="{{imageBase}}/{{product?.image}}" alt="product image" class="img-fluid" />
            <!-- <img *ngIf="!product?.image" src="\assets\images\no image.jpg" alt="" class="img-fluid" /> -->
            <span *ngIf="product?.shipFaster == true" class="shipFaster sheer">Ship Faster</span>
          </a>

          <a [routerLink]="['/detail',product.slug]" class="productName twoLine">{{product?.productName}}</a>
          <a [routerLink]="['/detail',product.slug]" class="price">
            <div class="offer"><span>₹</span> {{product?.price}}</div>
            <div class="old" *ngIf="product?.actualPrice != null"><span>₹</span> {{product?.actualPrice}}</div>
          </a>
          <a [routerLink]="['/detail',product.slug]" class="view">View</a>
        </div>
        <a class="ads" *ngIf="i === 5"
          (click)="bannerRedirections(banners?.web_ad_banner?.redirection_type,banners?.web_ad_banner?.slug,banners?.web_ad_banner?.title)">
          <img src="{{imageBase}}/{{banners?.web_ad_banner?.image}}" alt="ad banner">
        </a>
      </ng-container>
    </div>
    <!-- <pagination-controls *ngIf="data?.totalCount > limit" (pageChange)="pageChanged($event)"></pagination-controls> -->
  </div>
</div>

<div class="mobileFilter">
  <div class="filter" (click)="filterlist()">
    <img src="\assets\images\listing\filter.svg" alt="filter icon"><span>Filter</span>
  </div>
  <!-- <div class="filter category" (click)="categoryList()">
    <img src="\assets\images\listing\category.svg" alt=""><span>Category</span>
  </div> -->
  <div class="filter sort" (click)="sortList()">
    <img src="\assets\images\listing\sort.svg" alt="sort icon"><span>Sort By</span>
  </div>

</div>
<div class="filterList" [ngClass]="filterList?'view':'hide'">
  <div class="head">
    <div class="back" (click)="close()"><img src="\assets\images\listing\arrow_back.svg" alt="arrow icon"></div>
    Filter
  </div>
  <div class="section">
    <div class="leftSide">
      <div class="filterName" (click)="showGoldPurity('price')">Price</div>
      <div class="filterName" (click)="showGoldPurity('purity')">Gold Purity</div>
    </div>
    <div class="rightSide">
      <div class="filterValue" *ngIf="!isGoldPurity">
        <div class="filtername selectOption">    <!--gold form-->
          <!-- <ul class="list priceRange">
            <li><ngx-slider #slider [(value)]="minValue" [(highValue)]="maxValue" [options]="options"
                (userChangeEnd)="priceRanges()"></ngx-slider></li>
          </ul> -->
          <!-- <input type="checkbox" id="500">
          <label for="500">
            <div class="add">
              <span>Rs. 500 - Rs. 2000</span>
            </div>
          </label> -->
        </div>
        <!-- <div class="form-group">
          <input type="checkbox" id="2000">
          <label for="2000">
            <div class="add">
              <span>Rs. 2000 - Rs. 5000</span>
            </div>
          </label>
        </div>
        <div class="form-group">
          <input type="checkbox" id="5000">
          <label for="5000">
            <div class="add">
              <span>Rs. 5000 - Rs. 10000</span>
            </div>
          </label>
        </div> -->
      </div>
      <div class="filterValue" *ngIf="isGoldPurity">
        <div class="form-group" *ngFor="let item of gold_purity; let i = index">
          <input type="checkbox" id="purity+{{i}}" (click)="mobilePurityFilter(item.name)">
          <label for="purity+{{i}}">
            <div class="add">
              <span>{{item.name}}</span>
            </div>
          </label>
        </div>
        <!-- <div class="form-group">
          <input type="checkbox" id="20">
          <label for="20">
            <div class="add">
              <span>20</span>
            </div>
          </label>
        </div>
        <div class="form-group">
          <input type="checkbox" id="22">
          <label for="22">
            <div class="add">
              <span>22</span>
            </div>
          </label>
        </div> -->
      </div>
    </div>
  </div>
  <div class="applyFilter">
    <div class="clear" (click)="clearAll()">Clear All</div>
    <div class="apply">
      <div class="primary-button golden" (click)="applyFilterMob()">Apply Filter</div>
    </div>
  </div>
</div>
<div class="filterList" [ngClass]="category?'view':'hide'">
  <div class="head">
    <div class="back" (click)="close()"><img src="\assets\images\listing\arrow_back.svg" alt="arrow icon"></div>
    Categories
  </div>
  <div class="section">
    <div class="leftSide">
      <div class="filterName">
        <div class="img"><img src="\assets\images\Rectangle 9.png" alt=""></div>
        <div class="name">Gold</div>
      </div>
      <div class="filterName">
        <div class="img"><img src="\assets\images\Rectangle 9.png" alt=""></div>
        <div class="name">Gold</div>
      </div>
      <div class="filterName">
        <div class="img"><img src="\assets\images\Rectangle 9.png" alt=""></div>
        <div class="name">Gold</div>
      </div>
      <div class="filterName">
        <div class="img"><img src="\assets\images\Rectangle 9.png" alt=""></div>
        <div class="name">Gold</div>
      </div>
    </div>
    <div class="rightSide">
      <div class="filterValue">
        <div class="form-group">
          <input type="checkbox" id="500">
          <label for="500">
            <div class="add">
              <span>Rs. 500 - Rs. 2000</span>
            </div>
          </label>
        </div>
        <div class="form-group">
          <input type="checkbox" id="2000">
          <label for="2000">
            <div class="add">
              <span>Rs. 2000 - Rs. 5000</span>
            </div>
          </label>
        </div>
        <div class="form-group">
          <input type="checkbox" id="5000">
          <label for="5000">
            <div class="add">
              <span>Rs. 5000 - Rs. 10000</span>
            </div>
          </label>
        </div>
      </div>
      <div class="filterValue">
        <div class="form-group">
          <input type="checkbox" id="18">
          <label for="18">
            <div class="add">
              <span>18</span>
            </div>
          </label>
        </div>
        <div class="form-group">
          <input type="checkbox" id="20">
          <label for="20">
            <div class="add">
              <span>20</span>
            </div>
          </label>
        </div>
        <div class="form-group">
          <input type="checkbox" id="22">
          <label for="22">
            <div class="add">
              <span>22</span>
            </div>
          </label>
        </div>
      </div>
    </div>
  </div>
  <div class="applyFilter">
    <div class="clear">Clear All</div>
    <div class="apply">
      <div class="primary-button golden">Apply Filter</div>
    </div>
  </div>
</div>

<div class="filterList sorting" [ngClass]="sort?'view':'hide'">
  <div class="head">
    Sort By
    <div class="drop" (click)="close()"><img src="\assets\images\close.svg" alt="sort icon"></div>
  </div>
  <div class="section">
    <div class="radioBtn">
      <span class="radio" *ngFor="let item of sortValues; let i = index" (click)="applyFilter(item?.value,item?.title)">
        <input id="radio-{{i}}" name="radio" type="radio" [(ngModel)]="selectedSortValue" [value]="item.value">
        <label for="radio-{{i}}" class="radio-label">{{item?.title}}</label>
      </span>
      <!-- <span class="radio">
        <input id="radio-2" name="radio" type="radio">
        <label for="radio-2" class="radio-label">Nearly Listed</label>
      </span>
      <span class="radio">
        <input id="radio-3" name="radio" type="radio">
        <label for="radio-3" class="radio-label">Price High to Low</label>
      </span> -->
    </div>
  </div>
</div>

<div class="container">
  <a (click)="bannerRedirections(banners?.web_offer_banner?.redirection_type,banners?.web_offer_banner?.slug,banners?.web_offer_banner?.title)"
    class="offSection">
    <img class="image" src="{{imageBase}}/{{banners?.web_offer_banner?.image}}" alt="offer banner image" />
    <!-- <div class="content">
      <h2>{{banners?.web_offer_banner?.title}}</h2>
      <p>{{banners?.web_offer_banner?.description}}</p>
      <div class="explore">
        <span>{{banners?.web_offer_banner?.button_text}}</span> <img src="\assets\images\arrow.svg" alt="arrow icon" />
      </div>
    </div> -->
  </a>
</div>
