<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <title>TT Devassy Jewellery</title>
  <base href="/" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <link rel="icon" type="image/x-icon" href="favicon.ico" />
  <link rel="stylesheet" href="assets/fonts/stylesheet-gotham.css" type="text/css" />
  <link rel="stylesheet" href="assets/fonts/stylesheet-libre.css" type="text/css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.2.0/css/all.min.css"
    crossorigin="anonymous" referrerpolicy="no-referrer" />
  <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="preconnect" href="https://fonts.gstatic.com" />
  <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/animejs/2.0.2/anime.min.js"></script>
  <script src="https://camweara.com/integrations/camweara_api.js?v=1.6"></script>

  <!-- Razorpay Checkout-->
  <script src="https://checkout.razorpay.com/v1/checkout.js"></script>

  <!-- WEB ENGAGE BETA -->
  <!-- <script id='_webengage_script_tag' type='text/javascript'>
    var webengage;
    ! function (w, e, b, n, g) {
      function o(e, t) {
        e[t[t.length - 1]] = function () {
          r.__queue.push([t.join("."), arguments])
        }
      }
      var i, s, r = w[b],
        z = " ",
        l = "init options track screen onReady".split(z),
        a = "feedback survey notification".split(z),
        c = "options render clear abort".split(z),
        p = "Open Close Submit Complete View Click".split(z),
        u = "identify login logout setAttribute".split(z);
      if (!r || !r.__v) {
        for (w[b] = r = {
          __queue: [],
          is_spa: 1, //Change this to 0 if you do not wish to use default SPA behaviour of WebEngage SDK
          __v: "6.0",
          user: {}
        }, i = 0; i < l.length; i++) o(r, [l[i]]);
        for (i = 0; i < a.length; i++) {
          for (r[a[i]] = {}, s = 0; s < c.length; s++) o(r[a[i]], [a[i], c[s]]);
          for (s = 0; s < p.length; s++) o(r[a[i]], [a[i], "on" + p[s]])
        }
        for (i = 0; i < u.length; i++) o(r.user, ["user", u[i]]);
        setTimeout(function () {
          var f = e.createElement("script"),
            d = e.getElementById("_webengage_script_tag");
          f.type = "text/javascript", f.async = !0, f.src = ("https:" == e.location.protocol ? "https://widgets.in.webengage.com" : "http://widgets.in.webengage.com") + "/js/webengage-min-v-6.0.js", d.parentNode.insertBefore(f, d)
        })
      }
    }(window, document, "webengage");
    webengage.init('in~~15ba20729'); //replace the YOUR_WEBENGAGE_LICENSE_CODE with your WebEngage Account License Code
  </script> -->

  <!-- WEB ENGAGE LIVE -->
  <script id='_webengage_script_tag' type='text/javascript'>
    var webengage;
    ! function (w, e, b, n, g) {
      function o(e, t) {
        e[t[t.length - 1]] = function () {
          r.__queue.push([t.join("."), arguments])
        }
      }
      var i, s, r = w[b],
        z = " ",
        l = "init options track screen onReady".split(z),
        a = "feedback survey notification".split(z),
        c = "options render clear abort".split(z),
        p = "Open Close Submit Complete View Click".split(z),
        u = "identify login logout setAttribute".split(z);
      if (!r || !r.__v) {
        for (w[b] = r = {
          __queue: [],
          is_spa: 1, //Change this to 0 if you do not wish to use default SPA behaviour of WebEngage SDK
          __v: "6.0",
          user: {}
        }, i = 0; i < l.length; i++) o(r, [l[i]]);
        for (i = 0; i < a.length; i++) {
          for (r[a[i]] = {}, s = 0; s < c.length; s++) o(r[a[i]], [a[i], c[s]]);
          for (s = 0; s < p.length; s++) o(r[a[i]], [a[i], "on" + p[s]])
        }
        for (i = 0; i < u.length; i++) o(r.user, ["user", u[i]]);
        setTimeout(function () {
          var f = e.createElement("script"),
            d = e.getElementById("_webengage_script_tag");
          f.type = "text/javascript", f.async = !0, f.src = ("https:" == e.location.protocol ? "https://widgets.in.webengage.com" : "http://widgets.in.webengage.com") + "/js/webengage-min-v-6.0.js", d.parentNode.insertBefore(f, d)
        })
      }
    }(window, document, "webengage");
    webengage.init('in~aa13168a'); //replace the YOUR_WEBENGAGE_LICENSE_CODE with your WebEngage Account License Code
  </script>

  <script src="https://widgets.in.webengage.com/js/webengage-sdk.js"></script>
</head>

<body>
  <app-root></app-root>
</body>

</html>