.myAccount {
  padding: 40px 0 100px;
}
.sideMenu {
  width: 270px;
}
.contentSection {
  display: flex;

  .contents {
    width: calc(100% - 270px);
    padding-left: 50px;
  }
}

h1 {
  font-size: 44px;
  margin-bottom: 25px;
}

.wishlistSec {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -1%;
  .button {
    margin: 0 30px 25px;
  }
}

.slideItems {
  text-align: center;
  background: #ffffff;
  box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.06);
  margin: 20px 1%;
  position: relative;
  width: 31.33%;

  &:hover {
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);

    .productImage {
      img {
        transform: translateY(-10px);
        transition: ease 0.7s;
      }
    }
  }

  .productImage {
    padding: 30px 30px 0;
    overflow: hidden;

    img {
      transition: ease 0.9s;
    }
  }

  .productName {
    font-size: 14px;
    padding: 0 30px 15px;
    line-height: 20px;
  }

  .price {
    font-size: 14px;
    font-weight: 600;
    padding: 0 30px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    .offer {
      padding: 0 5px;
    }
    .old {
      text-decoration: line-through;
      padding: 0 5px;
      color: #b3b3b3;
      font-size: 12px;
    }
  }

  .ar {
    position: absolute;
    top: 50px;
    width: 23px;
    left: 40px;
    cursor: pointer;
    z-index: 2;
  }

  .wishlist {
    z-index: 2;
    position: absolute;
    right: 40px;
    top: 50px;
    cursor: pointer;

    .fav-gold {
      display: none;
    }

    &.active .fav-gold {
      display: block;
    }

    &.active .fav {
      display: none;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  background: #ffffff;
  border-radius: 8px;
  margin: 24px 0;
  min-height: 400px;

  .empty-state-image {
    width: 200px;
    height: auto;
    margin-bottom: 24px;
  }

  h3 {
    font-size: 24px;
    color: #333333;
    margin-bottom: 16px;
  }

  p {
    font-size: 16px;
    color: #666666;
    margin-bottom: 24px;
    max-width: 400px;
  }

  .primary-button {
    padding: 12px 32px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 50%;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
  }
}

@media screen and (max-width: 1260px) {
  .slideItems {
    width: 48%;
  }
}

@media screen and (max-width: 992px) {
  .myAccount {
    padding: 20px 0 50px;
  }
  .sideMenu {
    width: 60px;
  }
  .contentSection .contents {
    width: calc(100% - 60px);
  }
  h1 {
    font-size: 30px;
    margin-bottom: 10px;
  }
}
@media screen and (max-width: 640px) {
  .contentSection {
    flex-direction: column;
    .contents {
      width: 100%;
      padding-left: 0;
      padding-top: 30px;
    }
  }
  .sideMenu {
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .wishlistSec {
    margin: 0;
  }
  .slideItems {
    width: 100%;
  }
  h1 {
    font-size: 25px;
  }
}
