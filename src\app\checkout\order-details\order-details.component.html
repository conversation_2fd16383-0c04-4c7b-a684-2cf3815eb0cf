<!-- <div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif" >
</div> -->

<div class="cartPage">
  <div class="container">
    <div class="topPagination">
      <div class="paging">
        <div class="numbering">
          <span class="number active">1</span>Login
        </div>
        <div class="numbering">
          <span class="number active">2</span>Delivery Details
        </div>
        <div class="numbering">
          <span class="number active">3</span>Order Details
        </div>
      </div>
    </div>
    <div class="contentSection">
      <div class="leftSide">
        <ng-container *ngFor="let product of cartData?.products">
          <div class="cartItems">
            <div class="image"><img src="{{imageBase}}/{{product?.image}}" alt=""></div>
            <div class="details">
              <div class="name">{{product?.productName}}</div>
              <div class="rate">₹{{product?.price}}</div>
              <p style="color:red; font-size: 12px; padding-top: 5px;" *ngIf="product?.outOfStock">Product is out of
                stock</p>
            </div>
            <!-- <div class="delete"><img src="\assets\images\my-account\delete.svg" alt=""></div> -->
          </div>
        </ng-container>
      </div>
      <div class="rightSide">
        <div class="box">
          <div class="promo">
            <div class="head">Promo Code</div>
            <div class="enter">
              <span class="code"><input type="text" placeholder="Enter Promo Code" [(ngModel)]="promocode"></span>
              <!-- <span class="primary-button">Apply</span> -->
            </div>
          </div>
          <div class="addSpecial">
            <div class="instruction">
              <div class="form-group">
                <input type="checkbox" id="instruction" [disabled]="isDisabled" [checked]="instruction">
                <label for="instruction">
                  <div class="add">Add Special Instruction </div>
                  <div class="type"><textarea name="instruction" id="instruction" cols="30" rows="4"
                      placeholder="Type Here" [(ngModel)]="instruction" [disabled]="isDisabled"></textarea></div>
                </label>
              </div>

            </div>
          </div>
          <div class="amounts">
            <div class="name">Subtotal</div>
            <div class="rate">₹{{cartData?.sub_total}}</div>
          </div>
          <div class="amounts">
            <div class="name">Shipping Charge</div>
            <div class="rate">₹{{cartData?.shipping_charge}}</div>
          </div>
          <div class="amounts discount" *ngFor="let item of cartData?.discount_lists">
            <div class="name">{{item?.label}}</div>
            <div class="rate">-₹{{item?.amount}}</div>
          </div>
          <div class="amounts grand">
            <div class="name">Grand Total</div>
            <div class="rate">₹{{cartData?.grand_total}}</div>
          </div>
          <div class="redeem">
            <div class="form-group">
              <input type="checkbox" id="redeem" [checked]="selectedScheme == 'cash' || selectedScheme == 'weight'"
                [disabled]="isDisabled">
              <label for="redeem">
                <div class="add">
                  <div class="text">Redeem Using Scheme Balance</div>
                  <!-- <div class="balance">Balance : ₹{{cartData?.wallet_balance}}</div> -->
                </div>
              </label>
              <div class="balance">
                <input type="radio" name="scheme" id="1" [disabled]="isDisabled" value="cash"
                [checked]="selectedScheme == 'cash'">
                <label for="1">Cash Scheme Balance: ₹{{cartData?.scheme_balance}}</label>
              </div>
              <div class="balance">
                <input type="radio" name="scheme" id="2" [disabled]="isDisabled" value="weight"
                [checked]="selectedScheme == 'weight'">
                <label for="2">Weight Scheme Balance : {{cartData?.scheme_weight_balance}} gm</label>
              </div>
            </div>
            <div class="form-group">
              <input type="checkbox" id="main" [disabled]="isDisabled" [(ngModel)]="selectedMainWallet">
              <label for="main">
                <div class="add">
                  <div class="text">Main Wallet</div>
                  <div class="balance">Balance : ₹7895.00</div>
                </div>
              </label>
            </div>
            <div class="form-group redeemReward">
              <input type="checkbox" id="reward" [(ngModel)]="selectedRewardPoints"
                [disabled]="isDisabled">
              <label for="reward">
                <div class="add">
                  <div class="text">Redeem Using Reward Points</div>
                  <div class="balance">Balance : {{cartData?.reward_points_balance}}pts</div>
                </div>
              </label>
            </div>
            <div class="primary-button golden" (click)="checkout()">Continue to Payment</div>
          </div>
        </div>
      </div>
    </div>
    <form *ngIf="!checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
      action="{{ccAvenueUrl}}?command=initiateTransaction&enc_request={{encRequest}}&access_code={{accessCode}}&request_type=XML&response_type=XML&version=1.1">
      <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
      <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
    </form>
    <form *ngIf="checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
    action="{{ccAvenueBeta}}?command=initiateTransaction">
    <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
    <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
  </form>
  </div>
</div>
