<!-- <div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif">
</div> -->

<div class="designJewellery">
  <div class="container">
    <div class="title">
      <h1>Design Your Jewellery</h1>
      <p>Enter your details below and upload any jewellery design image you like to be made exclusively for you. You
        will get the piece perfectly made and delivered at your doorstep.</p>
    </div>
    <form [formGroup]="designForm">
      <div class="form">
        <div class="leftSide">
          <img src="\assets\images\design-jewellery.svg" alt="design jewellery image" />
        </div>
        <div class="rightSide">
          <div class="field">
            <span>Name</span>
            <input type="text" formControlName="name" />
            <span *ngIf="df.name.invalid && df.name.touched">
              <span class="text-danger" *ngIf="df.name.errors?.required"> Name is required </span>
            </span>
          </div>
          <div class="field">
            <span>Email</span>
            <input type="email" formControlName="email" />
            <span *ngIf="df.email.invalid && df.email.touched">
              <span class="text-danger" *ngIf="df.email.errors?.required"> Email is required </span>
            </span>
            <span class="text-danger" *ngIf="df.email.errors?.pattern">Please Enter Valid Email </span>
          </div>
          <div class="field">
            <span>Mobile Number</span>
            <input type="tel" formControlName="mobile" />
            <span *ngIf="df.mobile.invalid && df.mobile.touched">
              <span class="text-danger" *ngIf="df.mobile.errors?.required"> Mobile Number is required </span>
            </span>
            <span class="text-danger" *ngIf="df.mobile.errors?.pattern">Please Enter Valid Mobile Number </span>
          </div>
          <div class="field">
            <span>Description</span>
            <textarea formControlName="description"></textarea>
            <span *ngIf="df.description.invalid && df.description.touched">
              <span class="text-danger" *ngIf="df.description.errors?.required"> Description is required </span>
            </span>
          </div>
          <div class="field">
            <span>Upload Design (png or jpg)*</span>
            <span class="uploading"><input type="file" formControlName="image" name="file"
                (change)="onFileChange($event)">
              <img class="preview" [src]="URL" alt="uploaded image" *ngIf="URL" />
            </span>
            <span *ngIf="df.image.invalid && df.image.touched && !image">
              <span class="text-danger" *ngIf="df.image.errors?.required"> Image is required </span>
            </span>
          </div>
          <re-captcha formControlName="recaptchaReactive" [siteKey]="recaptcha_key"></re-captcha>
          <div class="submitBtn"><input type="submit" value="Submit" (click)="submit()" /></div>
        </div>
      </div>
    </form>
  </div>
</div>
