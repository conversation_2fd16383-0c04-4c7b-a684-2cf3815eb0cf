import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';

@Component({
  selector: 'app-faq',
  templateUrl: './faq.component.html',
  styleUrls: ['./faq.component.scss']
})
export class FaqComponent implements OnInit {

  selectedIndex: any
  data: any;

  constructor(private ApiService: ApiService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.FAQ).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.data = res?.data
      }
    })
  }

  showAccordion(index: any) {
    if (this.selectedIndex == index) {
      this.selectedIndex = ''
    } else {
      this.selectedIndex = index
    }
  }

}
