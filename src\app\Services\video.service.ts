import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface VideoQuality {
  src: string;
  type: string;
  label: string;
  bandwidth: number; // in kbps
}

@Injectable({
  providedIn: 'root'
})
export class VideoService {
  private connectionSpeed = new BehaviorSubject<number>(0);
  private videoQualities: { [key: string]: VideoQuality[] } = {
    'banner-video': [
      {
        src: 'https://gziw1jnd2b.ufs.sh/f/5oCdTODIdQkJ7fFfSxkVPQk0XmJoB3wCOHLRyrdt8l6IqYnE',
        type: 'video/webm',
        label: '1080p',
        bandwidth: 5000
      },
      {
        src: 'https://gziw1jnd2b.ufs.sh/f/5oCdTODIdQkJ3RscXA2m87dPh31O9U4wpY6KNfDvGRWjScCa',
        type: 'video/webm',
        label: '720p',
        bandwidth: 2500
      },
      {
        src: 'https://gziw1jnd2b.ufs.sh/f/5oCdTODIdQkJ3KowHR2m87dPh31O9U4wpY6KNfDvGRWjScCa',
        type: 'video/webm',
        label: '480p',
        bandwidth: 1000
      }
    ]
  };

  constructor() {
    this.detectConnectionSpeed();
  }

  private async detectConnectionSpeed(): Promise<void> {
    try {
      const startTime = performance.now();
      const response = await fetch('/assets/images/placeholder.jpg?' + new Date().getTime());
      const endTime = performance.now();
      
      if (response.ok) {
        const fileSize = parseInt(response.headers.get('content-length') || '0');
        const speed = (fileSize * 8) / ((endTime - startTime) / 1000); // bits per second
        this.connectionSpeed.next(speed / 1000); // Convert to kbps
      }
    } catch (error) {
      console.error('Error detecting connection speed:', error);
    }
  }

  getOptimalVideoQuality(videoId: string): Observable<VideoQuality> {
    const qualities = this.videoQualities[videoId] || [];
    if (qualities.length === 0) {
      return new BehaviorSubject<VideoQuality>({
        src: '',
        type: 'video/webm',
        label: '480p',
        bandwidth: 1000
      }).asObservable();
    }

    return this.connectionSpeed.pipe(
      map((speed: number) => {
        // Default to lowest quality
        let bestQuality = qualities[qualities.length - 1];
        
        // Find the highest quality that our connection can handle
        for (const quality of qualities) {
          if (speed > quality.bandwidth * 1.3) { // 30% buffer
            bestQuality = quality;
          } else {
            break;
          }
        }
        
        return bestQuality;
      })
    );
  }

  preloadVideo(videoId: string): void {
    const qualities = this.videoQualities[videoId];
    if (!qualities) return;

    // Preload the first 3 seconds of each quality
    qualities.forEach(quality => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', `${quality.src}#t=0,3`, true);
      xhr.responseType = 'blob';
      xhr.send();
    });
  }
}
