import { ApiService } from 'src/app/Services/api.service';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Component, NgZone, OnInit, ViewChild } from '@angular/core';
import { NgbInputDatepickerConfig } from '@ng-bootstrap/ng-bootstrap';
import { DatepickerOptions } from 'ng2-datepicker';
import { getYear } from 'date-fns';
import locale from 'date-fns/locale/en-US';
import { apiEndPoints } from '../ApiEndPoints';
import { ToastrService } from 'ngx-toastr';
import * as moment from 'moment';
import { Router } from '@angular/router';
import { NgxSpinnerService } from 'ngx-spinner';
declare var Razorpay: any;

@Component({
  selector: 'app-gold-account',
  templateUrl: './gold-account.component.html',
  styleUrls: ['./gold-account.component.scss'],
  providers: [NgbInputDatepickerConfig],
})
export class GoldAccountComponent implements OnInit {
  dob: any;
  nominee_dob: any;
  URL: any;

  options: DatepickerOptions = {
    minYear: getYear(new Date()) - 50, // minimum available and selectable year
    maxYear: getYear(new Date()) + 20, // maximum available and selectable year
    placeholder: ' ', // placeholder in case date model is null | undefined, example: 'Please pick a date'
    format: 'LLLL do yyyy', // date format to display in input
    formatTitle: 'LLLL yyyy',
    formatDays: 'EEEEE',
    firstCalendarDay: 0, // 0 - Sunday, 1 - Monday
    locale: locale, // date-fns locale
    position: 'bottom',
    inputClass: '', // custom input CSS class to be applied
    calendarClass: 'datepicker-default', // custom datepicker calendar CSS class to be applied
    scrollBarColor: '#dfe3e9',
  };

  selectedScheme: any;
  selectedAmount: any;
  goldForm!: FormGroup;
  selectedCountry: any;
  selectedState: any;
  countries: any;
  states: any;
  userImage: any;
  proofImage: any;
  proofUrl: any;
  panUrl: any;
  panImage: any;
  plans: any;
  schemeAmounts: any;
  isAdult: any = true;
  schemeUpi: any;
  proofSideUrl: any;
  proofSideImage: any;
  PopupUpi: boolean = false;
  upiId: any;
  dobValidation: any;
  countryValidation: any;
  stateValidation: any;
  panimageValidation: any;
  proofimageValidation: any;
  userimageValidation: any;
  fileExtArray = ['jpg', 'jpeg', 'JPEG', 'JPG', 'PNG', 'png', 'webp', 'WEBP'];
  isUnique: boolean = true;
  first: boolean = true;
  second: boolean = false;
  third: boolean = false;
  @ViewChild('myInput') myInput: any;
  upiIntentUrl: any;
  private statusCheckInterval: any;
  windowRef = window;
  private rzp: any;
  isProcessingPayment: boolean = false;

  constructor(
    private formbuilder: FormBuilder,
    private ApiService: ApiService,
    private toast: ToastrService,
    private ngZone: NgZone,
    private router: Router,
    private spinner: NgxSpinnerService
  ) { }

  ngOnInit(): void {
    this.initGoldForm();
    this.getCountries();
    this.getSchemePlans();
  }

  initGoldForm() {
    this.goldForm = this.formbuilder.group({
      name: ['', [Validators.required]],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      pan: [
        '',
        [
          Validators.required,
          Validators.pattern(/^([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}?$/),
        ],
      ],
      district: ['', [Validators.required]],
      city: ['', [Validators.required]],
      gender: [''],
      code: ['91'],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      aadhar: ['', [Validators.required, Validators.pattern(/^\d{12}$/)]],
      house_name: ['', [Validators.required]],
      street: ['', [Validators.required]],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      nominee_name: ['', [Validators.required]],
      nominee_aadhar: [
        '',
        [Validators.required, Validators.pattern(/^\d{12}$/)],
      ],
      nominee_relatiom: ['', [Validators.required]],
      proof: ['', [Validators.required]],
      guardian_name: ['', [Validators.required]],
      guardian_email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      guardian_pan: [
        '',
        [
          Validators.required,
          Validators.pattern(/^([a-zA-Z]){5}([0-9]){4}([a-zA-Z]){1}?$/),
        ],
      ],
      guardian_house_name: ['', [Validators.required]],
      guardian_city: ['', [Validators.required]],
      guardian_street: ['', [Validators.required]],
      guardian_code: ['91'],
      guardian_mobile: [
        '',
        [Validators.required, Validators.pattern('^[0-9]*$')],
      ],
      guardian_relation: ['', [Validators.required]],
      guardian_aadhar: [
        '',
        [Validators.required, Validators.pattern(/^\d{12}$/)],
      ],
      guardian_district: ['', [Validators.required]],
      guardian_pincode: [
        '',
        [Validators.required, Validators.pattern(/^\d{6}$/)],
      ],
    });
  }

  get gf() {
    return this.goldForm.controls;
  }

  getCountries() {
    this.ApiService.getData(apiEndPoints.COUNTRY).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.countries = res?.Data;
      }
    });
  }

  selectCountry() {
    const countryId = this.selectedCountry;
    this.ApiService.postData(apiEndPoints.STATE, {
      countryId: countryId,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.states = res?.Data;
        this.goldForm?.get('state')?.enable();
      }
    });
  }

  getSchemePlans() {
    this.ApiService.getData(apiEndPoints.SCHEME_PLANS).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.plans = res?.Data;
      }
    });
  }

  getAmounts() {
    if (this.selectedScheme) {
      this.ApiService.postData(apiEndPoints.SCHEME_AMOUNTS, {
        plan_id: this.selectedScheme,
      }).subscribe((res: any) => {
        if (res.ErrorCode == 0) {
          this.schemeAmounts = res?.Data;
        }
      });
    }
  }

  nextOne() {
    if (this.selectedAmount && this.selectedScheme) {
      //Webengage Start
      let schemeName = this.plans.find(
        (item: any) => item.id == this.selectedScheme
      )?.name;
      this.windowRef.webengage.track('Open New Mi Gold Account', {
        scheme: schemeName,
        'installment amount': Number(this.selectedAmount),
      });
      //Webengage End

      this.first = false;
      this.second = true;
      window.scroll({
        top: 0,
        left: 0,
        behavior: 'smooth',
      });
    } else {
      this.toast.error('Please select a scheme and amount');
    }
  }

  nextTwo() {
    if (this.isAdult) {
      if (!this.dob) {
        this.dobValidation = 'Date of birth is required';
      }

      if (!this.selectedCountry) {
        this.countryValidation = 'Country is required';
      }

      if (!this.selectedState) {
        this.stateValidation = 'State is required';
      }

      let requiredFields = [
        'name',
        'aadhar',
        'email',
        'mobile',
        'pan',
        'pincode',
        'nominee_name',
        'nominee_relatiom',
        'nominee_aadhar',
        'district',
        'city',
        'house_name',
        'street',
      ];

      let isFormComplete = requiredFields.every(
        (field) => !!this.goldForm.get(field)?.value
      );

      if (
        isFormComplete &&
        this.dob &&
        this.selectedCountry &&
        this.selectedState
      ) {
        if (this.isUnique) {
          //Webengage Start
          let countryName = this.countries.find(
            (item: any) => item.id == this.selectedCountry
          )?.name;
          let stateName = this.states.find(
            (item: any) => item.id == this.selectedState
          )?.name;
          this.windowRef.webengage.track('Personal Details', {
            Name: this.goldForm.get('name')?.value || '',
            'Phone Number': String(this.goldForm.get('mobile')?.value) || '',
            'Email Id': this.goldForm.get('email')?.value || '',
            PAN: this.goldForm.get('pan')?.value ? true : false,
            Country: countryName || '',
            State: stateName || '',
            'House Name/Flat No': this.goldForm.get('house_name')?.value || '',
            District: this.goldForm.get('district')?.value || '',
            'Street/Location': this.goldForm.get('street')?.value || '',
            'Aadhar number': this.goldForm.get('aadhar')?.value ? true : false,
            City: this.goldForm.get('city')?.value || '',
            Pincode: String(this.goldForm.get('pincode')?.value) || '',
            'Nominee Name': this.goldForm.get('nominee_name')?.value || '',
            'Nominee Relationship':
              this.goldForm.get('nominee_relatiom')?.value || '',
            'Nominee DOB': this.goldForm.get('nominee_dob')?.value || '',
            'Nominee Aadhar': this.goldForm.get('nominee_aadhar')?.value
              ? true
              : false,
          });
          //Webengage End

          this.second = false;
          this.third = true;

          window.scroll({
            top: 0,
            left: 0,
            behavior: 'smooth',
          });
        } else this.toast.error('aadhar number must be unique');
      } else {
        requiredFields.forEach((field) => {
          if (!this.goldForm.get(field)?.value) {
            this.goldForm.get(field)?.markAsTouched();
          }
        });
      }
    } else {
      if (!this.dob) {
        this.dobValidation = 'Date of birth is required';
      }

      if (!this.selectedCountry) {
        this.countryValidation = 'Country is required';
      }

      if (!this.selectedState) {
        this.stateValidation = 'State is required';
      }

      let requiredFields = [
        'name',
        'aadhar',
        'guardian_email',
        'guardian_mobile',
        'guardian_pan',
        'guardian_pincode',
        'guardian_name',
        'guardian_relation',
        'guardian_aadhar',
        'guardian_district',
        'guardian_city',
        'guardian_house_name',
        'guardian_street',
      ];

      let isFormComplete = requiredFields.every(
        (field) => !!this.goldForm.get(field)?.value
      );

      if (
        isFormComplete &&
        this.dob &&
        this.selectedCountry &&
        this.selectedState
      ) {
        if (this.isUnique) {
          this.second = false;
          this.third = true;

          window.scroll({
            top: 0,
            left: 0,
            behavior: 'smooth',
          });
        } else this.toast.error('aadhar number must be unique');
      } else {
        requiredFields.forEach((field) => {
          if (!this.goldForm.get(field)?.value) {
            this.goldForm.get(field)?.markAsTouched();
          }
        });
      }
    }
  }

  checkDob() {
    let date: any = new Date(this.dob);
    let today: any = new Date();

    let ageInMilliseconds = today - date;
    let ageInYears = ageInMilliseconds / (1000 * 60 * 60 * 24 * 365.25); // Account for leap years

    if (ageInYears >= 18) {
      this.isAdult = true;
    } else {
      this.isAdult = false;
    }
  }

  onFileChange(event: any) {
    if (event.target.files.length > 0) {
      let imageName = event.target.files[0].name;
      let fileExtensionName = imageName.split('.').pop();
      if (this.fileExtArray.includes(fileExtensionName)) {
        const reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onloadend = (e: any) => {
          this.URL = e.target['result'];
        };
        this.userImage = event.target.files[0];
      } else {
        this.toast.error(
          fileExtensionName.toUpperCase() +
          ' not supported. Please upload an image file'
        );
      }
    }
  }

  onProofChange(event: any) {
    if (event.target.files.length > 0) {
      let imageName = event.target.files[0].name;
      let fileExtensionName = imageName.split('.').pop();
      if (this.fileExtArray.includes(fileExtensionName)) {
        const reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onloadend = (e: any) => {
          this.proofUrl = e.target['result'];
        };
        this.proofImage = event.target.files[0];
      } else {
        this.toast.error(
          fileExtensionName.toUpperCase() +
          ' not supported. Please upload an image file'
        );
      }
    }
  }

  onProofSideChange(event: any) {
    if (event.target.files.length > 0) {
      let imageName = event.target.files[0].name;
      let fileExtensionName = imageName.split('.').pop();
      if (this.fileExtArray.includes(fileExtensionName)) {
        const reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onloadend = (e: any) => {
          this.proofSideUrl = e.target['result'];
        };
        this.proofSideImage = event.target.files[0];
      } else {
        this.toast.error(
          fileExtensionName.toUpperCase() +
          ' not supported. Please upload an image file'
        );
      }
    }
  }

  onPanChange(event: any) {
    if (event.target.files.length > 0) {
      let imageName = event.target.files[0].name;
      let fileExtensionName = imageName.split('.').pop();
      if (this.fileExtArray.includes(fileExtensionName)) {
        const reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        reader.onloadend = (e: any) => {
          this.panUrl = e.target['result'];
        };
        this.panImage = event.target.files[0];
      } else {
        this.toast.error(
          fileExtensionName.toUpperCase() +
          ' not supported. Please upload an image file'
        );
      }
    }
  }

  popUpClose() {
    this.PopupUpi = !this.PopupUpi;
  }

  upiVerify() {
    if (!this.upiId) {
      this.toast.error('Enter upi id');
      return;
    }
    this.ApiService.postData(apiEndPoints.verify_vpa, {
      vpa: this.upiId,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.toast.success(res?.Message);
        this.submitForm();
      } else {
        this.toast.error(res?.Message);
      }
    });
  }

  submitForm() {
    let converted_dob = moment(this.dob).format('YYYY-MM-DD');
    let converted_nominee_dob = moment(this.nominee_dob).format('YYYY-MM-DD');

    const formData = new FormData();
    formData.append('name', this.goldForm.get('name')?.value);
    formData.append('email', this.goldForm.get('email')?.value);
    formData.append('pan_no', this.goldForm.get('pan')?.value.toUpperCase());
    formData.append('address_4', this.goldForm.get('district')?.value);
    formData.append('address_3', this.goldForm.get('city')?.value);
    formData.append('gender', this.goldForm.get('gender')?.value);
    formData.append('country_code', this.goldForm.get('code')?.value);
    formData.append('mobile', this.goldForm.get('mobile')?.value);
    formData.append('adhaar_no', this.goldForm.get('aadhar')?.value);
    formData.append('address_1', this.goldForm.get('house_name')?.value);
    formData.append('address_2', this.goldForm.get('street')?.value);
    formData.append('pincode', this.goldForm.get('pincode')?.value);
    formData.append('nominee', this.goldForm.get('nominee_name')?.value);
    formData.append(
      'nominee_adhar',
      this.goldForm.get('nominee_aadhar')?.value
    );
    formData.append(
      'nominee_relation',
      this.goldForm.get('nominee_relatiom')?.value
    );
    formData.append('idcard_type', this.goldForm.get('proof')?.value);
    formData.append('pancard_image', this.panImage);
    formData.append('idcard_image', this.proofImage);
    if (this.proofSideImage)
      formData.append('idcard_back', this.proofSideImage);
    formData.append('image', this.userImage);
    formData.append('country_id', this.selectedCountry);
    formData.append('state_id', this.selectedState);
    formData.append('plan_id', this.selectedScheme);
    formData.append('monthly_amount', this.selectedAmount);
    formData.append('dob', converted_dob);
    formData.append('nominee_dob', converted_nominee_dob);
    formData.append('is_adult', this.isAdult);
    // formData.append('payer_vpa', this.upiId)

    if (!this.isAdult) {
      formData.append('mobile', this.goldForm.get('guardian_mobile')?.value);
      formData.append('email', this.goldForm.get('guardian_email')?.value);
      formData.append(
        'address_1',
        this.goldForm.get('guardian_house_name')?.value
      );
      formData.append('address_2', this.goldForm.get('guardian_street')?.value);
      formData.append('address_3', this.goldForm.get('guardian_city')?.value);
      formData.append(
        'address_4',
        this.goldForm.get('guardian_district')?.value
      );
      formData.append('pincode', this.goldForm.get('guardian_pincode')?.value);
      formData.append(
        'pan_no',
        this.goldForm.get('guardian_pan')?.value.toUpperCase()
      );
      formData.append('nominee', this.goldForm.get('guardian_name')?.value);
      formData.append(
        'nominee_adhar',
        this.goldForm.get('guardian_aadhar')?.value
      );
      formData.append(
        'nominee_relation',
        this.goldForm.get('guardian_relation')?.value
      );
      formData.append(
        'country_code',
        this.goldForm.get('guardian_code')?.value
      );
    }

    //Razorpay Payment Gateway
    this.ApiService.postData(apiEndPoints.saveShemeRzp, formData).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          const paymentDetails = res?.Data?.data;

          const options = {
            key: paymentDetails.api_key,
            order_id: paymentDetails.razorpay_order_id,
            amount: paymentDetails.amount, // amount in paise
            currency: 'INR',
            name: 'TT Devassy',
            description: 'Gold Scheme',
            handler: (response: any) => {
              // Handle success    
              this.handlePaymentSuccess(paymentDetails, response);          
            },
            prefill: {
              name: this.goldForm.get('name')?.value,
              email: this.goldForm.get('email')?.value,
              contact: this.goldForm.get('mobile')?.value,
            },
            // notes: {
            //   address: this.billingForm.get('address')?.value,
            // },
            theme: {
              color: '#F37254',
            },
            modal: {
              ondismiss: () => {
                this.ngZone.run(() => {
                  this.handlePaymentFailure();
                });
              },
            },
          };

          this.rzp = new Razorpay(options);
          this.rzp.on('payment.failed', (response: any) => {
            this.ngZone.run(() => {
              this.handlePaymentFailure();
            });
          });
          this.rzp.open();
        }
      }
    );

    // if (this.upiId) {
    // this.ApiService.postData(apiEndPoints.SAVE_SCHEME, formData).subscribe((res: any) => {
    // this.ApiService.postData(apiEndPoints.newSaveScheme, formData).subscribe(
    //   (res: any) => {
    //     if (res?.ErrorCode == 0) {
    //       try {
    //         const paymentDetails = JSON.parse(res.Data.payment_details);
    //         this.upiIntentUrl = paymentDetails.Response.Body.intentUrl;
    //         this.upiModal();
    //       } catch (error) {
    //       }

    //       this.schemeUpi = res?.Data;

    //       // Constants for timing
    //       const INTERVAL_TIME = 10000; // 10 seconds
    //       const MAX_DURATION = 300000; // 5 minutes in milliseconds
    //       const startTime = Date.now();

    //       // Start interval for checking UPI status
    //       let intervalId = setInterval(() => {
    //         // Check if 5 minutes have passed
    //         if (Date.now() - startTime >= MAX_DURATION) {
    //           clearInterval(intervalId);
    //           // this.spinner.hide();
    //           this.toast.error(
    //             'Payment verification timeout. Please check your payment status in My Gold Schemes.'
    //           );
    //           this.popUpClose();
    //           return;
    //         }

    //         this.ApiService.postData(apiEndPoints.check_upi_status, {
    //           order_id: this.schemeUpi?.order_id,
    //         }).subscribe({
    //           next: (res: any) => {
    //             if (res?.ErrorCode == 0) {
    //               if (res?.Data?.transaction_status == 'success') {
    //                 clearInterval(intervalId);
    //                 // this.spinner.hide();
    //                 this.toast.success(res?.Message);
    //                 this.router.navigate(['/my-gold-schemes']);
    //               } else if (res?.Data?.transaction_status == 'failed') {
    //                 clearInterval(intervalId);
    //                 // this.spinner.hide();
    //                 this.toast.error(res?.Message);
    //               }
    //               // If status is pending or any other status, continue checking
    //             }
    //           },
    //           error: (error) => {
    //             // Don't clear interval on error, continue checking
    //           },
    //         });
    //       }, INTERVAL_TIME);

    //       // Store interval ID to clear it when component is destroyed
    //       this.statusCheckInterval = intervalId;
    //     } else {
    //       this.toast.error(res?.Message);
    //     }
    //   }
    // );
    // } else this.toast.error("Enter UPI Id")
  }

  upiModal() {
    // if (!this.userImage) {
    //   this.userimageValidation = "User Image is Required"
    // }
    // if (!this.proofImage) {
    //   this.proofimageValidation = "Proof Image front side is Required"
    // }
    // if (!this.panImage) {
    //   this.panimageValidation = "Pan card Image is Required"
    // }

    // if (this.userImage && this.proofImage && this.panImage && this.goldForm.get('proof')?.value) {
    this.PopupUpi = !this.PopupUpi;
    // }
    // else {
    //   this.goldForm.get('proof')?.markAsTouched()
    // }
  }

  aadharValidation(e: any) {
    let aadhar = this.goldForm.get('aadhar')?.value;
    if (aadhar) {
      if (aadhar == e.value) {
        this.isUnique = false;
        this.toast.error('aadhar number must be unique');
      } else this.isUnique = true;
    }
  }

  firstSection() {
    this.second = false;
    this.third = false;
    this.first = true;
  }

  secondSection() {
    if (this.selectedAmount && this.selectedScheme) {
      if (this.third) this.third = false;
      if (this.first) this.first = false;
      this.second = true;
    }
  }

  onInputChange(event: any) {
    const inputValue = event.target.value.toUpperCase();
    this.myInput.nativeElement.value = inputValue;
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.key === 'e') {
      event.preventDefault();
    }
  }

  imageValidations() {
    if (!this.userImage) {
      this.userimageValidation = 'User Image is Required';
    }
    if (!this.proofImage) {
      this.proofimageValidation = 'Proof Image front side is Required';
    }
    if (!this.panImage) {
      this.panimageValidation = 'Pan card Image is Required';
    }

    if (
      this.userImage &&
      this.proofImage &&
      this.panImage &&
      this.goldForm.get('proof')?.value
    ) {
      //Webengage Start
      this.windowRef.webengage.track('Upload Document', {
        'Files Uploaded': true,
      });
      //Webengage End

      this.submitForm();
    } else {
      this.goldForm.get('proof')?.markAsTouched();
    }
  }

  ngOnDestroy() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval);
    }
  }

  private handlePaymentFailure() {
    // Close the Razorpay modal if it's open
    if (this.rzp) {
      this.rzp.close();
      this.isProcessingPayment = false;
      this.spinner.hide();
    }
  }

  handlePaymentSuccess(paymentDetails: any, response: any) {
    this.isProcessingPayment = true;
    this.spinner.show();

    this.ApiService.postData(apiEndPoints.goldSchemeSuccess, {
      order_number: paymentDetails.order_number,
      ...response,
    }).subscribe({
      next: (res: any) => {
        this.isProcessingPayment = false;
        this.spinner.hide();
        
        if (res?.ErrorCode == 0) {
          this.toast.success("Payment Success");
          this.router.navigate(['/my-gold-schemes']);
        } else {
          this.toast.error("Payment Failed");
          this.router.navigate(['/gold-account']);
        }
      },
      error: (error) => {
        this.isProcessingPayment = false;
        this.spinner.hide();
        this.toast.error("Payment verification failed");
        this.router.navigate(['/gold-account']);
      }
    });
  }

}
