.top {
    margin-bottom: 30px;
    img {
        width: 100%;
        height: 100%;
    }
}


.blogbanner{
    margin-bottom: 90px;
    overflow: hidden;
    swiper.swiper.swiper-initialized {
        max-width: 70%;
        overflow: visible;
    }
    .productImage img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .productImage {
        display: block;
        height: 100%;
    }

    .slideItems {
        position: relative;
        margin: 0 20px;
    }
    h2{
        color: #fff;
        margin-bottom: 30px;
    }
    h1{
        color: #fff;
        margin-bottom: 30px;
        font-size: 44px;
    }

    .primary-button{
        background-color: #fff;
        color: #D2AB66;
        border-color: #fff;
        width: 240px;
    }
    .head {
        position: absolute;
        left: 50%;
        top: 60px;
        opacity: 0;
        transform: translateX(-50%);
        color: #fff;
        font-size: 16px;
        border-bottom: 1px solid #D2AB66;
        padding-bottom: 10px;
        span{
          padding-right: 9px;
          position: relative;
          &::after{
            content: ",";
            position: absolute;
            right: 5px;
          }
          &:last-child::after{
            content: "";
          }
        }
    }
    .content {
        position: absolute;
        left: 50%;
        top: 40%;
        opacity: 0;
        transform: translate(-50%, -50%);
        text-align: center;
        color: #fff;
    }

}

.blogContent{
        display: flex;
    .leftSide {

        width: calc(100% - 330px);
        padding-right: 40px;


        .topSection{
            color: #000;
        }
        .topImage {
            position: relative;
            margin-bottom: 30px;
        }

        .blogImage {
            width: 100%;
            object-position: top;
        }

        .icon {
            position: absolute;
            left: 0;
            top: 30px;
        }

        .subHead {
            font-size: 16px;
            border-bottom: 1px solid #D2AB66;
            padding-bottom: 10px;
            display: inline-block;
            margin-bottom: 30px;
            padding-right: 9px;
            position: relative;
            & + .subHead{
              &::after{
                content: ",";
                position: absolute;
                left: -9px;
              }
              &:last-child::after{
                content: "";
              }

            }
        }
        h6 {
            font-size: 20px;
            text-transform: capitalize;
            margin-bottom: 20px;
        }

        .bottom {
            margin: 25px 0 0px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            color: #787878;
        }

        .comment {
            display: flex;
            align-items: center;
        }

        .comment span {
            padding-left: 7px;
        }

        .boxSection {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            margin-top: 50px;

            .box {
                width: 50%;
                border-top: 1px solid rgba(0, 0, 0, 0.14);
                padding-top: 50px;
                padding-bottom: 50px;
                padding-left: 15px;
                padding-right: 15px;
                color: #000;
                .blogImage {
                    height: 215px;
                    object-fit: cover;
                }
            }

        }



    }
    .rightSide{
        width: 330px;

        .search {
            position: relative;
            width: 100%;

            .searching {
                position: absolute;
                right: 6px;
                top: 18px;
                font-size: 12px;
                color: #1D1D1D;
                cursor: pointer;
            }

            input[type="text"] {
                font-size: 12px;
                padding-left: 0;
                margin-bottom: 40px;
                color: #BBBBBB;
            }
        }
        .recent {
            padding-bottom: 45px;
            h6 {
                font-size: 20px;
                margin-bottom: 20px;
            }

            .post {margin-bottom: 15px;}

            .postname {
                font-size: 16px;
            }

            .date {
                font-size: 14px;
                color: #787878;
            }
        }
        .category {

                h6 {
                    font-size: 20px;
                    margin-bottom: 20px;
                }

                .cate {
                    display: flex;
                    justify-content: space-between;
                    font-size: 16px;
                    margin-bottom: 15px;
                }
        }

    }
}

@media screen and (max-width:992px) {

    .blogbanner {
      margin-bottom: 50px;
      .slideItems {
        height: 380px;
        margin: 0 5px;
      }

      .head {
        font-size: 14px;
      }

      .content {
        width: 80%;
      }

    }
    .blogContent {
      .leftSide {
        width: 100%;
        padding-right: 0;
      }

      .rightSide {
          display: none;
      }

    }

  }

  @media screen and (max-width:640px) {

      .blogbanner {
          h2{
              margin-bottom: 20px;
          }
          h1{
              margin-bottom: 20px;
              font-size: 25px;
          }
          .primary-button {
              width: 160px;
          }

          .slideItems {
              height: 300px;
          }

           .head {
              top: 10px;
              font-size: 12px;
              width: max-content;
          }

      }
      .blogContent .leftSide .boxSection .box {
        width: 100%;
        padding: 40px 0;
      }
  }
