.designJewellery {
    padding: 40px 0 100px;

    h1 {
      font-size: 44px;
      margin-bottom: 25px;
    }

    .form {
      display: flex;
      margin: 0 auto;
      width: 75%;
      justify-content: space-between;
      max-height: 820px;
    padding: 30px 40px;
        border: 1px solid #BBBBBB;

      .leftSide {
        width: 49%;
      }

      .rightSide {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 49%;
      }
        input{
            margin-bottom: 20px;
        }
        textarea{margin-bottom: 16px;}
      input,
      select,
      textarea {
        border: 1px solid #BBBBBB;
      }

      select {
        -webkit-appearance: none;
        -moz-appearance: none;
        background-image: url("/assets/images/angle-down.svg");
        background-repeat: no-repeat;
        background-position: 98%;

      }

      .field span {
        font-size: 14px;
        color: #787878;
        font-weight: 500;
        margin-bottom: 10px;
      }
      .field.time .text-danger {
        bottom: 13px;
      }
      .filtername{
        padding: 12px 20px;
        border: 1px solid #BBBBBB;
        margin-right: 15px;
        font-size: 14px;
        cursor: pointer;
        width: 100%;
        height: 49px;
        position: relative;
        margin-bottom: 20px;
      }

      .field.time {
        .timePicker {
          position: relative;
          &::after {
            content: "";
            position: absolute;
            width: 24px;
            height: 24px;
            z-index: -1;
            right: 10px;
            top: 10px;
            background-image: url(/assets/images/clock.svg);
            background-repeat: no-repeat;
          }
        }

        input {
          cursor: pointer;
          display: block;
          width: 100%;
          padding: 12px 15px 14px 12px;
          background: transparent;
          font-size: 12px;
          &:focus-visible {
            outline: none;
          }
        }
      }

      .leftSide img {
        height: 100%;
        width: 100%;
        object-fit: cover;
      }
    }
  }

  .smallBanner {
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .uploading {
    width: 100%;
    height: 120px;
    border: 1px solid #D5DBDE;
    position: relative;
    margin-bottom: 30px;
    &::before{
        content: "";
        background-image: url(/assets/images/gold-scheme/upload.svg);
        width: 35px;
        height: 34px;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%,-50%);
        background-repeat: no-repeat;
        background-size: contain;
    }
    input[type="file"] {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 0;
        top: 0;
        outline: none;
        opacity: 0;
    }
}

.calander   .ng-untouched{
    display: block;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 24px;
      height: 24px;
      z-index: -1;
      right: 10px;
      top: 10px;
      background-image: url(/assets/images/calander.svg);
      background-repeat: no-repeat;
    }
  }

  .datepicker-container.datepicker-default {
    width: 100%;
  }


  .bottomSection {
    display: flex;
    margin: 0 -1% 100px;
    justify-content: space-between;

    .box {
        width: 33.33%;
        text-align: center;
        border: 1px solid #BBBBBB;
        margin: 0 1%;
        padding: 30px;
        font-size: 14px;
    }

    .image {
        margin-bottom: 30px;
    }
}



@media screen and (max-width: 992px) {
  .designJewellery {
    padding: 30px 0 50px;

    h1 {
      font-size: 30px;
      margin-bottom: 10px;
    }

    .form {
      width: 90%;
      padding: 30px;
      flex-wrap: wrap;
      max-height: unset;

      .leftSide {
        width: 100%;
      }

      .rightSide {
        width: 100%;
      }
    }

  }
}


@media screen and (max-width: 640px) {
  .bottomSection {
    flex-wrap: wrap;
    margin: 0 0 40px;
    .box {
      width: 100%;
      margin: 10px 0;
    }
  }

}

@media screen and (max-width: 480px) {
  .designJewellery {
    h1 {
      font-size: 25px;
    }
  }
}
