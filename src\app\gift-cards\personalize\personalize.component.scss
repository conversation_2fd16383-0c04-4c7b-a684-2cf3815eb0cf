.myAccount {
    padding: 40px 0 100px;
}
.sideMenu {
    width: 270px;
}
.contentSection {
    display: flex;
    .contents {
        width: calc(100% - 270px);
        padding-left: 50px;
    }
}

.field .text-danger{
  bottom: -3px;
}
.field .billCountry{
  position: relative;
  .text-danger {
    bottom: -17px;
  }
}

.head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    span{font-weight: 600;}
    .add {
        width: auto;
    }
    .add {
        font-size: 14px;
        font-weight: 600;
        position: relative;
        text-align: right;
        cursor: pointer;
        &::before {
            content: "+";
            position: absolute;
            left: -25px;
            top: 1px;
            border: 1px solid #000;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            text-align: end;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

}

.giftSection {
    padding: 30px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    display: flex;
    flex-wrap: wrap;
    padding: 30px;
}

.leftSection {
    width: 50%;
    background-image: url(/assets/images/gift-cards/Frame.svg);
    background-size: cover;
    background-color: #D2AB66;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 40px;
    font-size: 36px;
    font-weight: 600;
    color: #fff;
    // .name {
    //     width: 164px;
    // }

}

.rightSection {
    width: 50%;
    padding-left: 30px;
    font-size: 14px;
    input, textarea{
        border: 1px solid #D5DBDE;
        margin-top: 10px;
    }
    .button {
        display: flex;
        margin: 0 -9px;
        .primary-button {
            margin: 0 9px;
        }
    }

}


.registerPopup {
    position: fixed;
    width: 100%;
    height: 100%;
    background: #00000040;
    top: 0;
    left: 0;
    z-index: 99;

    .box {
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      text-align: center;
      max-width: 450px;
      margin: 0 auto;
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
      position: fixed;
      top: 50%;
      left: 50%;
      z-index: 9;
      background: #fff;
      transform: translate(-50%, -50%);
      overflow: hidden;
    }

    &.show {
      display: flex !important;
    }

    &.hide {
      display: none;
    }

      p {
        text-align: center;
        font-size: 14px;
        margin-bottom: 15px;
      }

    .top {
        position: relative;
        padding: 30px;
        background-image: url(/assets/images/gift-cards/Frame.svg);
        background-color: #D2AB66;
        .rate {
            position: absolute;
            left: 30px;
            top: 30px;
            font-size: 20px;
            color: #fff;
        }

        .close {
            position: absolute;
            right: 15px;
            top: 15px;
            cursor: pointer;
            opacity: unset;
            border: 1px solid #fff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            img{
                width: 8px;
                height: 8px;
            }
        }
        .giftfor {
            padding-top: 30px;
            .name {
                font-size: 24px;
                padding-top: 10px;
                color: #fff;
                font-weight: 600;
            }
        }

    }
    .bottom {
        padding: 30px;
            font-size: 14px;
        .from {
            font-size: 16px;
        }

        .name {
            font-size: 20px;
            font-weight: 600;
            padding-bottom: 10px;
        }
        .code {
            font-weight: 600;
            padding-top: 5px;
        }
    }

  }

.editPersonalInfo {
    width: 100%;

        .edit{
            position: absolute;
            right: 10px;
            top: 10px;
            display: flex;
            font-size: 14px;
            font-weight: 600;
            padding: 10px;
            cursor: pointer;
        }
        .head {
          font-weight: 600;
          padding-bottom: 20px;
          position: relative;
        }
        .pInfo {
            display: flex;

            .field {
            width: 290px;
            margin-bottom: 30px;
            font-size: 16px;
            }

            .text {
            font-weight: 600;
            padding-bottom: 5px;
            }
        }
        .addNew {
            display: flex;
            position: absolute;
            right: 0;
            top: 0;
            font-size: 14px;
            cursor: pointer;
        }
        .address {
            .name {
                font-size: 16px;
            }

            div {
                font-size: 14px;
                padding-bottom: 6px;
            }
        }
        .delete{
            position: absolute;
            right: 10px;
            bottom: 10px;
            padding: 10px;
            cursor: pointer;
        }


    .form {
        .twoSection{
            display: flex;
            justify-content: space-between;
            .field {
                width: 49%;
            }
        }
        .threeSection {
            display: flex;
            justify-content: space-between;
            .field {
                width: 32%;
            }
        }


        input,
        select,
        textarea {
          border: 1px solid #BBBBBB;
        }

        select {
          -webkit-appearance: none;
          -moz-appearance: none;
          background-image: url("/assets/images/angle-down.svg");
          background-repeat: no-repeat;
          background-position: 98%;

        }

        // .field span {
        //   font-size: 14px;
        //   color: #787878;
        //   font-weight: 500;
        //   margin-bottom: 10px;
        //   text-transform: capitalize;
        // }
        .field .fieldName{font-size: 14px;}
        .field.time {
          .timePicker {
            position: relative;
            &::after {
              content: "";
              position: absolute;
              width: 24px;
              height: 24px;
              z-index: -1;
              right: 10px;
              top: 10px;
              background-image: url(/assets/images/clock.svg);
              background-repeat: no-repeat;
            }
          }


          input {
            cursor: pointer;
            display: block;
            width: 100%;
            padding: 10px;
            background: transparent;
            font-size: 12px;
            &:focus-visible {
              outline: none;
            }
          }
        }
        .field.gender {
            display: flex;
            align-items: center;
            span {
                padding-right: 50px;
                margin-bottom: 0;
            }

            .radioBtn {
                padding-bottom: 0;
            }

        }

        .leftSide img {
          height: 100%;
          width: 100%;
          object-fit: cover;
        }
      }
    .buttons {
        display: flex;
        justify-content: flex-end;
        .primary-button {
            width: 220px;
            margin-left: 20px;
        }
    }

    .filtername {
        padding: 12px 20px;
        border: 1px solid #D5DBDE;
        margin-right: 15px;
        font-size: 14px;
        cursor: pointer;
        width: 100%;
        height: 49px;
        position: relative;
        margin-bottom: 20px;

        img {
          padding-right: 10px;
        }
      }

      .drop {
        display: none;
      }

    .selectOption {

      span {
        padding-right: 5px;
      }

      .selectMenu {
        font-size: 14px;
        color: #000;
        position: relative;
        padding-right: 25px;

        &.sort {
          &::before {
            content: "";
            background-image: url(/assets/images/angle-down.svg);
            position: absolute;
            right: 0px;
            top: 3px;
            width: 20px;
            height: 20px;
          }
        }
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        position: absolute;
        opacity: 0;
        box-shadow: 0px 2px 2px #D5DBDE;
        background: #fff;
        width: 100%;
        left: 0px;
        top: 49px;
        z-index: -1;

        &.active {
          z-index: 3;
          opacity: 1;
          transition: all linear .2s;
          z-index: 1;
        }

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: -12px;
          width: 100%;
          height: 20px;

        }

        li {
          padding: 0px 15px;

          &:hover {
            color: #D2AB66;
          }
        }
      }

    }


  }
  .buttons {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    .primary-button {
        width: 220px;
    }
}

h1 {
  font-size: 44px;
  margin-bottom: 25px;

  @media screen and (max-width:992px) {
      margin-bottom: 10px;
      font-size: 30px;
  }

  @media screen and (max-width:480px) {
      font-size: 25px;
  }
}


@media screen and (max-width: 1200px) {
  .leftSection{
    font-size: 25px;
  }
}

@media screen and (max-width: 992px) {
  .myAccount {
    padding: 20px 0 50px;
  }
  .sideMenu {
      width: 60px;
  }
  .contentSection .contents {
      width: calc(100% - 60px);
  }
}





@media screen and (max-width: 640px) {
  .contentSection {
    flex-direction: column;
    .contents {
      width: 100%;
      padding-left: 0;
      padding-top: 30px;
      .pInfo .field{
        font-size: 14px;
        margin-bottom: 15px;
      }
    }
    .pInfo {
      flex-direction: column;
    }

  }
  .sideMenu {
      width: 100%;
  }
  .editPersonalInfo .form .twoSection {
    flex-direction: column;
    .field {
      width: 100%;
    }
  }
  .leftSection, .rightSection{
    width: 100%;
    .image{
      margin-bottom: 15px;
    }
  }
  .rightSection{margin-top: 20px;padding-left: 0;}
}
