import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';
import { environment } from 'src/environments/environment.prod';

@Component({
  selector: 'app-about',
  templateUrl: './about.component.html',
  styleUrls: ['./about.component.scss']
})
export class AboutComponent implements OnInit {
  aboutus: any;
  imageBase = environment.imageBase

  constructor(private ApiService: ApiService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.aboutus_page).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.aboutus = res?.Data
      }
    })
  }

}
