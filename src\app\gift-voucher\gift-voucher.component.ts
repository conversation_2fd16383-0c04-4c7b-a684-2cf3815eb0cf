import { Component, OnInit } from '@angular/core';
import { ApiService } from '../Services/api.service';
import { apiEndPoints } from '../ApiEndPoints';
import { giftVoucher } from '../shared/models/gift-voucher.model';
import { environment } from 'src/environments/environment.prod';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';

@Component({
  selector: 'app-gift-voucher',
  templateUrl: './gift-voucher.component.html',
  styleUrls: ['./gift-voucher.component.scss'],
})
export class GiftVoucherComponent implements OnInit {
  giftVoucherDetails!: giftVoucher;
  firstBanner!: string;
  secondBanner!: string;
  imageBaseUrl: string = environment.imageBase;
  constructor(
    private apiService: ApiService,
    private toast: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getGiftCardDetails();
  }

  getGiftCardDetails() {
    this.apiService.getGiftCardDetails(apiEndPoints.gift_card_page).subscribe(
      (res) => {
        if (res.ErrorCode === 0) {
          this.giftVoucherDetails = res.Data;
          this.firstBanner =
            this.imageBaseUrl +
            '/' +
            this.giftVoucherDetails?.top_banner?.image;
          this.secondBanner =
            this.imageBaseUrl +
            '/' +
            this.giftVoucherDetails?.bottom_banner?.image;
        } else {
          this.toast.error('Something went wrong', 'Error');
        }
      },
      (error) => {
        this.toast.error(error, 'Error');
      }
    );
  }

  bannerRedirections(type: any, id: any, title: any) {
    if (type == 1) this.router.navigate(['/detail', id]);
    else if (type == 2 || 3 || 4) this.router.navigate(['/listing', type, id]);
    if (title == 'Zora') this.router.navigateByUrl('/zora-collection');

    if (type == 5 && id == 3) {
      this.router.navigateByUrl('/virtual-shopping');
    } else if (type == 5 && id == 4) {
      this.router.navigateByUrl('/zora-collection');
    } else if (type == 5 && id == 5) {
      this.router.navigateByUrl('/divo-collection');
    } else if (type == 5 && id == 6) {
      this.router.navigateByUrl('/poyem-collection');
    } else if (type == 5 && id == 7) {
      this.router.navigateByUrl('/orlin-collection');
    } else if (type == 5 && id == 8) {
      this.router.navigateByUrl('/book-appointment');
    } else if (type == 5 && id == 2) {
      this.router.navigateByUrl('/gold-schemes');
    } else if (type == 5 && id == 1) {
      this.router.navigateByUrl('/advance-booking');
    }else if(type == 5 && id == 9){
      this.router.navigateByUrl('/gift-voucher');
    }else if (type == 5 && id == 10){
      this.router.navigateByUrl('/digital-gold');
    }else if (type == 5 && id == 11){
      this.router.navigateByUrl('/about');
    }
  }
}
