<ngx-spinner
  bdColor="rgba(0, 0, 0, 0.8)"
  size="medium"
  color="#fff"
  type="square-jelly-box"
  [fullScreen]="true"
>
  <p style="color: white">{{ schemeUpi?.msg }}</p>
  <p style="color: white">Please do not refersh page and go back</p>
</ngx-spinner>

<div class="booking">
  <div class="container">
    <div class="title">
      <h1>Open New Mi Gold Account</h1>
      <p>
        Thank you for expressing your interest in opening a new MiGold Account.
        Please complete the process below for us to facilitate your request.
      </p>
    </div>
    <div class="paging">
      <div class="numbering" (click)="firstSection()">
        <span class="number active">1</span>Scheme Details
      </div>
      <div class="numbering" (click)="secondSection()">
        <span
          class="number"
          [ngClass]="{ active: second == true || third == true }"
          >2</span
        >Personal Details
      </div>
      <div class="numbering">
        <span class="number" [ngClass]="{ active: third == true }">3</span
        >Upload Document
      </div>
    </div>
  </div>

  <div class="container">
    <form [formGroup]="goldForm">
      <div class="bookingForm FirstSection" *ngIf="first">
        <div class="head">Specify Quantity and period</div>
        <div class="sections">
          <div class="leftSide">
            <div class="field">
              <div class="name">Choose your scheme</div>
              <ng-select
                class="filtername selectOption"
                [items]="plans"
                bindLabel="name"
                bindValue="id"
                [ngModelOptions]="{ standalone: true }"
                [(ngModel)]="selectedScheme"
                [clearable]="false"
                name="schemeSelect"
                required
                #schemeSelect="ngModel"
                [searchable]="false"
                (change)="getAmounts()"
              >
              </ng-select>
              <span *ngIf="schemeSelect.invalid && schemeSelect.touched">
                <span class="text-danger" *ngIf="schemeSelect.errors?.required"
                  >Please select a scheme</span
                >
              </span>
            </div>
          </div>
          <div class="rightSide">
            <div class="field">
              <div class="name">Choose installment amount</div>
              <ng-select
                class="filtername selectOption"
                [clearable]="false"
                [searchable]="false"
                [(ngModel)]="selectedAmount"
                [ngModelOptions]="{ standalone: true }"
                name="amountSelect"
                required
                #amountSelect="ngModel"
              >
                <ng-option
                  value="{{ amount }}"
                  *ngFor="let amount of schemeAmounts?.amounts"
                >
                  {{ amount }}
                </ng-option>
              </ng-select>
              <span *ngIf="amountSelect.invalid && amountSelect.touched">
                <span class="text-danger" *ngIf="amountSelect.errors?.required"
                  >Please select a amount</span
                >
              </span>
            </div>
          </div>
        </div>
        <div class="text-center">
          <div class="primary-button golden" (click)="nextOne()">Next</div>
        </div>
      </div>

      <div class="bookingForm SecondSection" *ngIf="second">
        <div class="head">Personal Details</div>
        <div class="sections">
          <div class="leftSide">
            <div class="field">
              <div class="name">Name*</div>
              <input type="text" formControlName="name" required />
              <span *ngIf="gf.name.invalid && gf.name.touched">
                <span class="text-danger" *ngIf="gf.name.errors?.required">
                  Name is required
                </span>
              </span>
            </div>
            <div class="field form calender">
              <div class="name">Date of Birth</div>
              <input
                type="date"
                [(ngModel)]="dob"
                [ngModelOptions]="{ standalone: true }"
                (ngModelChange)="checkDob()"
              />
              <!-- <ngx-datepicker [(ngModel)]="dob" [options]="options" [ngModelOptions]="{standalone: true}"
                (ngModelChange)="checkDob()"></ngx-datepicker> -->
              <span class="text-danger dob" *ngIf="!dob">{{
                dobValidation
              }}</span>
            </div>
            <div class="field drop" *ngIf="isAdult">
              <div class="name">Country</div>
              <ng-select
                class="filtername selectOption"
                [items]="countries"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="selectedCountry"
                [ngModelOptions]="{ standalone: true }"
                [clearable]="false"
                [searchable]="false"
                (change)="selectCountry()"
              >
              </ng-select>
              <span class="text-danger" *ngIf="!selectedCountry">{{
                countryValidation
              }}</span>
            </div>
            <div class="field" *ngIf="isAdult">
              <div class="name">Mail ID*</div>
              <input type="email" formControlName="email" />
              <span *ngIf="gf.email.invalid && gf.email.touched">
                <span class="text-danger" *ngIf="gf.email.errors?.required">
                  Email is required
                </span>
              </span>
              <span class="text-danger" *ngIf="gf.email.errors?.pattern"
                >Please Enter Valid Email
              </span>
            </div>
            <div class="field" *ngIf="isAdult">
              <div class="name">PAN number</div>
              <input
                type="text"
                formControlName="pan"
                (input)="onInputChange($event)"
                #myInput
              />
              <span *ngIf="gf.pan.invalid && gf.pan.touched">
                <span class="text-danger" *ngIf="gf.pan.errors?.required">
                  Pan Number is required
                </span>
              </span>
              <span class="text-danger" *ngIf="gf.pan.errors?.pattern"
                >Please Enter Valid Pan Number
              </span>
            </div>
            <div class="field" *ngIf="isAdult">
              <div class="name">District</div>
              <input type="text" formControlName="district" />
              <span *ngIf="gf.district.invalid && gf.district.touched">
                <span class="text-danger" *ngIf="gf.district.errors?.required">
                  District is required
                </span>
              </span>
            </div>
            <div class="field" *ngIf="isAdult">
              <div class="name">City</div>
              <input type="text" formControlName="city" />
              <span *ngIf="gf.city.invalid && gf.city.touched">
                <span class="text-danger" *ngIf="gf.city.errors?.required">
                  City is required
                </span>
              </span>
            </div>
          </div>
          <div class="rightSide">
            <div class="field">
              <div class="name">Gender</div>
              <ng-select
                class="filtername selectOption"
                name=""
                id=""
                [clearable]="false"
                [searchable]="false"
                formControlName="gender"
              >
                <ng-option value="male">Male</ng-option>
                <ng-option value="female">Female</ng-option>
              </ng-select>
            </div>
            <div class="field">
              <div class="name">Aadhar number</div>
              <input
                type="number"
                formControlName="aadhar"
                required
                (keydown)="onKeyDown($event)"
              />
              <span *ngIf="gf.aadhar.invalid && gf.aadhar.touched">
                <span class="text-danger" *ngIf="gf.aadhar.errors?.required">
                  Aadhar Number is required
                </span>
              </span>
              <span class="text-danger" *ngIf="gf.aadhar.errors?.pattern"
                >Please Enter Valid Aadhar Number
              </span>
            </div>
            <div class="field drop" *ngIf="isAdult">
              <div class="name">State</div>
              <ng-select
                class="filtername selectOption"
                [items]="states"
                bindLabel="name"
                bindValue="id"
                [(ngModel)]="selectedState"
                [ngModelOptions]="{ standalone: true }"
                [clearable]="false"
                [searchable]="false"
                [disabled]="!states || states.length === 0"
              >
              </ng-select>
              <span class="text-danger" *ngIf="!selectedState">{{
                stateValidation
              }}</span>
            </div>
            <div class="field" *ngIf="isAdult">
              <div class="name">Mobile number*</div>
              <div class="country">
                <select
                  class="selectOption"
                  name=""
                  id=""
                  formControlName="code"
                >
                  <option
                    [value]="country.phonecode"
                    *ngFor="let country of countries"
                  >
                    +{{ country.phonecode }}
                  </option>
                </select>
                <input
                  type="number"
                  formControlName="mobile"
                  (keydown)="onKeyDown($event)"
                />
                <span *ngIf="gf.mobile.invalid && gf.mobile.touched">
                  <span class="text-danger" *ngIf="gf.mobile.errors?.required">
                    Mobile Number is required
                  </span>
                </span>
                <span class="text-danger" *ngIf="gf.mobile.errors?.pattern"
                  >Please Enter Valid Mobile Number
                </span>
              </div>
            </div>
            <div class="field" *ngIf="isAdult">
              <div class="name">House Name/Flat No</div>
              <input type="text" formControlName="house_name" />
              <span *ngIf="gf.house_name.invalid && gf.house_name.touched">
                <span class="text-danger" *ngIf="gf.house_name.errors?.required"
                  >House name is required
                </span>
              </span>
            </div>
            <div class="field" *ngIf="isAdult">
              <div class="name">Street/Location</div>
              <input type="text" formControlName="street" />
              <span *ngIf="gf.street.invalid && gf.street.touched">
                <span class="text-danger" *ngIf="gf.street.errors?.required">
                  Street is required
                </span>
              </span>
            </div>
            <div class="field" *ngIf="isAdult">
              <div class="name">Pin Code</div>
              <input
                type="number"
                formControlName="pincode"
                (keydown)="onKeyDown($event)"
              />
              <span *ngIf="gf.pincode.invalid && gf.pincode.touched">
                <span class="text-danger" *ngIf="gf.pincode.errors?.required">
                  Pincode is required
                </span>
              </span>
              <span class="text-danger" *ngIf="gf.pincode.errors?.pattern"
                >Please Enter Valid pincode</span
              >
            </div>
          </div>
        </div>

        <div class="nominee" *ngIf="isAdult">
          <div class="head">Nominee Details</div>
          <div class="sections">
            <div class="leftSide">
              <div class="field">
                <div class="name">Name</div>
                <input type="text" formControlName="nominee_name" />
                <span
                  *ngIf="gf.nominee_name.invalid && gf.nominee_name.touched"
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.nominee_name.errors?.required"
                  >
                    Nominee Name is required
                  </span>
                </span>
              </div>
              <div class="field">
                <div class="name">Relationship</div>
                <input type="text" formControlName="nominee_relatiom" />
                <span
                  *ngIf="
                    gf.nominee_relatiom.invalid && gf.nominee_relatiom.touched
                  "
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.nominee_relatiom.errors?.required"
                  >
                    Nominee Relation is required
                  </span>
                </span>
              </div>
            </div>
            <div class="rightSide form">
              <div class="field">
                <div class="name">Aadhar number</div>
                <input
                  type="number"
                  formControlName="nominee_aadhar"
                  (keydown)="onKeyDown($event)"
                  (change)="aadharValidation($event.target)"
                />
                <span
                  *ngIf="gf.nominee_aadhar.invalid && gf.nominee_aadhar.touched"
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.nominee_aadhar.errors?.required"
                  >
                    Aadhar Number is required
                  </span>
                </span>
                <span
                  class="text-danger"
                  *ngIf="gf.nominee_aadhar.errors?.pattern"
                  >Please Enter Valid Aadhar number</span
                >
              </div>
              <div class="field calender">
                <div class="name">Date of Birth</div>
                <input
                  type="date"
                  [(ngModel)]="nominee_dob"
                  [ngModelOptions]="{ standalone: true }"
                />
                <!-- <ngx-datepicker [(ngModel)]="nominee_dob" [options]="options"
                  [ngModelOptions]="{standalone: true}"></ngx-datepicker> -->
              </div>
            </div>
          </div>
        </div>

        <div class="guardian" *ngIf="!isAdult">
          <div class="head">Guardian Details</div>
          <div class="sections">
            <div class="leftSide">
              <div class="field">
                <div class="name">Name*</div>
                <input type="text" formControlName="guardian_name" />
                <span
                  *ngIf="gf.guardian_name.invalid && gf.guardian_name.touched"
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_name.errors?.required"
                  >
                    Name is required
                  </span>
                </span>
              </div>
              <div class="field">
                <div class="name">Email*</div>
                <input type="email" formControlName="guardian_email" />
                <span
                  *ngIf="gf.guardian_email.invalid && gf.guardian_email.touched"
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_email.errors?.required"
                  >
                    Email is required
                  </span>
                </span>
                <span
                  class="text-danger"
                  *ngIf="gf.guardian_email.errors?.pattern"
                  >Please Enter Valid Email</span
                >
              </div>
              <div class="field">
                <div class="name">PAN number</div>
                <input
                  type="text"
                  formControlName="guardian_pan"
                  (input)="onInputChange($event)"
                  #myInput
                />
                <span
                  *ngIf="gf.guardian_pan.invalid && gf.guardian_pan.touched"
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_pan.errors?.required"
                  >
                    Pan Number is required
                  </span>
                </span>
                <span
                  class="text-danger"
                  *ngIf="gf.guardian_pan.errors?.pattern"
                  >Please Enter Valid Pan Number</span
                >
              </div>
              <div class="field drop">
                <div class="name">Country</div>
                <ng-select
                  class="filtername selectOption"
                  [items]="countries"
                  bindLabel="name"
                  bindValue="id"
                  [(ngModel)]="selectedCountry"
                  [ngModelOptions]="{ standalone: true }"
                  [clearable]="false"
                  [searchable]="false"
                  (change)="selectCountry()"
                >
                </ng-select>
                <span class="text-danger" *ngIf="!selectedCountry">{{
                  countryValidation
                }}</span>
              </div>
              <div class="field">
                <div class="name">House Name/Flat No</div>
                <input type="text" formControlName="guardian_house_name" />
                <span
                  *ngIf="
                    gf.guardian_house_name.invalid &&
                    gf.guardian_house_name.touched
                  "
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_house_name.errors?.required"
                    >House Name is required
                  </span>
                </span>
              </div>
              <div class="field">
                <div class="name">City</div>
                <input type="text" formControlName="guardian_city" />
                <span
                  *ngIf="gf.guardian_city.invalid && gf.guardian_city.touched"
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_city.errors?.required"
                  >
                    City is required
                  </span>
                </span>
              </div>
              <div class="field">
                <div class="name">Street/Location</div>
                <input type="text" formControlName="guardian_street" />
                <span
                  *ngIf="
                    gf.guardian_street.invalid && gf.guardian_street.touched
                  "
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_street.errors?.required"
                  >
                    Street is required
                  </span>
                </span>
              </div>
            </div>
            <div class="rightSide form">
              <div class="field">
                <div class="name">Mobile number*</div>
                <div class="country">
                  <select
                    class="selectOption"
                    name=""
                    id=""
                    formControlName="guardian_code"
                  >
                    <option
                      [value]="country.phonecode"
                      *ngFor="let country of countries"
                    >
                      +{{ country.phonecode }}
                    </option>
                  </select>
                  <input
                    type="number"
                    formControlName="guardian_mobile"
                    (keydown)="onKeyDown($event)"
                  />
                  <span
                    *ngIf="
                      gf.guardian_mobile.invalid && gf.guardian_mobile.touched
                    "
                  >
                    <span
                      class="text-danger"
                      *ngIf="gf.guardian_mobile.errors?.required"
                    >
                      Mobile Number is required
                    </span>
                  </span>
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_mobile.errors?.pattern"
                    >Please Enter Valid Mobile Number</span
                  >
                </div>
              </div>
              <div class="field">
                <div class="name">Relationship</div>
                <input type="text" formControlName="guardian_relation" />
                <span
                  *ngIf="
                    gf.guardian_relation.invalid && gf.guardian_relation.touched
                  "
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_relation.errors?.required"
                  >
                    Relation is required
                  </span>
                </span>
              </div>
              <div class="field">
                <div class="name">Aadhar number</div>
                <input
                  type="number"
                  formControlName="guardian_aadhar"
                  (keydown)="onKeyDown($event)"
                  (change)="aadharValidation($event.target)"
                />
                <span
                  *ngIf="
                    gf.guardian_aadhar.invalid && gf.guardian_aadhar.touched
                  "
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_aadhar.errors?.required"
                  >
                    Aadhar Number is required
                  </span>
                </span>
                <span
                  class="text-danger"
                  *ngIf="gf.guardian_aadhar.errors?.pattern"
                  >Please Enter Valid Aadhar number</span
                >
              </div>
              <div class="field drop">
                <div class="name">State</div>
                <ng-select
                  class="filtername selectOption"
                  [items]="states"
                  bindLabel="name"
                  bindValue="id"
                  [(ngModel)]="selectedState"
                  [ngModelOptions]="{ standalone: true }"
                  [clearable]="false"
                  [searchable]="false"
                  [disabled]="!states || states.length === 0"
                >
                </ng-select>
                <span class="text-danger" *ngIf="!selectedState">{{
                  stateValidation
                }}</span>
              </div>
              <div class="field">
                <div class="name">District</div>
                <input type="text" formControlName="guardian_district" />
                <span
                  *ngIf="
                    gf.guardian_district.invalid && gf.guardian_district.touched
                  "
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_district.errors?.required"
                  >
                    District is required
                  </span>
                </span>
              </div>
              <div class="field">
                <div class="name">Pin Code</div>
                <input
                  type="number"
                  formControlName="guardian_pincode"
                  (keydown)="onKeyDown($event)"
                />
                <span
                  *ngIf="
                    gf.guardian_pincode.invalid && gf.guardian_pincode.touched
                  "
                >
                  <span
                    class="text-danger"
                    *ngIf="gf.guardian_pincode.errors?.required"
                  >
                    Pincode is required
                  </span>
                </span>
                <span
                  class="text-danger"
                  *ngIf="gf.guardian_pincode.errors?.pattern"
                  >Please Enter Valid Pincode</span
                >
              </div>
            </div>
          </div>
        </div>
        <div class="text-center">
          <div class="primary-button golden" (click)="nextTwo()">Next</div>
        </div>
      </div>
      <div class="bookingForm uploadingSection" *ngIf="third">
        <div class="field d-flex">
          <div class="name">Upload Your Photo</div>
          <span class="uploading">
            <input type="file" required (change)="onFileChange($event)" />
            <img *ngIf="URL" class="preview" [src]="URL" alt="uploaded image" />
          </span>
          <span class="text-danger" *ngIf="!userImage">{{
            userimageValidation
          }}</span>
        </div>
        <div class="field">
          <div class="name">Upload Your Id</div>
          <div class="photos d-flex">
            <div class="radioBtn">
              <span class="radio">
                <input
                  id="radio-1"
                  name="proof"
                  type="radio"
                  formControlName="proof"
                  value="passport"
                  required
                />
                <label for="radio-1" class="radio-label"
                  >Valid Indian Passport</label
                >
              </span>
              <span class="radio">
                <input
                  id="radio-2"
                  name="proof"
                  type="radio"
                  formControlName="proof"
                  value="voterId"
                  required
                />
                <label for="radio-2" class="radio-label">Voter Id</label>
              </span>
              <span class="radio">
                <input
                  id="radio-3"
                  name="proof"
                  type="radio"
                  formControlName="proof"
                  value="aadhar"
                  required
                />
                <label for="radio-3" class="radio-label">Aadhar card</label>
              </span>
              <span class="radio">
                <input
                  id="radio-4"
                  name="proof"
                  type="radio"
                  formControlName="proof"
                  value="license"
                  required
                />
                <label for="radio-4" class="radio-label">Driving License</label>
              </span>
            </div>
            <span *ngIf="gf.proof.invalid && gf.proof.touched">
              <span class="text-danger" *ngIf="gf.proof.errors?.required">
                Proof Type is required
              </span>
            </span>
            <span class="uploading mr-3">
              <input type="file" (change)="onProofChange($event)" required />
              <img
                *ngIf="proofUrl"
                class="preview"
                [src]="proofUrl"
                alt="uploaded image"
              />
              <span class="side">Front Side</span>
            </span>
            <span
              class="text-danger"
              *ngIf="!proofImage"
              style="margin-left: 310px"
              >{{ proofimageValidation }}</span
            >
            <span class="uploading">
              <input
                type="file"
                (change)="onProofSideChange($event)"
                required
              />
              <img
                *ngIf="proofSideUrl"
                class="preview"
                [src]="proofSideUrl"
                alt="uploaded image"
              />
              <span class="side">Back Side</span>
            </span>
          </div>
        </div>
        <div class="field d-flex">
          <div class="name">pan card</div>
          <span class="uploading">
            <input type="file" (change)="onPanChange($event)" required />
            <img
              *ngIf="panUrl"
              class="preview"
              [src]="panUrl"
              alt="uploaded image"
            />
          </span>
          <span class="text-danger" *ngIf="!panImage">{{
            panimageValidation
          }}</span>
        </div>
        <div class="text-center">
          <!-- <div class="primary-button golden" (click)="upiModal()">Continue to Payments</div> -->
          <div class="primary-button golden" (click)="imageValidations()">
            Continue to Payments
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
<div class="helpiline">
  <div class="container">
    <div class="content">
      <span>Help Line Number</span>
      <a href="callto:+91 9497 916 000"
        ><img
          src="\assets\images\advance-booking\Vector.svg"
          alt="phone icon"
        />+91 9497 916 000</a
      >
    </div>
  </div>
</div>

<!-- <div class="registerPopup otp" *ngIf="PopupUpi">
  <div class="box">
    <div class="close" (click)="popUpClose()"><img src="\assets\images\close-white.svg" alt="close icon"></div>
    <div class="head">UPI</div>
    <div><input type="text" placeholder="Enter your upi id " [(ngModel)]="upiId"></div>
    <div class="field"><input type="submit" value="Continue" (click)="upiVerify()"></div>
  </div>
</div>
 -->

<div class="registerPopup otp" *ngIf="PopupUpi">
  <div class="box">
    <div class="close" (click)="popUpClose()">
      <img src="\assets\images\close-white.svg" alt="close icon" />
    </div>

    <div class="payment-content">
      <h2 class="title">Scan & Pay</h2>

      <p class="description">
        {{ schemeUpi?.msg ? schemeUpi?.msg + ". " : "" }}
        Simply scan the QR code using your favorite payment app . Enjoy a quick
        and hassle-free payment experience
      </p>

      <div class="qr-container">
        <qrcode
          [qrdata]="upiIntentUrl"
          [width]="256"
          [errorCorrectionLevel]="'M'"
        ></qrcode>
      </div>
    </div>
  </div>
</div>

<!-- <div>
  <img src="../../assets/images/coming_soon.png" style="
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 50%;
  ">
</div> -->
