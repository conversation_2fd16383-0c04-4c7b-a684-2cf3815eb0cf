<div class="orderDetail">
  <div class="container">
    <div class="head">My Order </div>
    <div class="contentSec">
      <div class="sideBar">
        <div class="field">Delivery Address</div>
        <div class="address">
          <div>{{deliveryAddress?.name}}</div>
          <div>{{deliveryAddress?.address}}</div>
          <!-- <div>Vengoor</div> -->
          <div>{{deliveryAddress?.city}}-{{deliveryAddress?.pincode}}</div>
          <div>{{deliveryAddress?.state}}, {{deliveryAddress?.country}} </div>
          <div>Mobile Number : {{deliveryAddress?.mobile}}</div>
        </div>
        <div class="line"></div>
        <div class="field">Billing Address</div>
        <div class="address">
          <div>{{billingAddress?.name}}</div>
          <div>{{billingAddress?.address}}</div>
          <!-- <div>Vengoor</div> -->
          <div>{{billingAddress?.city}}-{{billingAddress?.pincode}}</div>
          <div>{{billingAddress?.state}}, {{billingAddress?.country}} </div>
          <div>Mobile Number : {{billingAddress?.mobile}}</div>
        </div>
        <div class="line"></div>
        <div class="field">Delivery Details</div>
        <div class="address">
          Order Id : {{productdata?.order_id}}
        </div>
        <div class="line"></div>
        <div class="field">Payment Details</div>
        <div class="address">
          {{data?.price_details?.key}}: ₹ {{data?.price_details?.value}}
        </div>
        <!-- <div class="amountTotal">
          <div class="line"></div>
          <div class="field">Amount</div>
          <div class="amount">
            <div class="d-flex">
              <span>Subtotal : </span>
              <span>₹ 47,447.00</span>
            </div>
          </div>
          <div class="amount">
            <div class="d-flex">
              <span>Already Paid :</span>
              <span class="red">₹40,000.00</span>
            </div>
          </div>
          <div class="primary-button golden mt-5">Pay Balance</div>
        </div> -->
      </div>
      <div class="content">
        <div class="orders">
          <span class="image"><img src="{{imageBase}}/{{productdata?.thumbnail}}" alt="product image"></span>
          <div class="orderDetails">
            <div class="orderId">Order Id : {{productdata?.order_id}}</div>
            <div class="item">{{productdata?.name}}</div>
            <div class="price">₹{{productdata?.price}}</div>
          </div>
          <div class="button" *ngIf="data?.cancellation_possible == 1" (click)="popupConfirm()">Cancel item</div>
        </div>
        <!-- delivered -->
        <div class="orderedStatus">
          <div class="statusBar">
            <div class="sections" [ngClass]="{'active': item.is_active == 1,'cancelled' : item.slug == 'cancelled' || item.slug == 'returned'}" *ngFor="let item of data?.order_status">
              <div class="text">{{item?.name}}</div>
              <div class="line"></div>
              <div class="date">
                <div *ngIf="item.date != null">{{item?.date}}</div>
                <div *ngIf="item.time != null">{{item?.time}}</div>
              </div>
            </div>
            <!-- <div class="sections active">
              <div class="text">Shipped</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections">
              <div class="text">Out For Delivery</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections">
              <div class="text">Delivered</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div> -->
          </div>
          <div class="bottom">
            <div class="itemStatus">
              <!-- <div> Your item has been Assembling </div> -->
              <div class="return" (click)="returnPopup = true" *ngIf="data?.return_possible == 1"><img src="\assets\images\my-account\return.svg" alt="return"> Return</div>
            </div>
            <div class="statusAs">
              <div class="stat">
                <!-- <span class="color"></span>
                <div class="rightSide">
                  <div class="date">Delivery Expected by Feb 2 2022</div>
                </div> -->
              </div>
            </div>
          </div>
        </div>
        <!-- shipped -->
        <!-- <div class="orderedStatus">
          <div class="statusBar">
            <div class="sections active">
              <div class="text">Ordered</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections active">
              <div class="text">Shipped</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections">
              <div class="text">Out For Delivery</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections">
              <div class="text">Delivered</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="itemStatus">Your item has been Assembling </div>
            <div class="statusAs">
              <div class="stat">
                <span class="color"></span>
                <div class="rightSide">
                  <div class="date">Delivery Expected by Feb 2 2022</div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <!-- cancelled -->
        <!-- <div class="orderedStatus cancelled">
          <div class="statusBar">
            <div class="sections active">
              <div class="text">Ordered</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections active">
              <div class="text">Cancelled</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="itemStatus">Your item has been Assembling </div>
            <div class="statusAs">
              <div class="stat">
                <span class="color"></span>
                <div class="rightSide">
                  <div class="date">Cancelled</div>
                  <div>As per your request, your item has been cancelled
                    Feb 2 2022 7:32 Am</div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <!-- returned -->
        <!-- <div class="orderedStatus cancelled">
          <div class="statusBar">
            <div class="sections active">
              <div class="text">Ordered</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections active">
              <div class="text">returned</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="itemStatus">Item returned by you </div>
            <div class="statusAs">
              <div class="stat">
                <span class="color"></span>
                <div class="rightSide">
                  <div class="date">returned</div>
                  <div>As per your request, your item has been cancelled
                    Feb 2 2022 7:32 Am</div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <!-- arrived -->
        <!-- <div class="orderedStatus">
          <div class="statusBar">
            <div class="sections active">
              <div class="text">Ordered</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections active">
              <div class="text">Delivered</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="itemStatus">Your item has been delivered</div>
            <div class="statusAs">
              <div class="stat">
                <span class="color"></span>
                <div class="rightSide">
                  <div class="date">Delivered on Feb 2 2022</div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
        <!-- total -->
        <!-- <div class="orderedStatus">
          <div class="statusBar">
            <div class="sections active">
              <div class="text">Ordered</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections active">
              <div class="text">Work Inprogress</div>
              <div class="line"></div>
              <div class="date">
                <div>Jan 30 2022</div>
                <div>5:32 Pm</div>
              </div>
            </div>
            <div class="sections">
              <div class="text">Work Completed</div>
              <div class="line"></div>
            </div>
            <div class="sections">
              <div class="text">Out For Delivery</div>
              <div class="line"></div>
            </div>
            <div class="sections">
              <div class="text">Delivered</div>
              <div class="line"></div>
            </div>
          </div>
          <div class="bottom">
            <div class="itemStatus"></div>
            <div class="statusAs">
              <div class="stat">
                <span class="color"></span>
                <div class="rightSide">
                  <div class="date">Orderd</div>
                  <div>
                    <span>your item has Orderd</span>
                    <div>Feb 2 2022 7:32 Am</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
    </div>
  </div>
</div>


<div class="referPopup" [ngClass]="confirmPopup?'show':'hide'">
  <div class="box">
    <div class="close" (click)="confirmPopup = false"><img src="\assets\images\close-white.svg" alt="close icon"></div>
    <div><p>Are you sure?</p></div>
    <div class="button">
      <div class="primary-button white" (click)="confirmPopup = false">No</div>
      <div class="primary-button golden" (click)="cancelOrder()">Yes</div>
    </div>
  </div>
</div>


<div class="referPopup" [ngClass]="returnPopup?'show':'hide'">
  <div class="box">
    <div class="close" (click)="returnPopup = false"><img src="\assets\images\close-white.svg" alt="close icon"></div>
    <div><p>Are you sure?</p></div>
    <div class="button">
      <div class="primary-button white" (click)="returnPopup = false">No</div>
      <div class="primary-button golden" (click)="returnOrder()">Yes</div>
    </div>
  </div>
</div>
