<div
  class="adBanner"
  [ngStyle]="{ 'background-image': 'url(' + firstBanner + ')' }"
>
  <div class="content">
    <h1>{{ brand?.main_title }}</h1>
    <h3>{{ brand?.main_subtitle }}</h3>
  </div>
</div>

<div class="container">
  <div class="mostUnique">
    <div class="leftSection">
      <div class="content">
        <h2>{{ brand?.section2_title }}</h2>
        <p [innerHTML]="brand?.section2_description_1"></p>
      </div>
    </div>
    <div class="rightSection">
      <div class="image">
        <img src="{{ imageBase }}/{{ brand?.section2_image }}" alt="zora product image" />
      </div>
      <p [innerHTML]="brand?.section2_description_2"></p>
    </div>
  </div>
</div>

<!-- <div class="container">
    <div class="collectionSection">
        <div class="image"><span class="imageLine"><img src="\assets\images\zora\Rectangle 313.svg" alt=""></span></div>
        <div class="gold">
            <img src="\assets\images\zora\image1 (1) 1.svg" alt="">
            <div class="text">
                <div class="name">TT GOLD LIGHTWEIGHT 18KT BANGLE - 000</div>
                <div class="text">With the Zora Collection, T T Devassy Jewellery bringsan exclusive</div>
            </div>
        </div>
    </div>
</div> -->

<div class="container">
  <div class="collectionSecond">
    <!-- <div class="collections">
            <div class="gold">
                <img src="\assets\images\zora\slider-earring1 2.svg" alt="">
                <div class="text">
                    <div class="name">TT GOLD LIGHTWEIGHT 18KT BANGLE - 000</div>
                    <div class="text">With the Zora Collection, T T Devassy Jewellery bringsan exclusive</div>
                </div>
            </div>
            <div class="gold">
                <img src="\assets\images\zora\slider-earring1 1.svg" alt="">
                <div class="text">
                    <div class="name">TT GOLD LIGHTWEIGHT 18KT BANGLE - 000</div>
                    <div class="text">With the Zora Collection, T T Devassy Jewellery bringsan exclusive</div>
                </div>
            </div>
        </div> -->
    <!-- <div class="image"><span class="imageLine"><img src="\assets\images\zora\Rectangle 314.svg" alt=""></span></div> -->
    <div class="image">
      <img src="{{ imageBase }}/{{ brand?.section3_image_2 }}" alt="zora image" />
    </div>
    <div class="image">
      <img src="{{ imageBase }}/{{ brand?.section3_image_1 }}" alt="zora image" />
    </div>
    <div class="image">
      <img src="{{ imageBase }}/{{ brand?.section3_image_3 }}" alt="zora image" />
    </div>
  </div>
</div>

<div class="container">
  <div class="bannerAd">
    <img
      (click)="
        bannerRedirections(
          brand?.web_offer_banner?.redirection_type,
          brand?.web_offer_banner?.redirection_id,
          brand?.web_offer_banner?.title
        )
      "
      src="{{ imageBase }}/{{ brand?.web_offer_banner?.image }}"
      alt="zora banner"
    />
  </div>
</div>
<div class="container">
  <div class="bookEnquire">
    <a
      (click)="
        bannerRedirections(
          brand?.mi_gold_banner?.redirection_type,
          brand?.mi_gold_banner?.redirection_id,
          brand?.mi_gold_banner?.title
        )
      "
      class=""
      [ngStyle]="{ 'background-image': 'url(' + secondBanner + ')' }"
    >
      <h3>{{ brand?.mi_gold_banner?.title }}</h3>
      <p>{{ brand?.mi_gold_banner?.sub_title }}</p>
      <div class="join">{{ brand?.mi_gold_banner?.button_text }}</div>
    </a>
    <a
      (click)="
        bannerRedirections(
          brand?.advance_booking_banner?.redirection_type,
          brand?.advance_booking_banner?.redirection_id,
          brand?.advance_booking_banner?.title
        )
      "
      class=""
      [ngStyle]="{ 'background-image': 'url(' + thirdBanner + ')' }"
    >
      <h3>{{ brand?.advance_booking_banner?.title }}</h3>
      <div class="book">{{ brand?.advance_booking_banner?.button_text }}</div>
    </a>
  </div>
</div>
<!-- <div class="container">
  <div class="weHelp">
    <div class="box">
      <div class="leftSide">
        <img src="\assets\images\register-image.png" alt="" />
      </div>
      <div class="rightSide">
        <form [formGroup]="contactForm" (ngSubmit)="submitData()">
          <div class="head">We are here to help you</div>
          <div class="field">
            <div>Name</div>
            <input type="text" formControlName="name" />
          </div>
          <span
            class="text-danger"
            *ngIf="cf.name.errors?.['required']&&cf.name.touched"
          >
            Name is required
          </span>
          <div class="field">
            <div>Email Id</div>
            <input
              type="email"
              formControlName="email"
              placeholder="Email Id"
            />
          </div>
          <span
            class="text-danger"
            *ngIf="cf.email.errors?.['required']&&cf.email.touched"
          >
            Email is required
          </span>
          <span
            class="text-danger"
            *ngIf="!cf.email.errors?.['required']&&cf.email.errors?.['pattern']"
          >
            Invalid email
          </span>
          <div class="field">
            <div>Mobile Number</div>
            <input type="tel" formControlName="mobile" />
          </div>
          <span
            class="text-danger"
            *ngIf="cf.mobile.errors?.['required']&&cf.mobile.touched"
          >
            Mobile is required
          </span>
          <span
            class="text-danger"
            *ngIf="!cf.mobile.errors?.['required']&&cf.mobile.errors?.['pattern']"
          >
            Invalid number
          </span>
          <div class="field">
            <div>Message</div>
            <textarea
              name=""
              id=""
              cols="30"
              rows="3"
              formControlName="message"
            ></textarea>
          </div>
          <span
            class="text-danger"
            *ngIf="cf.message.errors?.['required']&&cf.message.touched"
          >
            Message is required
          </span>

          <label class="check"
            ><span><input type="checkbox" formControlName="checkbox" /></span
            ><span
              >By clicking I give my consent to receive messages/ email and
              updates to my number or email id
            </span></label
          >
          <div class="field"><input type="submit" value="Register" /></div>
        </form>
      </div>
    </div>
  </div>
</div> -->
