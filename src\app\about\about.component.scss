.topSection {
  padding: 60px 0 0;
  position: relative;
  margin-bottom: 60px;
  overflow: hidden;

  &::before {
    content: "";
    background-image: url(/assets/images/about-blue.svg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top;
    position: absolute;
    left: 0;
    top: 0;
    width: 52%;
    height: 90%;
    z-index: -1;
  }

  .sections {
    display: flex;
    justify-content: space-between;

    .leftSide {
      width: 240px;
      padding-bottom: 10%;

      h1 {
        color: #D2AB66;
        margin-top: 45px;
      }
    }

    .rightSide {
      width: calc(100% - 300px);
      margin-top: 20px;

      .head {
        color: #D2AB66;
        font-size: 30px;
        margin-bottom: 20px;
        position: relative;

        &::before {
          content: "";
          position: absolute;
          left: 183px;
          width: 1000%;
          height: 1px;
          border: 1px solid #000000;
          top: 24px;
        }

        &::after {
          content: "";
          position: absolute;
          left: 176px;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #000000;
          top: 21px;
        }
      }

      img {
        width: 100%;
      }
    }

  }


}

.humbleSection {
  padding: 50px 0 0px;
  margin-bottom: 100px;

  .sections {
    display: flex;
    justify-content: space-between;
    position: relative;
    padding-bottom: 100px;
    border-right: 1px solid #000;

    &::before {
      content: "";
      position: absolute;
      width: 1000%;
      height: 1px;
      bottom: 0;
      background: #000;
      right: 0;
    }

    &::after {
      content: "";
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #000;
      top: 0;
      right: -4.4px;
    }

    span {
      width: 33.33%;
      text-align: center;
    }

    span p {
      text-align: left;
      padding: 0 60px;
    }
  }
}

.shopRobbed {
  display: flex;
  padding-bottom: 100px;

  .leftSide {
    width: 76px;
  }

  .rightSide {
    width: calc(100% - 215px);
    padding-left: 50px;

    h2 {
      margin-bottom: 20px;
    }
  }
}

.secondGen {
  padding: 0px 0 100px;
  position: relative;

  &::before {
    content: "";
    background-image: url(/assets/images/about-blue.svg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 560px;
    height: 55%;
    z-index: -1;
  }

  .heading {
    display: flex;
    align-items: center;

    .line {
      display: block;
      border-bottom: 1px solid #000;
      width: calc(100% - 260px);
      margin-left: 45px;
      position: relative;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #000;
        top: -3px;
      }

      &:after {
        content: "";
        position: absolute;
        right: 0;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #000;
        top: -3px;
      }
    }

    h2 {
      width: 260px;
    }

  }

  .contents {
    display: flex;
    flex-wrap: wrap;
    padding-top: 50px;
  }

  .leftSide {
    max-width: 400px;
  }

  .rightSide {
    width: calc(100% - 400px);
    padding-left: 100px;

    p {
      margin-bottom: 25px;

      span {
        font-size: 24px;
        font-weight: 600;
      }
    }
  }

  .qoute {
    color: #D2AB66;
    font-size: 16px;
    padding: 20px 0 30px;
    font-family: "Libre Bodoni";
    line-height: 30px;
  }
}


.milestone {
  padding: 100px 0;

  .heading h2 {
    border-bottom: 1px solid #000;
    display: inline-block;
    margin: 0 auto 35px;
    width: max-content;
    padding: 0 20px 20px;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #000;
      bottom: -4px;
      right: 0px;
    }

    &::before {
      content: "";
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #000;
      bottom: -4px;
      left: 0px;
    }
  }

  .heading {
    text-align: center;
  }

  img {
    width: 100%;
  }
}

.chairmanVision {
  position: relative;
  margin-bottom: 100px;

  &::before {
    content: "";
    background-image: url(/assets/images/about-blue.svg);
    background-size: cover;
    background-repeat: no-repeat;
    background-position: top;
    position: absolute;
    left: 0;
    bottom: 0;
    width: 50%;
    height: 100%;
    z-index: -1;
  }

  .content {
    display: flex;
    flex-wrap: wrap;
  }

  .leftSide {
    width: 50%;
    height: auto;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;

    h2 {
      color: #D2AB66;
      height: 60%;
      display: flex;
      align-items: center;
      width: 40%;
      padding-left: 100px;
      justify-content: flex-start;
      position: relative;
    }

    .lineTop {
      position: absolute;
      left: 0;
      top: 0;
      width: 40%;
      height: 1px;
      border-top: 1px solid #000;
      content: "";

      &::after {
        content: "";
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #000;
        top: -4px;
        right: 0px;
      }
    }

    .lineLeft {
      position: absolute;
      left: 0;
      top: 0;
      width: 1px;
      height: 100%;
      border-left: 1px solid #000;
      content: "";

      &::after {
        content: "";
        position: absolute;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #000;
        bottom: 0;
        left: -4.5px;
      }
    }
  }

  .rightSide {
    width: 50%;
    padding: 30px 0 30px 50px;
  }

}

.twoSections {
  display: flex;
  justify-content: space-around;
  margin-bottom: 100px;

  .section {
    width: 25%;
    border-left: 1px solid #000;
    position: relative;

    &::before {
      position: absolute;
      content: "";
      bottom: 0;
      left: 0;
      width: 40%;
      height: 1px;
      border: 1px solid #000;
    }

    &::after {
      content: "";
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #000;
      bottom: -4.5px;
      left: 40%;
    }
  }

  .content {
    padding-bottom: 70px;
    padding-left: 60px;
  }

  .section.kids {
    img {
      margin-bottom: 25px;
    }

  }

  .readMore {
    display: flex;
    font-weight: 500;
    margin-top: 20px;

    span {
      padding-right: 20px;
      height: 100%;
    }

    img {

      transform: rotate(-90deg);
      width: 10px;
      margin-right: 0;

    }
  }

  .section.bridal {
    .content {
      padding-right: 40px;
    }

    .image {
      text-align: right;
      width: 100%;
    }

    .image img {
      margin-bottom: -90px;
    }

  }
}

.leaderSpeak {
  margin-bottom: 100px;

  .leader {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-bottom: 30px;

    .image {
      width: 100%;

      img {
        width: 100%;
      }
    }

    .text {
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      position: absolute;
      right: 16%;
      bottom: 42%;
      transform: translateY(50%);

      .designation {
        color: #FFF;
        text-align: center;
        font-family: "Gotham";
        font-size: 30px;
        font-style: normal;
        font-weight: 500;
        line-height: 108.7%;
        text-transform: capitalize;
      }

      .name {
        color: #FFF;
        font-family: "Gotham";
        font-size: 60px;
        font-style: normal;
        font-weight: 500;
        line-height: 108.7%;
        text-transform: capitalize;
      }
    }
  }

  p {
    margin-bottom: 20px;
  }

  .otherLeaders {
    .contents {
      display: flex;
      align-items: center;
      margin-top: 100px;

      &:nth-child(even) {
        flex-direction: row-reverse;
        .image{
          margin: 0 auto 0 0;
        }
      }
    }

    h4 {
      font-size: 32px;
      font-weight: 500;
      line-height: normal;
      letter-spacing: -0.5px;
      text-transform: capitalize;
    }

    .desig {
      color: #000;
      font-family: "Gotham";
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 25px;
      text-transform: capitalize;
      margin-bottom: 20px;
    }

    .detail {
      width: calc(100% - 531px);
    }

    .image {
      display: flex;
      justify-content: center;
      background-color: #2c5b7f;
      border-radius: 50%;
      width: 400px;
      height: 400px;
      margin-left: auto;
      //overflow: hidden;
      position: relative;

      img {
        height: 100%;
        border-radius: 50%;
        max-width: 400px;
        max-height: 400px;
        object-fit: cover;
      }

      &::after {
        content: "";
        position: absolute;
        bottom: -7px;
        left: 50%;
        transform: translateX(-50%);
        background-image: url(../../assets/images/about/vector.png);
        height: 77px;
        background-repeat: no-repeat;
        max-width: 308px;
        width: 100%;
      }
    }
  }

}

.twoGlories {
  margin-bottom: 100px;
  position: relative;

  &:before {
    content: "";
    position: absolute;
    background-repeat: no-repeat;
    background-size: cover;
    background-image: url(/assets/images/about-blue.svg);
    width: 60%;
    height: 100%;
    right: 0;
    z-index: -1;
  }

  .contents {
    display: flex;
    flex-wrap: wrap;
  }

  .image {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .text {
    width: 50%;
    padding: 50px;
    color: #fff;

    h2 {
      color: #D2AB66;
    }

    p {
      margin-bottom: 20px;
    }
  }
}



.visionMission {
  margin-bottom: 100px;

  .line {
    display: block;
    width: 80%;
    text-align: center;
    margin: 0 auto;
  }

  .content {
    display: flex;
    flex-wrap: wrap;
    width: 80%;
    margin: 50px auto;
    justify-content: space-between;
  }

  .line img {
    width: 100%;
  }

  .vision {
    width: 48%;
    text-align: right;
  }

  .mission {
    width: 48%;
  }
}

.thankyou {
  text-align: center;
  padding-bottom: 100px;


  .sincere div {
    margin-bottom: 5px;
  }

  .sincere {
    font-size: 16px;
    margin-top: 30px;
  }

  .sincere span {
    font-weight: 600;
  }
}


@media screen and (max-width:1360px) {
  .twoSections .section {
    width: 40%;
  }
}

@media screen and (max-width:992px) {
  .topSection {
    padding: 30px 0 0;
    margin-bottom: 10px;

    &::before {
      width: 50%;
    }

    .sections {
      flex-direction: column;
      margin-bottom: 20px;

      .leftSide {
        width: 100%;
        text-align: center;
        margin-bottom: 20px;
        padding-bottom: 0;

        h1 {
          margin-top: 5px;
        }

      }

      .rightSide {
        width: 100%;
        text-align: center;

        .head {
          text-align: left;
          font-size: 25px;

          &::after {
            top: 18px;
            left: 151px;
          }

          &::before {
            left: 153px;
            top: 21px;
          }
        }
      }

      &.sectionOne .logo {
        padding-bottom: 20px;
      }

      &.sectionTwo {
        .leftSide {
          display: block;
        }

        .rightSide {
          padding: 30px;
        }

      }

      &.sectionThree {
        .leftSide {
          padding: 30px;
        }

        .rightSide {
          justify-content: center;
        }
      }
    }

  }

  .secondGen {
    padding: 40px 0 20px;

    &::before {
      width: 50%;
      bottom: auto;
      top: 130px;
      height: calc(100% - 130px);
    }

    .leftSide {
      width: 80%;
      padding-left: 0;
      text-align: center;
      padding-bottom: 10px;
      max-width: 100%;
      margin: 0 auto;
    }

    .rightSide {
      width: 100%;
      text-align: center;
      padding-left: 0;

      p {
        margin-bottom: 12px;

        span {
          font-size: 14px;
        }
      }
    }

    .qoute {
      padding-bottom: 20px;
    }

  }

  .humbleSection {
    margin-bottom: 60px;

    .sections {
      display: flex;
      flex-direction: column;
      padding-bottom: 40px;

      span {
        width: 100%;
        text-align: center;

        p {
          text-align: center;
          padding: 20px 30px;
        }
      }
    }


  }

  .shopRobbed {
    padding-bottom: 40px;

    .leftSide {
      width: 36px;
    }

    .rightSide {
      width: calc(100% - 50px);
      padding-left: 30px;
    }
  }

  .milestone {
    padding: 30px 0;

    .heading h2 {
      width: auto;
    }
  }

  // .milestones {

  //   .sections {
  //     display: flex;
  //     flex-direction: column;
  //   }

  //   .box {
  //     margin-top: 0;
  //     padding: 140px 120px;
  //     width: 100%;
  //   }

  //   .box:nth-child(2n) {
  //     margin-top: 5%;
  //   }

  //   .box:nth-child(2n+1) {
  //     margin-top: 5%;
  //   }

  //   .box:nth-child(2n+1)::before {
  //     right: auto;
  //     transform: translateX(-50%);
  //     left: 50%;
  //     bottom: -80px;
  //   }

  //   .box:nth-child(2n)::before {
  //     left: 50%;
  //     transform: translateX(-50%);
  //     bottom: -80px;
  //     top: auto;
  //   }

  // }

  .twoSections {
    margin-bottom: 0px;
    flex-direction: column;

    .section {
      width: 70%;
      text-align: center;
      margin: 0 auto 40px;
    }
  }

  .twoGlories {
    margin-bottom: 50px;

    &:before {
      width: 100%;
      height: 80%;
      bottom: 0;
    }

    .image {
      width: 100%;
      justify-content: center;
      margin-top: 40px;
    }

    .text {
      width: 100%;
      text-align: center;
      padding: 10px;
    }
  }

  .chairmanVision {
    margin-bottom: 50px;

    .leftSide {
      width: 100%;
      height: 120px;
      margin-top: 60px;

      h2 {
        width: 100%;
        justify-content: center;
        padding: 0 10px;
        height: 100%;
        text-align: center;
      }

      .lineTop {
        width: 100%;
      }

    }

    &::before {
      width: 100%;
    }

    .rightSide {
      width: 100%;
      padding: 30px 30px 50px;
      text-align: center;
      color: #fff;
    }


  }

  .leaderSpeak {
    margin-bottom: 50px;

    .leader {
      .text {
        .designation {
          font-size: 18px;
        }

        .name {
          font-size: 32px;
        }
      }
    }


    p {
      margin-bottom: 15px;
    }

    .otherLeaders {
      .detail {
        width: calc(100% - 248px);
      }

      .image {
        width: 208px;
        height: 208px;

        &::after {
          height: 69px;
          width: 170px;
          background-size: contain;
          bottom: -26px;
        }
      }
    }
  }

  .visionMission {
    margin-bottom: 40px;

    .vision {
      width: 100%;
      text-align: center;
    }

    .mission {
      width: 100%;
      text-align: center;
      margin-top: 20px;
    }

    .content {
      margin: 20px auto;
    }
  }

  .thankyou {
    padding-bottom: 40px;
  }

}

@media screen and (max-width:640px) {
  .twoSections .section {
    width: 100%;
  }

  .twoSections .content {
    padding-left: 30px;
    padding-bottom: 40px;
  }

  .twoGlories:before {
    height: 90%;
    background-size: cover;
  }

  .secondGen .heading h2 {
    width: 140px;
  }

  .secondGen .heading .line {
    width: calc(100% - 140px);
    margin-left: 0;
  }

  .leaderSpeak {
    .leader {
      .text {
        right: 6%;

        .designation {
          font-size: 12px;
        }

        .name {
          font-size: 20px;
        }
      }
    }
  p{
    text-align: center;
  }

    .otherLeaders {
      .contents {
        margin-top: 50px;
        display: flex;
        flex-direction: column-reverse;
        &:nth-child(even) {
          flex-direction: column-reverse;
          .image{
            margin: 0 auto 30px;;
          }
        }
      }

      h4 {
        font-size: 25px;
      }

      .desig {
        font-size: 14px;
        margin-bottom: 12px;
      }

      .detail {
        width: 100%;
        text-align: center;
      }

      .image {
        margin: 0 auto 30px;
      }
    }


  }
}