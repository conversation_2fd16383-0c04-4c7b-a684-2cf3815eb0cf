// Banner Section with Enhanced Video and Thumbnail Styling
.bannerSection {
  height: 100dvh;
  min-height: 640px;
  position: relative;
  overflow: hidden;
  width: 100%;

  .swiper {
    height: 100%;
    width: 100%;
  }

  .slideItems {
    width: 100%;
    height: 100%;
    position: relative;

    a {
      display: block;
      width: 100%;
      height: 100%;
    }
  }

  // Video Container with Enhanced State Management
  .video-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    background: #000;

    // Show video and hide thumbnail when video is successfully playing
    &.video-loaded {
      .video-thumbnail {
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.5s ease;
      }
      
      .banner-video {
        opacity: 1;
        transition: opacity 0.5s ease;
      }

      .web-video{
        display: block;
      }

      .mobile-video {
        display: none;
      }

    }
  }

  // Shared styles for both video and thumbnail elements
  .video-element {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
  }

  .video-thumbnail {
    z-index: 2;
    transition: opacity 0.5s ease;
    background: #f0f0f0;
  }

  .banner-video {
    z-index: 1;
    opacity: 0;
    transition: opacity 0.5s ease;
  }

  // Loading overlay that appears on top of thumbnail
  .video-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(2px);
    transition: opacity 0.3s ease;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    background: rgba(0, 0, 0, 0.6);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  .loading-content p {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    letter-spacing: 0.5px;
  }

  // Slide Content Overlay
  .slide-content-overlay {
    position: absolute;
    bottom: 30px;
    left: 30px;
    z-index: 4;
    color: white;
    max-width: 50%;
  }

  .slide-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
  }

  .slide-description {
    font-size: 1.1rem;
    opacity: 0.9;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    line-height: 1.4;
  }

  // Custom Next Slide Preview
  .next-slide-preview {
    position: absolute;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    cursor: pointer;
    transition: all 0.3s ease;
    pointer-events: auto;

    &:hover {
      transform: translateY(-5px);

      .preview-content {
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4);
      }
    }

    .preview-content {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      padding: 12px;
      transition: all 0.3s ease;
      border-radius: 8px;
      border: 1px solid rgba(255, 255, 255, 0.2);

      .preview-image {
        width: 86px;
        height: 86px;
        overflow: hidden;
        margin-right: 12px;
        flex-shrink: 0;
        border-radius: 4px;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .preview-text {
        display: flex;
        flex-direction: column;
        color: white;

        .next-label {
          font-size: 12px;
          font-weight: 600;
          letter-spacing: 1px;
          color: rgba(255, 255, 255, 0.7);
          margin-bottom: 2px;
          text-transform: uppercase;
        }

        .slide-title {
          font-size: 18px;
          font-weight: 500;
          line-height: 1.2;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          margin-bottom: 26px;
          max-width: 200px;
        }

        .custom-pagination {
          display: flex;
          gap: 10px;
          align-items: center;

          .pagination-line {
            height: 2px;
            width: 42px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 1px;
            transition: all 0.3s ease;
            cursor: pointer;

            &.active {
              background: #D3AD68;
            }

            &:hover {
              background: rgba(255, 255, 255, 0.6);
            }
          }
        }
      }
    }
  }

  // Traditional pagination dots
  .swiper-pagination {
    bottom: 20px;
    left: 30px;
    width: auto;
    text-align: left;

    .swiper-pagination-bullet {
      width: 8px;
      height: 8px;
      background: rgba(255, 255, 255, 0.4);
      border-radius: 50%;
      margin: 0 6px 0 0;
      cursor: pointer;
      transition: all 0.3s ease;
      opacity: 1;

      &.swiper-pagination-bullet-active {
        background: #fff;
        transform: scale(1.3);
      }
    }
  }

  // Slide counter
  .slide-counter {
    position: absolute;
    top: 30px;
    right: 30px;
    z-index: 1000;
    color: white;
    font-size: 18px;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);

    .separator {
      margin: 0 5px;
      opacity: 0.7;
    }
  }

  // Navigation arrows
  .swiper-button-next,
  .swiper-button-prev {
    color: white;
    
    &:after {
      font-size: 20px;
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .slide-content-overlay {
      left: 20px;
      bottom: 20px;
      max-width: 70%;
    }

    .slide-title {
      font-size: 2rem;
    }
    
    .slide-description {
      font-size: 1rem;
    }
    
    .next-slide-preview {
      right: auto;
      left: 50%;
      transform: translateX(-50%);
      width: max-content;

      .preview-content {
        padding: 10px;

        .preview-text .slide-title {
          max-width: 150px;
        }
      }
    }
    
    .swiper-pagination {
      bottom: 15px;
      left: 20px;

      .swiper-pagination-bullet {
        width: 6px;
        height: 6px;
        margin: 0 4px 0 0;
      }
    }

    .slide-counter {
      top: 20px;
      right: 20px;
      font-size: 16px;
    }

    .loading-content {
      padding: 15px;
      border-radius: 8px;
    }
    
    .spinner {
      width: 32px;
      height: 32px;
      border-width: 2px;
      margin-bottom: 12px;
    }
    
    .loading-content p {
      font-size: 12px;
    }
  }

  @media (max-width: 480px) {
    .next-slide-preview {
      bottom: 15px;
      right: 15px;
      left: auto;
      transform: none;

      .preview-content {
        padding: 8px;

        .preview-image {
          width: 60px;
          height: 60px;
        }

        .preview-text .slide-title {
          font-size: 14px;
          max-width: 120px;
        }

        .custom-pagination .pagination-line {
          width: 30px;
        }
      }
    }

    .slide-content-overlay {
      left: 15px;
      bottom: 15px;
      max-width: 80%;
    }

    .slide-title {
      font-size: 1.5rem;
    }
  }
}

button {
  background: transparent;
  border: none;
}

// Existing styles for other components remain unchanged

.specification {
  padding: 40px 0 0;

  .contents {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
  }

  .box {
    display: flex;
    flex-direction: column;
    padding: 30px 0 0;
    align-items: center;
    width: 110px;

    &:hover img {
      transform: rotate(360deg);
      transition: ease 0.8s;
    }
  }

  .text {
    text-align: center;
    padding-top: 15px;
    font-size: 14px;
    font-family: "Libre Bodoni";
  }
}

.topTrending {
  margin: 100px 0;

  h2 {
    text-align: center;
    margin-bottom: 40px;
  }

  .imageSection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 42px;

    .image img {
      width: 100%;
    }
  }
}

.forMenWomen {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .for {
    aspect-ratio: 1.01;
    position: relative;
    width: 50%;
    overflow: hidden;
    max-height: 90vh;

    &:hover {
      .image {
        transform: scale(1.1);
        transition: all linear 0.8s;
      }

      h2 img {
        transform: translateX(10px);
        transition: all linear 0.8s;
      }
    }

    .image {
      transition: all linear 0.8s;
      height: 100%;
      object-fit: cover;
      width: 100%;
      object-position: top;
    }

    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 0;
      background: linear-gradient(180deg,
          rgba(0, 0, 0, 0) 0%,
          rgba(0, 0, 0, 0.35) 100%);
    }
  }

  h4 {
    position: absolute;
    bottom: 47px;
    margin-bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    width: 100%;
    text-align: center;
    z-index: 1;

    img {
      transition: all linear 0.8s;
    }

    span {
      padding-right: 10px;
    }
  }
}

.trendingSection {
  background-color: #fffbf3;
  padding: 100px 0 140px;
  background-size: cover;
  background-position: top right;

  h2 {
    margin-bottom: 40px;
  }

  .slide {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    bottom: -20px;
  }

  .slideItems {
    text-align: center;
    background: #ffffff;
    box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.06);
    margin: 0px 8px;
    height: 100%;

    &:hover {
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);

      .productImage {
        img {
          transform: translateY(-10px);
          transition: ease 0.7s;
        }
      }
    }

    .productImage {
      padding: 30px 30px 26px;
      overflow: hidden;

      img {
        transition: ease 0.9s;
        aspect-ratio: 1;
        object-fit: cover;
      }
    }
  }

  .productName {
    font-size: 14px;
    padding: 0 30px 15px;
    line-height: 20px;
  }

  .price {
    font-size: 14px;
    font-weight: 600;
    padding: 0 30px 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    .offer {
      padding: 0 5px;
    }

    .old {
      text-decoration: line-through;
      padding: 0 5px;
      color: #b3b3b3;
    }
  }

  .ar {
    position: absolute;
    top: 20px;
    width: 23px;
    left: 20px;
    cursor: pointer;
    z-index: 2;
  }

  .wishlist {
    z-index: 2;
    position: absolute;
    right: 20px;
    display: flex;
    top: 20px;
    cursor: pointer;

    .fav-gold {
      display: none;
    }

    &.active .fav-gold {
      display: block;
    }

    &.active .fav {
      display: none;
    }
  }
}

.brandsLove {
  padding: 100px 0 150px;

  .slide {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    bottom: 0;
    margin-top: 20px;
  }

  .slideItems {
    width: 100%;
    height: 100%;

    &:hover img {
      transform: translate3d(10px, 10px, 10px);
      transition: all linear 0.8s;
    }

    img {
      transition: all linear 0.8s;
      width: 100%;
    }
  }

  .brandImage {
    position: absolute;
    bottom: 30px;
    left: 50%;
    width: 50%;
    transform: translateX(-50%);
  }
}

.bookGold {
  display: block;
  position: relative;

  .image {
    width: 100%;
    height: 100%;
  }

  .desktop {
    display: block;
  }

  .mobile {
    display: none;
  }

  @media screen and (max-width: 768px) {
    .desktop {
      display: none;
    }

    .mobile {
      display: block;
    }
  }

  .content {
    display: none;
    position: absolute;
    left: 50%;
    transform: translate(-50%, -50%);
    top: 50%;
    text-align: center;
    color: #fff;
    max-width: 302px;

    h2 {
      color: #fff;
      margin-bottom: 30px;
    }

    .book {
      background: #fff;
      color: #d2ab66;
      padding: 18px 70px;

      &:hover {
        background: #d2ab66;
        color: #fff;
      }
    }
  }
}

.digitalWear {
  margin: 100px 0;
  background-image: url(/assets/images/digital-bg.svg);
  background-repeat: no-repeat;
  background-size: cover;
  overflow: hidden;

  .digitalImg:hover {
    transform: translateY(50px);
    transition: all linear 0.9s;
  }

  .digitalImg {
    transition: all linear 0.7s;
    width: calc(100% - 368px);
  }

  .digital {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .content {
    width: 368px;

    h2 {
      margin-bottom: 25px;
    }

    p {
      margin-bottom: 30px;
    }
  }

  .digitalBtn {
    background: #ce9599;
    padding: 18px 40px;
    color: #fff;

    &:hover {
      background: #fff;
      color: #ce9599;
    }
  }
}

// .offSection {
//   // display: grid;
//   // grid-template-columns: repeat(2, 1fr);
//   // gap: 40px; 
// }

.hotCategory {
  padding-bottom: 100px;

  .slide {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: center;
    bottom: 0;
    margin-top: 20px;
  }

  .slideItems {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;

    img {
      transition: all linear 0.8s;
      height: 100%;
      width: 100%;
    }
  }

  .name {
    position: absolute;
    right: 10px;
    left: auto;
    bottom: 10px;
    color: #fff;
    font-size: 20px;
    z-index: 1;
    font-family: "Libre Bodoni";
  }

  .slideItems::before {
    content: "";
    position: absolute;
    width: 0;
    height: 100%;
    background: #00000085;
    z-index: 1;
    right: 0;
    transition: ease 0.5s;
    top: 0;
  }

  .slideItems:hover::before {
    width: 100%;
    transition: ease 0.5s;
  }
}

.perfectBride {
  display: flex;

  .image:hover:before {
    background: #00000080;
    width: 100%;
    height: 100%;
    transition: ease 0.5s;
  }

  .image:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
  }

  .leftSection {
    width: 70%;
    display: flex;
    flex-direction: column;

    .top {
      display: flex;
      justify-content: space-between;
      padding-bottom: 15px;
      height: 50%;

      .image {
        width: 32.33%;
        margin-right: 1%;
        position: relative;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }

    .bottom {
      display: flex;
      height: 50%;

      .image {
        &.image_2 {
          width: 32.33%;
          margin-right: 1%;
          position: relative;
        }

        img {
          width: 100%;
          height: 100%;
        }

        &.image_1 {
          width: calc(100% - 34.33%);
          margin-right: 1%;
          position: relative;
        }
      }
    }

    .name {
      position: absolute;
      right: 7%;
      bottom: 7%;
      color: #fff;
      font-family: "LibreBodoni-Regular";
    }
  }

  .rightSection {
    width: 30%;

    .image {
      height: 100%;
      position: relative;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

.clientSay {
  padding: 100px 0 0;

  .heading {
    justify-content: space-between;

    h2 {
      width: 70%;
    }
  }

  .slide {
    position: relative;
    display: flex;
    align-items: center;
    bottom: 0;
    width: 200px;
    margin: 0 auto 30px;
    justify-content: center;
  }

  .slideItems {
    text-align: center;
  }

  .image {
    width: 49px;
    height: 49px;
    border-radius: 50%;
    overflow: hidden;
  }

  .item {
    text-align: left;
  }

  .name {
    font-size: 16px;
    font-weight: 700;
    text-align: left;
  }

  .date {
    color: #858585;
    font-size: 14px;
    padding-bottom: 30px;
  }

  .testimonialBtn {
    max-width: 270px;
    margin: 0 auto;
  }

  p.description {
    display: inline;

    img {
      width: auto;
    }
  }

  .swiper-slide {
    border-left: 1px solid #CCCCCC;
    padding: 35px 30px;
  }

  .description .quote-start {
    margin-top: -18px;
  }

  .description .quote-end {
    transform: rotate(180deg);
  }

  .desig {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 35px;
  }
}

.miSection {
  margin-top: 100px;
  display: flex;

  .leftImage {
    width: 55%;
    height: 346px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: top;
    }
  }

  .rightContent {
    width: 45%;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px 60px;
    color: #25675d;
    background-image: url(/assets/images/mi-bg.svg);
    background-repeat: no-repeat;
    background-size: cover;

    h2 {
      color: #25675d;
    }
  }

  .join {
    background: #25675d;
    color: #fff;
    padding: 16px 40px;
    width: 230px;
    font-size: 16px;
    text-align: center;
    margin-top: 15px;

    &:hover {
      background: #225a52;
    }
  }
}

.featured {
  .container {
    padding-right: 0;
  }

  .slideItems {
    text-align: center;
    background: #ffffff;
    box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.06);
    margin: 20px 8px;

    &:hover {
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);

      .productImage {
        img {
          transform: translateY(-10px);
          transition: ease 0.7s;
        }
      }
    }

    .productImage {
      padding: 30px 30px 0;
      overflow: hidden;

      img {
        transition: ease 0.9s;
        max-height: 308px;
        object-fit: cover;
      }
    }
  }

  .productName {
    font-size: 14px;
    padding: 0 30px 15px;
    line-height: 20px;
  }

  .price {
    font-size: 14px;
    font-weight: 600;
    padding: 0 30px 30px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;

    .offer {
      padding: 0 5px;
    }

    .old {
      text-decoration: line-through;
      padding: 0 5px;
      color: #b3b3b3;
      font-size: 12px;
    }
  }

  .ar {
    position: absolute;
    top: 50px;
    width: 23px;
    left: 40px;
    cursor: pointer;
    z-index: 2;
  }

  .wishlist {
    z-index: 2;
    position: absolute;
    right: 40px;
    display: flex;
    top: 50px;
    cursor: pointer;

    .fav-gold {
      display: none;
    }

    &.active .fav-gold {
      display: block;
    }

    &.active .fav {
      display: none;
    }
  }

  .leftSection {
    width: 20%;
    position: relative;
  }

  .rightSection {
    width: 77%;
  }

  .contents {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}

.collectionSec {
  display: flex;
  justify-content: space-between;

  .box {
    width: 50%;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: 0;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.35) 100%);
      left: 0;
    }

    img {
      width: 100%;
    }
  }

  .head {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;

    h2 {
      font-size: 34px;
      color: #fff;
      font-family: 'Gotham';
    }
  }

  p {
    color: #fff;
  }
}

.special {
  display: block;
  position: relative;
  height: 450px;
  position: relative;

  h2 {
    color: #fff;
    margin-bottom: 25px;
  }

  video {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover;
    top: 0;
    left: 0;
  }

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .content {
    margin: 0 auto;
    width: 290px;
    text-align: center;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .explore {
    width: 240px;
    background: #fff;
    padding: 16px 0 13px;
    color: #000;
    font-weight: bold;
  }
}

.blogSec {
  padding-bottom: 50px;
  padding-top: 100px;

  .blogSection {
    display: flex;
    justify-content: space-between;
    margin: 0 -7px;

    .blogs {
      width: 25%;
      padding: 0 7px;
      padding-bottom: 40px;
    }

    .blogImage {
      width: 100%;
    }

    .featuredIcon {
      position: absolute;
      left: 0;
      top: 20px;
    }

    .image {
      position: relative;
      padding-bottom: 20px;
    }

    .date {
      color: #1d1d1d66;
      padding-bottom: 12px;
      font-size: 12px;
    }

    .text {
      padding-bottom: 12px;
      font-size: 16px;
      font-weight: 600;
    }

    .name {
      font-size: 14px;
    }
  }

  .view {
    display: block;
    text-align: center;

    a {
      background: #000;
      color: #fff;
      padding: 18px;
      width: 200px;
      font-size: 16px;
    }
  }
}

.WhatsGoing {
  padding-top: 100px;

  .head {
    display: flex;
    align-items: center;
    font-size: 30px;
    font-weight: 600;
    padding-bottom: 33px;

    span {
      padding-left: 10px;
    }
  }

  .social {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .socialMedia {
      padding-bottom: 60px;
      width: 30%;

      &:nth-child(2) {
        width: 40%;
      }

      &:nth-child(3) {
        width: 26%;
      }

      iframe {
        height: 337px;

        ._10b4 {
          max-height: 100% !important;
        }

        ._2lqg {
          height: 100% !important;
        }
      }
    }
  }
}

.connectSection {
  padding: 40px 0;

  .connect {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    justify-content: space-between;
  }

  .box {
    text-align: center;
    border: 1px solid #d5dbde;
    padding: 27px 20px 17px;

    &:hover {
      img {
        transform: translateX(10px);
        transition: ease 0.8s;
      }
    }

    img {
      transition: ease 0.8s;
    }
  }

  h6 {
    margin-top: 15px;
  }
}

.testimonialPopup {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #00000040;
  top: 0;
  left: 0;
  z-index: 99;

  .box {
    display: flex;
    max-width: 820px;
    width: 95%;
    margin: 0 auto;
    box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
    position: fixed;
    top: 50%;
    left: 50%;
    z-index: 9;
    background: #fff;
    transform: translate(-50%, -50%);
    overflow: hidden;
  }

  &.show {
    display: flex !important;
  }

  &.hide {
    display: none;
  }

  .content {
    padding: 55px 40px 40px;
    width: 100%;
  }

  .field {

    input,
    textarea {
      border: 1px solid #c8c4c4;
    }

    textarea {
      width: 100%;
      height: 136px;
      margin-bottom: 8px;
    }

    .name {
      color: #787878;
      font-size: 14px;
      padding-bottom: 6px;
      font-weight: 600;
    }

    .filtername {
      padding: 12px 20px;
      border: 1px solid #c8c4c4;
      margin-right: 15px;
      font-size: 14px;
      cursor: pointer;
      width: 100%;
      height: 47px;
      position: relative;
      margin-bottom: 15px;

      img {
        padding-right: 10px;
      }
    }

    .drop {
      display: none;
    }

    .selectOption {
      span {
        padding-right: 5px;
      }

      .selectMenu {
        font-size: 14px;
        color: #000;
        position: relative;
        padding-right: 25px;

        &.sort {
          &::before {
            content: "";
            background-image: url(/assets/images/angle-down.svg);
            position: absolute;
            right: 0px;
            top: 3px;
            width: 20px;
            height: 20px;
          }
        }
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        position: absolute;
        opacity: 0;
        box-shadow: 0px 2px 2px #d5dbde;
        background: #fff;
        width: 100%;
        left: 0px;
        top: 49px;
        z-index: -1;

        &.active {
          z-index: 3;
          opacity: 1;
          transition: all linear 0.2s;
          z-index: 1;
        }

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: -12px;
          width: 100%;
          height: 20px;
        }

        li {
          padding: 0px 15px;

          &:hover {
            color: #d2ab66;
          }
        }
      }
    }
  }

  .uploading {
    width: 120px;
    height: 120px;
    border: 1px solid #d5dbde;
    position: relative;
    margin-bottom: 30px;

    &::before {
      content: "";
      background-image: url(/assets/images/gold-scheme/upload.svg);
      width: 35px;
      height: 34px;
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      background-repeat: no-repeat;
      background-size: contain;
    }

    input[type="file"] {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      outline: none;
      opacity: 0;
    }
  }

  .submitBtn {
    text-align: right;

    .primary-button {
      max-width: 151px;
    }
  }

  p {
    text-align: center;
    font-size: 14px;
    margin-bottom: 15px;
  }

  .field {
    width: 100%;
  }

  input[type="text"],
  input[type="url"],
  input[type="tel"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  select,
  textarea,
  input[type="date"] {
    border: 1px solid #d5dbde;
    padding: 20px 16px;
  }

  input[type="tel"] {
    padding-left: 90px !important;
  }

  .close {
    position: absolute;
    right: 10px;
    top: 10px;
    cursor: pointer;
    opacity: unset;
    padding: 10px;
    z-index: 1;

    img {
      filter: invert(100%);
    }
  }
}

h1 {
  font-size: 44px;
}

// Responsive Design for Medium Screens
@media screen and (max-width: 992px) {
  .title {
    padding-bottom: 20px;

    h2 {
      margin-bottom: 10px;
    }
  }

  .specification {
    padding: 0;

    .container {
      padding-right: 0;
    }

    .box {
      img {
        width: 40px;
      }
    }
  }

  .topTrending {
    margin: 50px 0;

    h2 {
      margin-bottom: 20px;
    }

    .imageSection {
      grid-template-columns: 1fr;
      gap: 12px;
    }
  }

  .forMenWomen {
    flex-direction: column;

    .for {
      width: 100%;
      height: auto;
    }

    h2 {
      bottom: 4%;
    }
  }

  .trendingSection {
    padding: 50px 0;

    h2 {
      margin-bottom: 20px;
    }
  }

  .trendingSection .swiper,
  .brandsLove .swiper,
  .hotCategory .swiper,
  .clientSay .swiper {
    padding-bottom: 20px;
  }

  .brandsLove {
    padding: 50px 0 40px;
  }

  .bookGold {
    .content {
      width: 90%;
    }

    .image {
      object-fit: cover;
    }
  }

  .digitalWear {
    margin: 50px 0;
    background-size: cover;

    .digital {
      flex-wrap: wrap;
    }

    .content {
      width: 100%;
      margin-bottom: 50px;
      text-align: center;
    }

    .digitalImg {
      width: 100%;
    }
  }

  .clientSay {
    padding: 50px 0 0;
  }

  .hotCategory {
    padding-bottom: 50px;
  }

  .miSection {
    margin-top: 50px;
    flex-wrap: wrap;

    .leftImage {
      width: 100%;
      height: auto;
    }

    .rightContent {
      width: 100%;
      padding: 40px;
    }
  }

  .featured {
    margin-top: 50px;
    padding: 50px 0;

    .contents {
      flex-wrap: wrap;
    }

    .leftSection {
      width: 100%;
    }

    .rightSection {
      width: 100%;
    }
  }

  .collectionSec {
    padding-top: 50px;
    flex-wrap: wrap;

    .box {
      width: 100%;
    }

    .head {
      width: 100%;
      margin-bottom: 30px;

      h2 {
        font-size: 28px;
      }
    }
  }

  .special {
    margin-top: 50px;
  }

  .hotCategory .name {
    font-size: 25px;
  }

  .blogSec {
    padding-top: 50px;

    .blogSection {
      flex-wrap: wrap;

      .blogImage {
        aspect-ratio: 1.2;
        object-fit: cover;
      }

      .image {
        padding-bottom: 10px;
      }

      .text {
        font-size: 12px;
      }

      .blogs {
        width: 50%;
        padding-bottom: 20px;
      }

      .featuredIcon {
        width: 50px;
        top: 10px;
      }
    }
  }

  .WhatsGoing {
    padding-top: 50px;

    .head {
      font-size: 20px;
      padding-bottom: 20px;
    }

    .social {
      flex-direction: column;
      align-items: center;

      .socialMedia {
        width: 70%;
        padding-bottom: 30px;

        &:nth-child(2),
        &:nth-child(3) {
          width: 100%;
        }

        iframe {
          height: 312px;
        }
      }
    }
  }

  .connectSection {
    .box {
      padding: 17px 8px 10px;
    }
  }

  h1 {
    font-size: 30px;
  }

  .specification {
    .box {
      padding: 10px;
      width: 100%;

      img {
        width: 20px;
      }
    }

    .text {
      font-size: 12px;
    }
  }
}

// Responsive Design for Tablet Screens
@media screen and (max-width: 768px) {
  .bannerSection {
    .video-container {
      .mobile-video {
        display: block !important;
      }
  
      .web-video {
        display: none !important;
      }
    }
  }

  .testimonialPopup.show {
    display: flex;
    flex-direction: column;
    width: 100%;

    .leftSide {
      display: none;
    }

    .check {
      margin-bottom: 10px;
    }

    .rightSide {
      width: 100%;
      padding: 20px;
    }

    .box {
      max-width: 100%;
      width: 80%;
    }
  }

  .trendingSection {
    .container {
      padding-right: 0;
    }

    .slide {
      bottom: -10px;
    }
  }

  .forMenWomen {
    h4 {
      bottom: 27px;
    }

    .for {
      aspect-ratio: 1.21;
    }
  }

  .specification {
    display: none;
  }
}

// Responsive Design for Mobile Screens
@media screen and (max-width: 480px) {
  .perfectBride .leftSection .name {
    font-size: 12px;
  }

  .WhatsGoing .social .socialMedia {
    width: 100%;
  }

  // .offSection {
  //   // grid-template-columns: repeat(1, 1fr);
  //   gap: 12px; 
  // }

  .connectSection .connect {
    grid-template-columns: repeat(1, 1fr);
    gap: 15px;
  }

  h1 {
    font-size: 25px;
  }
}