import { ComponentFixture, TestBed } from '@angular/core/testing';

import { BuybackPolicyComponent } from './buyback-policy.component';

describe('BuybackPolicyComponent', () => {
  let component: BuybackPolicyComponent;
  let fixture: ComponentFixture<BuybackPolicyComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ BuybackPolicyComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(BuybackPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
