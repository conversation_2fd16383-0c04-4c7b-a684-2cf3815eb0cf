import {
  Component,
  ElementRef,
  OnDestroy,
  OnInit,
  ViewChild,
  HostListener,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  SimpleChanges,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Inject,
  PLATFORM_ID,
} from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

// Lazy load HLS.js
let HlsJS: any = null;

interface BannerItem {
  id?: number;
  name?: string;
  title: string;
  url?: string;
  poster?: string;
  thumbnail?: string;
  image?: string;
  redirection_type?: number;
  slug?: string;
  bannerName?: string;
  bannerCategory?: string;
  type?: 'video' | 'image';
}

@Component({
  selector: 'app-video-player',
  templateUrl: './video-player.component.html',
  styleUrls: ['./video-player.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VideoPlayerComponent implements OnInit, OnDestroy, OnChanges {
  @ViewChild('videoPlayerDesktop', { static: false })
  videoPlayerDesktop!: ElementRef<HTMLVideoElement>;

  @ViewChild('videoPlayerMobile', { static: false })
  videoPlayerMobile!: ElementRef<HTMLVideoElement>;

  @Input() bannerData: BannerItem[] = [];
  @Input() imageBase: string = '';
  @Input() autoSlideEnabled: boolean = true;
  @Input() slideInterval: number = 10000;

  @Output() bannerClick = new EventEmitter<BannerItem>();

  private hls: any = null;
  private hlsLoaded = false;
  private autoSlideInterval: any = null;
  private progressInterval: any = null;
  private startTime = 0;
  private intersectionObserver?: IntersectionObserver;

  isMobile = false;
  currentSlideIndex = 0;
  isLoading = false;
  error: string | null = null;
  progressPercentage = 0;
  nextSlide: BannerItem | null = null;
  isVisible = false;

  processedSources: BannerItem[] = [];

  constructor(
    private cdr: ChangeDetectorRef,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {}

  async ngOnInit(): Promise<void> {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    this.isMobile = window.innerWidth <= 768;
    this.processBannerData();

    // Set up intersection observer for lazy loading
    this.setupIntersectionObserver();

    // Initialize player only when component is visible
    setTimeout(() => {
      if (this.isVisible) {
        this.initializePlayer();
      }
    }, 100);
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['bannerData'] && !changes['bannerData'].firstChange) {
      this.processBannerData();
      if (this.isVisible) {
        setTimeout(() => this.initializePlayer(), 100);
      }
    }
  }

  ngOnDestroy() {
    this.destroyPlayer();
    this.stopAutoSlide();
    this.stopProgress();

    if (this.intersectionObserver) {
      this.intersectionObserver.disconnect();
    }
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyboardEvent(event: KeyboardEvent) {
    if (!this.isVisible) return;

    switch (event.key) {
      case 'ArrowLeft':
        this.previousSlide();
        break;
      case 'ArrowRight':
      case ' ':
        event.preventDefault();
        this.nextSlideAction();
        break;
      case 'p':
      case 'P':
        this.toggleAutoSlide();
        break;
    }
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    if (!isPlatformBrowser(this.platformId)) return;

    const wasMobile = this.isMobile;
    this.isMobile = window.innerWidth <= 768;

    if (
      wasMobile !== this.isMobile &&
      this.getCurrentSlide()?.type === 'video'
    ) {
      this.changeSlide();
    }
  }

  private setupIntersectionObserver(): void {
    if (
      !isPlatformBrowser(this.platformId) ||
      !('IntersectionObserver' in window)
    ) {
      this.isVisible = true;
      return;
    }

    this.intersectionObserver = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          this.isVisible = entry.isIntersecting;
          if (this.isVisible && !this.hlsLoaded) {
            this.initializePlayer();
          } else if (!this.isVisible) {
            this.pauseVideo();
          }
        });
      },
      { threshold: 0.1 }
    );

    // Observe the component element
    if (this.intersectionObserver) {
      this.intersectionObserver.observe(
        document.querySelector('app-video-player') || document.body
      );
    }
  }

  private processBannerData(): void {
    const defaultVideoSources: BannerItem[] = [
      {
        name: 'TT Devassy Collection',
        title: 'TT Devassy Jewellery Collection',
        url: 'assets/video/banner/output.m3u8',
        poster:
          'https://gziw1jnd2b.ufs.sh/f/5oCdTODIdQkJ3RscXA2m87dPh31O9U4wpY6KNfDvGRWjScCa',
        thumbnail: 'assets/images/banner.png',
        type: 'video',
      },
    ];

    const processedBanners = this.bannerData.map((banner) => ({
      ...banner,
      type: banner.url ? 'video' : ('image' as 'video' | 'image'),
      poster:
        banner.poster ||
        (banner.image ? `${this.imageBase}/${banner.image}` : ''),
      thumbnail:
        banner.thumbnail ||
        (banner.image ? `${this.imageBase}/${banner.image}` : ''),
    }));

    const hasVideos = processedBanners.some(
      (banner) => banner.type === 'video'
    );

    this.processedSources = hasVideos
      ? processedBanners
      : [defaultVideoSources[0], ...processedBanners];

    this.updateNextSlide();
  }

  private async loadHlsLibrary(): Promise<void> {
    if (this.hlsLoaded || !isPlatformBrowser(this.platformId)) return;

    try {
      if (!HlsJS) {
        HlsJS = await import('hls.js');
      }
      this.hlsLoaded = true;
    } catch (error) {
      console.error('Failed to load HLS.js:', error);
      this.error = 'Failed to load video library';
    }
  }

  async initializePlayer() {
    if (!this.isVisible) return;

    const currentSlide = this.getCurrentSlide();
    if (!currentSlide) return;

    if (currentSlide.type === 'video' && !this.areVideoElementsAvailable()) {
      setTimeout(() => this.initializePlayer(), 100);
      return;
    }

    this.isLoading = false;
    this.error = null;

    if (currentSlide.type === 'video') {
      await this.loadHlsLibrary();
      this.initializeVideoPlayer();
    } else {
      this.initializeImageSlide();
    }
  }

  private async initializeVideoPlayer() {
    if (!this.hlsLoaded || !isPlatformBrowser(this.platformId)) {
      this.error = 'HLS library not loaded';
      return;
    }

    const video = this.getCurrentVideoElement();
    if (!video) {
      this.error = 'Video player not available';
      return;
    }

    const currentSlide = this.getCurrentSlide();
    if (!currentSlide?.url) {
      this.error = 'Video URL not available';
      return;
    }

    this.isLoading = true;
    this.error = null;

    // Configure video element
    video.muted = true;
    video.loop = false;
    video.preload = 'metadata';
    video.poster = currentSlide.poster || '';

    const Hls = HlsJS.default || HlsJS;

    if (Hls && Hls.isSupported()) {
      this.hls = new Hls({
        enableWorker: true,
        lowLatencyMode: false,
        backBufferLength: 30,
        maxBufferLength: 60,
        maxMaxBufferLength: 120,
        startLevel: -1, // Auto quality selection
      });

      this.hls.loadSource(currentSlide.url);
      this.hls.attachMedia(video);
      this.setupHlsEvents(video);
    } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
      video.src = currentSlide.url;
      this.setupVideoEvents(video);
      this.tryAutoplay(video);
    } else {
      this.error = 'HLS not supported';
      this.isLoading = false;
    }
  }

  private initializeImageSlide() {
    this.isLoading = false;
    this.error = null;
  }

  private setupHlsEvents(video: HTMLVideoElement) {
    const Hls = HlsJS.default || HlsJS;

    this.hls.on(Hls.Events.MANIFEST_PARSED, () => {
      this.isLoading = false;
      this.error = null;
      this.tryAutoplay(video);
      this.cdr.markForCheck();
    });

    this.hls.on(Hls.Events.ERROR, (_event: any, data: any) => {
      if (data.fatal) {
        switch (data.type) {
          case Hls.ErrorTypes.NETWORK_ERROR:
            this.error = 'Network error';
            this.hls?.startLoad();
            break;
          case Hls.ErrorTypes.MEDIA_ERROR:
            this.error = 'Media error';
            this.hls?.recoverMediaError();
            break;
          default:
            this.error = 'Fatal error';
            setTimeout(() => this.nextSlideAction(), 2000);
            break;
        }
        this.cdr.markForCheck();
      }
    });

    this.setupVideoEvents(video);
  }

  private setupVideoEvents(video: HTMLVideoElement) {
    const events = [
      {
        name: 'ended',
        handler: () => !this.autoSlideEnabled && this.nextSlideAction(),
      },
      {
        name: 'loadstart',
        handler: () => {
          this.isLoading = true;
          this.cdr.markForCheck();
        },
      },
      {
        name: 'canplay',
        handler: () => {
          this.isLoading = false;
          this.cdr.markForCheck();
        },
      },
      {
        name: 'error',
        handler: () => {
          this.error = 'Failed to load video';
          this.isLoading = false;
          this.cdr.markForCheck();
        },
      },
    ];

    events.forEach(({ name, handler }) => {
      video.addEventListener(name, handler, { once: name === 'ended' });
    });
  }

  private async tryAutoplay(video: HTMLVideoElement) {
    try {
      await video.play();
    } catch (error) {
      console.log('Autoplay failed - user interaction required');
    }
  }

  private pauseVideo() {
    const video = this.getCurrentVideoElement();
    if (video && !video.paused) {
      video.pause();
    }
  }

  startAutoSlide() {
    if (!this.isVisible) return;

    this.stopAutoSlide();
    this.startProgress();
    this.autoSlideInterval = setInterval(() => {
      this.nextSlideAction();
    }, this.slideInterval);
  }

  stopAutoSlide() {
    if (this.autoSlideInterval) {
      clearInterval(this.autoSlideInterval);
      this.autoSlideInterval = null;
    }
  }

  startProgress() {
    this.stopProgress();
    this.startTime = Date.now();
    this.progressPercentage = 0;
    this.progressInterval = setInterval(() => {
      this.updateProgress();
    }, 100);
  }

  stopProgress() {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = null;
    }
    this.progressPercentage = 0;
  }

  updateProgress() {
    if (!this.autoSlideEnabled || !this.isVisible) return;
    const elapsed = Date.now() - this.startTime;
    this.progressPercentage = Math.min(
      (elapsed / this.slideInterval) * 100,
      100
    );
  }

  nextSlideAction() {
    this.currentSlideIndex =
      (this.currentSlideIndex + 1) % this.processedSources.length;
    this.changeSlide();
  }

  previousSlide() {
    this.currentSlideIndex =
      this.currentSlideIndex === 0
        ? this.processedSources.length - 1
        : this.currentSlideIndex - 1;
    this.changeSlide();
  }

  goToSlide(index: number) {
    if (
      index >= 0 &&
      index < this.processedSources.length &&
      index !== this.currentSlideIndex
    ) {
      this.currentSlideIndex = index;
      this.changeSlide();
    }
  }

  toggleAutoSlide() {
    this.autoSlideEnabled = !this.autoSlideEnabled;
    if (this.autoSlideEnabled && this.isVisible) {
      this.startAutoSlide();
    } else {
      this.stopAutoSlide();
      this.stopProgress();
    }
  }

  private async changeSlide() {
    this.error = null;
    this.stopAutoSlide();
    this.stopProgress();
    this.destroyPlayer();
    this.updateNextSlide();

    const currentSlide = this.getCurrentSlide();
    if (currentSlide?.type === 'video') {
      await this.loadHlsLibrary();
    }

    setTimeout(() => {
      this.initializePlayer();
      if (this.autoSlideEnabled && this.isVisible) {
        this.startAutoSlide();
      }
    }, 50);
  }

  private updateNextSlide() {
    const nextIndex =
      (this.currentSlideIndex + 1) % this.processedSources.length;
    this.nextSlide = this.processedSources[nextIndex];
  }

  destroyPlayer() {
    if (this.hls) {
      this.hls.destroy();
      this.hls = null;
    }

    [this.videoPlayerDesktop, this.videoPlayerMobile].forEach((videoRef) => {
      const video = videoRef?.nativeElement;
      if (video) {
        video.src = '';
        video.load();
      }
    });
  }

  getCurrentSlide(): BannerItem | null {
    return this.processedSources[this.currentSlideIndex] || null;
  }

  getCurrentVideo(): BannerItem {
    return this.getCurrentSlide() || this.processedSources[0];
  }

  isCurrentSlideVideo(): boolean {
    return this.getCurrentSlide()?.type === 'video';
  }

  isCurrentSlideImage(): boolean {
    return this.getCurrentSlide()?.type === 'image';
  }

  getCurrentImageUrl(): string {
    const currentSlide = this.getCurrentSlide();
    if (currentSlide?.type === 'image') {
      return currentSlide.image
        ? `${this.imageBase}/${currentSlide.image}`
        : currentSlide.poster || '';
    }
    return '';
  }

  onSlideClick() {
    const currentSlide = this.getCurrentSlide();
    if (currentSlide) {
      this.bannerClick.emit(currentSlide);
    }
  }

  onNextBannerClick() {
    this.nextSlideAction();
  }

  onRetryClick() {
    this.error = null;
    this.initializePlayer();
  }

  private areVideoElementsAvailable(): boolean {
    const desktopElement = this.videoPlayerDesktop?.nativeElement;
    const mobileElement = this.videoPlayerMobile?.nativeElement;

    return this.isMobile ? !!mobileElement : !!desktopElement;
  }

  private getCurrentVideoElement(): HTMLVideoElement | null {
    if (this.isMobile) {
      return this.videoPlayerMobile?.nativeElement || null;
    } else {
      return this.videoPlayerDesktop?.nativeElement || null;
    }
  }

  // Getters for template
  get totalSlides(): number {
    return this.processedSources.length;
  }

  get allSources(): BannerItem[] {
    return this.processedSources;
  }

  // Track by function for ngFor optimization
  trackByIndex(index: number): number {
    return index;
  }
}
