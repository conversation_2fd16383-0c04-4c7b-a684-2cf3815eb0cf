.bookAppointment {
  padding: 40px 0 100px;

  .form {
    display: flex;
    margin: 0 auto;
    width: 75%;
    justify-content: center;
    max-height: 820px;

    .leftSide {
      width: 50%;
    }

    .rightSide {
      padding: 30px 40px;
      border: 1px solid #BBBBBB;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      width: 50%;
    }

    input,
    select,
    textarea {
      border: 1px solid #BBBBBB;
    }

    select {
      -webkit-appearance: none;
      -moz-appearance: none;
      background-image: url("/assets/images/angle-down.svg");
      background-repeat: no-repeat;
      background-position: 98%;

    }

    .field span {
      font-size: 14px;
      color: #787878;
      font-weight: 500;
      margin-bottom: 6px;
    }

    .field.time {
      .timePicker {
        position: relative;
        &::after {
          content: "";
          position: absolute;
          width: 24px;
          height: 24px;
          z-index: -1;
          right: 10px;
          top: 10px;
          background-image: url(/assets/images/clock.svg);
          background-repeat: no-repeat;
        }
      }


      input {
        cursor: pointer;
        display: block;
        width: 100%;
        padding: 10px;
        background: transparent;
        font-size: 12px;
        &:focus-visible {
          outline: none;
        }
      }
    }

    .leftSide img {
      height: 100%;
      width: 100%;
      object-fit: cover;
    }
  }

  h1 {
    font-size: 44px;
    margin-bottom: 25px;
    @media screen and (max-width: 991px) {
      font-size: 30px;
      margin-bottom: 10px;
    }
  }
}

.date.ng-untouched {
  display: block;
  position: relative;

  &::after {
    content: "";
    position: absolute;
    width: 24px;
    height: 24px;
    z-index: -1;
    right: 10px;
    top: 10px;
    background-image: url(/assets/images/calander.svg);
    background-repeat: no-repeat;
  }
}
.datepicker-container.datepicker-default {
  width: 100%;
}


@media screen and (max-width:992px) {

  .bookAppointment {
    padding: 20px 0 40px;

    .form{
      .leftSide {
        display: none;
      }

      .rightSide {
        width: 100%;
      }
    }

  }
}
@media screen and (max-width:640px) {

  .bookAppointment {
    .form{
      width: 90%;
    }

  }
}
