import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import SwiperCore, { Swiper, SwiperOptions, Virtual, Navigation, FreeMode, Pagination } from 'swiper';
import { SwiperComponent } from 'swiper/angular';
import { apiEndPoints } from '../ApiEndPoints';
import { ToastrService } from 'ngx-toastr';
import { NgxSpinnerService } from 'ngx-spinner';
import { Meta } from '@angular/platform-browser';
SwiperCore.use([Virtual, FreeMode, Navigation, Pagination]);

@Component({
  selector: 'app-blog',
  templateUrl: './blog.component.html',
  styleUrls: ['./blog.component.scss']
})
export class BlogComponent implements OnInit {

  bannerMain: SwiperOptions = {
    spaceBetween: 0,
    slidesPerView: 1,
    freeMode: false,
    loop: true,
    watchSlidesProgress: true,
    navigation: true,
    initialSlide: 1,
    autoplay: {
      delay: 2000,
    },
  };
  blogData: any;
  featuredBlog: any;
  blogCategories: any;
  recentBlogs: any;
  isLoading: boolean = false

  constructor(private ApiService: ApiService, private toast: ToastrService, private spinner: NgxSpinnerService,
    private meta : Meta ) { }

  ngOnInit(): void {
    this.getBlogs()
    this.getBlogCategory()
    this.getRecentBlogs()
  }

  getBlogs() {
    // this.isLoading = true
    this.ApiService.postData(apiEndPoints.BLOGS, {}).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.blogData = res?.data;
        this.featuredBlog = this.blogData.find((blog: any) => blog.is_featured);
        this.updateMeta();
        // this.isLoading = false;
      } else this.toast.error(res?.message)
    });
  }

updateMeta(){
    for (let blog of this.blogData){
      this.meta.updateTag({name: 'title', content: blog?.blog_title});
      this.meta.updateTag({name: 'description', content: blog?.blog_description });
      this.meta.updateTag({property: 'og:image', content: blog?.blog_image});
      this.meta.updateTag({property: 'og:title', content: blog?.blog_title});
      this.meta.updateTag({property: 'og:description', content: blog?.blog_description});
      this.meta.updateTag({rel : 'canonical', href : 'https://ttdevassyjewellery.com/blog'});
    }
}

  getBlogCategory() {
    this.ApiService.getData(apiEndPoints.BLOG_CATEGORY).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.blogCategories = res?.data;
      }
    });
  }

  getRecentBlogs() {
    this.ApiService.getData(apiEndPoints.RECENT_BLOGS).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.recentBlogs = res?.data;
      }
    });
  }

}
