.adBanner {
    background-repeat: no-repeat;
    background-size: cover;
    height: 504px;
    position: relative;

    .content {
        max-width: 660px;
        color: #fff;
        padding: 0 30px;
        position: absolute;
        right: 30px;
        top: 50%;
        transform: translateY(-50%);
        text-align: center;

        h1 {
            color: #fff;
            text-transform: capitalize;
        }

    }

}

.perfectGiftSection {
    padding-top: 100px;

    .perfectGift {
        display: flex;
        flex-wrap: wrap;
        gap: 8%;
        justify-content: center;
    }

    .content {
        width: 46%;
        max-width: 498px;

        li {
            font-size: 14px;
            line-height: 20px;
            list-style: none;
        }
    }

    h6 {
        font-size: 18px;
        color: #D2AB66;
        margin-top: 30px;
        font-weight: 500;
        margin-bottom: 20px;
    }
}


.howItWorks {
    padding: 100px 0 10px;

    .title {
        padding-bottom: 40px;

        h2 {
            margin-bottom: 5px;
        }
    }

    .contents {
        display: flex;
        justify-content: space-around;
    }

    .box {
        text-align: center;
        width: 256px;
        padding: 0 15px;
    }

    .step {
        font-weight: 500;
        padding: 20px 0 10px;
    }

    p {
        font-size: 14px;
    }
}

.adBanner {
    &.bottom {
        height: 301px;
        margin: 100px 0;

        .content {
            right: auto;
            left: 50%;
            top: auto;
            bottom: 20px;
            transform: translateX(-50%);

            h1 {
                font-size: 30px;
                color: #000;
                text-transform: capitalize;
            }

            p {
                color: #000;
                font-size: 16px;
                max-width: 440px;
                margin: auto;
            }
        }

        .buy {
            width: 240px;
            margin: 90px 0 0;
            background-color: #D2AB66;
            padding: 15px 10px;
            color: #fff;

            &:hover {
                background-color: #000;
            }
        }
    }

}

@media screen and (max-width:992px) {
    .adBanner {
        .content {
            max-width: 100%;
            right: 25px;
        }

    }

    .perfectGiftSection {
        padding-top: 50px;

        .perfectGift {
            flex-wrap: wrap;
            row-gap: 40px;
        }
    }

    .howItWorks {
        padding: 50px 0 10px;

        .title {
            padding-bottom: 25px;
        }

    }

    .adBanner.bottom {
        margin: 50px 0;
    }
}

@media screen and (max-width:640px) {
    .howItWorks .contents {
        flex-wrap: wrap;

        .box {
            width: 100%;
            padding: 15px 0;
        }
    }

    .perfectGiftSection {
        .content {
            width: 100%;
        }

    }

    .adBanner.bottom .content {
        
        .buy {
            width: 200px;
            margin: 10px 0 0;
        }
    }
}