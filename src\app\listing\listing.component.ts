import { Activated<PERSON>out<PERSON>, Router } from '@angular/router';
import { environment } from './../../environments/environment.prod';
import { apiEndPoints } from './../ApiEndPoints';
import { ApiService } from './../Services/api.service';
import {
  Component,
  OnDestroy,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { Options } from '@angular-slider/ngx-slider';
import { NgxSpinnerService } from 'ngx-spinner';
import { CommonService } from '../Services/common.service';
import { Meta } from '@angular/platform-browser';
@Component({
  selector: 'app-listing',
  templateUrl: './listing.component.html',
  styleUrls: ['./listing.component.scss'],
})
export class ListingComponent implements OnInit, OnDestroy {
  @ViewChild('slider', { static: false }) slider: any;
  minValue: any = 1000;
  maxValue: any = 200000;
  options: Options = {
    floor: 0,
    ceil: 400000,
    step: 100,
  };
  filterList: any = false;
  category: any = false;
  sort: any = false;

  sortValues: any = [
    { title: 'Newest', value: 'newest' },
    { title: 'Price- High to Low', value: 'hightolow' },
    { title: 'Price- Low to High', value: 'lowtohigh' },
    { title: 'Oldest', value: 'oldest' },
  ];
  sortType: any;
  productData: any;
  data: any;
  imageBase: any;
  type: any;
  id: any;
  startPrice: any;
  endPrice: any;
  gold_purity: any;
  purityValues: any = [];
  productTypes: any = [
    { id: 1, name: 'Ring' },
    { id: 2, name: 'Neckalce' },
    { id: 3, name: 'Earring' },
  ];
  productTypesIds: any = [];
  pageNumber: any = 1;
  limit: any = 10;
  total: any;
  selectedSort: any;
  banners: any;
  selectedSortValue: any;
  isGoldPurity: boolean = false;
  showModal: boolean = false;
  shipFast: any = 0;
  isLoading: boolean = false;
  checkSessionStorage: boolean = false;
  windowRef: any = window;

  constructor(
    private apiService: ApiService,
    private route: ActivatedRoute,
    private router: Router,
    private commonService: CommonService,
    private meta: Meta
  ) {
    this.getStorageData();
  }

  getStorageData() {
    if (sessionStorage.getItem('detailPage')) {
      this.endPrice = sessionStorage.getItem('endPrice')
        ? sessionStorage.getItem('endPrice')
        : '';
      this.selectedSort = sessionStorage.getItem('selectedSort')
        ? sessionStorage.getItem('selectedSort')
        : '';
      this.purityValues = sessionStorage.getItem('purityValues')
        ? sessionStorage.getItem('purityValues')?.split(',')
        : [];
    }
  }

  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.type = params['type'];
      this.id = params['id'];
      this.routeType();
      this.purityValues = this.purityValues ? this.purityValues : [];
      this.minValue = 1000;
      this.maxValue = this.endPrice ? this.endPrice : 200000;
      this.getProductList();
    });
    this.getGoldPurity();
    this.sortType = this.selectedSort
      ? this.selectedSort
      : this.sortValues[0]['title'];
    this.imageBase = environment.imageBase;
  }

  getProductList() {
    // this.isLoading = true
    let data = {
      type: this.type,
      slug: this.id,
      limit: this.limit,
      pageNo: this.pageNumber,
      startprice: this.startPrice,
      endprice: this.endPrice,
      sortby: this.selectedSort,
      goldpurities: this.purityValues,
      nextdaydelivery: this.shipFast,
    };
    this.apiService
      .postData(apiEndPoints.PRODUCT_LIST, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.data = res?.Data;
          this.productData = res?.Data?.items;
          this.total = res?.Data?.totalCount;
          this.banners = this.data?.web_banners;
          this.updateMetaTags();
          // for (let i = 0; i < this.productData.length; i++) {
          //   const script = this.renderer.createElement('script');
          //   this.renderer.setAttribute(script, 'src', 'https://camweara.com/integrations/camweara_api.js');
          //   this.renderer.setAttribute(script, 'onload', `loadTryOnButton({
          //     psku:'0622439',
          //     page:'listing',
          //     company:'Ttdevassyjewellery',
          //     buynow:{enable:'true'},
          //     appendButton:{id:'digital-${i}'},
          //     MBappendButton:{id:'digital${i}'},
          //     styles:{
          //     tryonbutton:{backgroundColor:'white',color:'black',border:'1px solid black',borderRadius:'25px'},
          //     tryonbuttonHover:{backgroundColor:'black',color:'white',border:'none',},
          //     MBtryonbutton:{width:'100%',borderRadius:'25px'}
          //     },
          //     tryonButtonData:{text:'Digital wear',faIcon:'fa fa-camera'},
          //   });`);
          //   this.renderer.appendChild(document.body, script);
          // }
          // this.isLoading = false
        }
      });
  }

  updateMetaTags() {
    for (let product of this.productData) {
      this.meta.updateTag({ name: 'title', content: product?.name });
      this.meta.updateTag({
        name: 'description',
        content: `Check out ${product.name} for just $${product.price}`,
      });
      this.meta.updateTag({
        property: 'og:image',
        content: this.imageBase + product?.image,
      });
      this.meta.updateTag({ property: 'og:title', content: product?.name });
      this.meta.updateTag({
        property: 'og:description',
        content: `Check out ${product.name} for just $${product.price}`,
      });
      this.meta.updateTag({
        rel: 'canonical',
        href: 'https://www.ttdevassyjewellery.com/listing',
      });
    }
  }

  getGoldPurity() {
    this.apiService.getData(apiEndPoints.goldpurities).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.gold_purity = res?.Data;
      }
    });
  }

  pageChanged(event: any) {
    this.pageNumber = event;
    this.getProductList();
    window.scroll({
      top: 0,
      left: 0,
      behavior: 'smooth',
    });
  }

  applyFilter(type: any, title: any) {
    this.sortType = title;
    this.selectedSort = type;
    this.pageNumber = 1;
    sessionStorage.setItem('selectedSort', this.selectedSort);
    this.getProductList();
  }

  filterlist() {
    this.filterList = !this.filterList;
  }

  categoryList() {
    this.category = !this.category;
  }

  sortList() {
    this.sort = !this.sort;
  }

  close() {
    this.filterList = false;
    this.category = false;
    this.sort = false;
  }

  addToWishlist(id: any, isfavorite: any) {
    if (localStorage.getItem('isLogged')) {
      let data = { type: 0, productId: id };
      if (!isfavorite) data['type'] = 1;
      this.apiService
        .postData(apiEndPoints.WISHLIST, data)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) {
            this.getProductList();
            this.commonService.sendWishlistEvent();

            //Webengage Start
            const productDetails = this.productData.find(
              (product: any) => product.id === id
            );
            const event = isfavorite
              ? 'Removed From Wishlist'
              : 'Added to Wishlist';
            this.windowRef.webengage.track(event, {
              'Product ID': isfavorite ? productDetails?.id : productDetails?.slug,
              'Product Name': productDetails?.name,
              // 'L1 Category Name': '',
              // 'L1 Category ID': '',
              // 'L2 Category Name': '',
              // 'L2 Category ID': '',
              Availability: productDetails?.outOfStock == false ? true : false,
              isBundle: false,
              Quantity: 1,
              MRP: parseFloat(productDetails?.price),
              Currency: 'INR',
              Source: 'Website',
              'Page URL': window.location.href,
            });
            //Webengage End
          }
        });
    } else this.toggleModal();
  }

  priceRanges() {
    this.startPrice = this.minValue;
    this.endPrice = this.maxValue;
    this.pageNumber = 1;
    this.getProductList();
    sessionStorage.setItem('endPrice', this.endPrice);
  }

  purityFilter(value: any) {
    if (value && this.purityValues.indexOf(value) == -1) {
      this.purityValues.push(value);
    } else {
      this.purityValues.splice(this.purityValues.indexOf(value), 1);
    }
    this.pageNumber = 1;
    sessionStorage.setItem('purityValues', this.purityValues);
    this.getProductList();
  }

  mobilePurityFilter(value: any) {
    if (value && this.purityValues.indexOf(value) == -1) {
      this.purityValues.push(value);
    } else {
      this.purityValues.splice(this.purityValues.indexOf(value), 1);
    }
    this.pageNumber = 1;
    this.getProductList();
  }

  categoryFilter(id: any) {
    if (id && this.productTypesIds.indexOf(id) == -1) {
      this.productTypesIds.push(id);
    } else {
      this.productTypesIds.splice(this.productTypesIds.indexOf(id), 1);
    }
    // this.getProductList()
  }

  bannerRedirections(type: any, slug: any, title: string) {
    if (type == 1) this.router.navigate(['/detail', slug]);
    else if (type == 2 || type == 3 || type == 4) {
      if (type == 2) type = 'watches';
      else if (type == 3) type = 'collection';
      else if (type == 4) type = 'category';
      this.router.navigate(['/listing', type, slug]);
    }
    if (title == 'Zora') this.router.navigateByUrl('/zora-collection');

    if (type == 5 && slug == 3) {
      this.router.navigateByUrl('/virtual-shopping');
    } else if (type == 5 && slug == 4) {
      this.router.navigateByUrl('/zora-collection');
    } else if (type == 5 && slug == 5) {
      this.router.navigateByUrl('/divo-collection');
    } else if (type == 5 && slug == 6) {
      this.router.navigateByUrl('/poyem-collection');
    } else if (type == 5 && slug == 7) {
      this.router.navigateByUrl('/orlin-collection');
    } else if (type == 5 && slug == 8) {
      this.router.navigateByUrl('/book-appointment');
    } else if (type == 5 && slug == 2) {
      this.router.navigateByUrl('/gold-schemes');
    } else if (type == 5 && slug == 1) {
      this.router.navigateByUrl('/advance-booking');
    } else if (type == 5 && slug == 9) {
      this.router.navigateByUrl('/gift-voucher');
    } else if (type == 5 && slug == 10) {
      this.router.navigateByUrl('/digital-gold');
    } else if (type == 5 && slug == 11) {
      this.router.navigateByUrl('/about');
    }
  }

  applyFilterMob() {
    this.close();
    this.pageNumber = 1;
    this.getProductList();
  }

  clearAll() {
    this.close();
    this.purityValues = [];
  }

  showGoldPurity(type: any) {
    if (type == 'purity') {
      this.isGoldPurity = true;
    } else {
      this.isGoldPurity = false;
    }
  }

  toggleModal() {
    this.commonService.sendClickEvent();
    this.showModal = true;
  }

  shipFilter() {
    if (this.shipFast == 0) {
      this.shipFast = 1;
      this.getProductList();
    } else {
      this.shipFast = 0;
      this.getProductList();
    }
  }

  routeType() {
    if (this.type == 'watches') this.type = 2;
    else if (this.type == 'collection') this.type = 3;
    else if (this.type == 'category') this.type = 4;
  }

  onScroll() {
    if (this.data?.totalCount > 10) {
      this.limit += 10;
      this.getProductList();
    }
  }

  ngOnDestroy(): void {
    sessionStorage.removeItem('detailPage');
  }
}
