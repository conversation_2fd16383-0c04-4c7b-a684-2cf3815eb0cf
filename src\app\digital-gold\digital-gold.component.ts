import { Component, OnInit } from '@angular/core';
import { ApiService } from '../Services/api.service';
import { apiEndPoints } from '../ApiEndPoints';
import { environment } from 'src/environments/environment.prod';

@Component({
  selector: 'app-digital-gold',
  templateUrl: './digital-gold.component.html',
  styleUrls: ['./digital-gold.component.scss']
})
export class DigitalGoldComponent implements OnInit {
  data: any;
  imageBase: any = environment.imageBase;

  constructor(private apiService: ApiService) { }

  ngOnInit(): void {
    this.apiService.getData(apiEndPoints.digitalgold_page).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.data = res?.Data
      }
    })
  }

}
