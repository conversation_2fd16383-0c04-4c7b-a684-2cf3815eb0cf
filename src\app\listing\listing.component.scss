.smallBanner {
  img {
    width: 100%;
  }
}

.heading {
  display: flex;
  align-items: center;
  margin-bottom: 30px;

  h1 {
    font-size: 44px;
    padding-right: 20px;
    margin: 0;
    text-transform: capitalize;
  }

  h2 {
    padding-right: 20px;
    margin: 0;
    text-transform: capitalize;
  }
}


.filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;

  .leftSide {
    display: flex;
    width: 50%;
    flex-wrap: wrap;
  }

  .rightSide {
    display: flex;
    width: 50%;
    justify-content: flex-end;

    .filtername {
      display: flex;
      align-items: center;
    }
  }

  .filtername {
    padding: 12px 20px;
    border: 1px solid #D5DBDE;
    margin-right: 15px;
    font-size: 14px;
    cursor: pointer;
    width: max-content;
    position: relative;
    margin-bottom: 10px;
    img {
      padding-right: 10px;
    }
  }

  .drop {
    display: none;
  }

  .selectOption {

    span {
      padding-right: 5px;
    }

    .selectMenu {
      font-size: 14px;
      color: #000;
      position: relative;
      padding-right: 25px;

      &.sort {
        &::before {
          content: "";
          background-image: url(/assets/images/angle-down.svg);
          position: absolute;
          right: 0px;
          top: 3px;
          width: 20px;
          height: 20px;
        }
      }
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      position: absolute;
      opacity: 0;
      box-shadow: 0px 2px 2px #D5DBDE;
      background: #fff;
      top: 47px;
      width: 0;
      left: 0px;

      &:before {
        content: "";
        position: absolute;
        left: 0;
        top: -12px;
        width: 100%;
        height: 20px;

      }

      li {
        padding: 0px 15px;
        &:hover{
          color: #D2AB66;
        }
      }
    }


    &:hover {
      ul {
        z-index: 3;
        opacity: 1;
        width: max-content;
        transition: all linear .5s;

      &.priceRange {
        width: 200px;
        padding-bottom: 15px;
      }




      }
    }
  }
  .selectPurity {
    padding: 15px;
    .form-group{
      margin: 10px 15px 10px 15px;
    }
  input {
    padding: 0;
    height: initial;
    width: initial;
    margin-bottom: 0;
    display: none;
    cursor: pointer;
  }

  label {
      position: relative;
      cursor: pointer;
      font-weight: 400;
      font-size: 13px;
      display: flex;
      flex-direction: column;
     justify-content: center;

  }

  label:before {
      content:'';
      -webkit-appearance: none;
      background-color: transparent;
      border: 1px solid #D5DBDE;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
      padding: 10px;
      display: inline-block;
      position: absolute;
      vertical-align: middle;
      cursor: pointer;
      margin-right: 10px;
  }
  input:checked + label:before {
      background: #D2AB66;
      border: 1px solid #D2AB66;
  }
  input:checked + label:after {
      content: '';
      display: block;
      position: absolute;
      top: 2px;
      left: 8px;
      width: 6px;
      height: 14px;
      border: solid #fff;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
  }
  input + label .type{
      display: none;
  }
  input:checked + label .type{
      display: block;

  }
  textarea{
      border: 1px solid #D5DBDE;
      margin-top: 20px;
  }
  .add{
      padding-left: 35px;
      padding-top: 2px;

  }

}

}

.listingSection {
  margin-bottom: 80px;
  padding-top: 40px;
}

.productsListing {
  display: flex;
  flex-wrap: wrap;
  margin-right: -1%;

  .items {
    text-align: center;
    background: #FFFFFF;
    box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.06);
    margin: 0px 1% 26px 0;
    position: relative;
    width: 24%;
    height: max-content;
    position: relative;

    &:hover {
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);

      .productImage {
        img {
          transform: translateY(-10px);
          transition: ease .7s;
        }
      }
    }

    .productImage {
      padding: 30px 30px 0;
      overflow: hidden;
      position: relative;
      margin-bottom: 20px;

      img {
        transition: ease .9s;
        max-height: 206px;
        object-fit: cover;
      }

      .shipFaster {
        position: absolute;
        bottom: 0;
        font-size: 14px;
        left: 0;
        background: #FF8A1E;
        color: #fff;
        padding: 6px 16px 6px 10px;
        border-radius: 0px 3px 3px 0px;

        &:after {
          background: #fff;
          content: "";
          height: 39px;
          position: absolute;
          right: -8px;
          top: 0;
          transform: skew(-13deg);
          width: 12px;
        }

      }
    }
  }

  .productName {
    font-size: 14px;
    padding: 0 30px 15px;
    line-height: 20px;
    margin-bottom: 15px;
  }

  .price {
    font-size: 14px;
    font-weight: 600;
    padding: 0 30px 30px;
    display: flex;
    justify-content: center;
    align-items: center;

    .offer {
      padding: 0 5px;
    }

    .old {
      text-decoration: line-through;
      padding: 0 5px;
      color: #B3B3B3;
      font-size: 13px;
    }
  }

  .ar {
    position: absolute;
    top: 50px;
    width: 23px;
    left: 40px;
    cursor: pointer;
    z-index: 2;
  }

  .wishlist {
    z-index: 2;
    position: absolute;
    right: 40px;
    display: flex;
    top: 50px;
    cursor: pointer;

    .fav-gold {
      display: none;
    }


    &.active .fav-gold {
      display: block;
    }

    &.active .fav {
      display: none;
    }
  }

  .view {
    border: 1px solid #1D1D1D;
    padding: 10px 20px;
    width: 80%;
    margin-bottom: 24px;
    font-size: 14px;
    font-weight: 600;
  }

  .ads {
    width: 49%;
    margin-bottom: 26px;
    max-height: 437px;
    object-fit: cover;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.adBanner {
  margin: 80px 0;
  display: block;

  img {
    width: 100%;
  }
}


.mobileFilter {
  display: none;
  justify-content: space-around;
  align-items: center;
  position: fixed;
  background: #fff;
  width: 100%;
  box-shadow: 2px -2px 12px rgb(0 0 0 / 6%);
  bottom: 0;
  z-index: 3;
  font-size: 14px;
  .filter {
    padding: 5px 10px;
    margin: 15px 0;
    display: flex;
    justify-content: center;
    width: 33.33%;
    span {
        padding-left: 10px;
    }
  }
  .filter.category, .filter.sort {
      border-left: 2px solid #EFEFEF;
  }
}
.filterList.view {
  position: fixed;
  width: 100%;
  height: 100%;
  background: #fff;
  top: 0;
  background: #fff;
  left: 0;
  z-index: 999;
  font-size: 14px;
  .head {
    font-size: 17px;
    font-weight: 600;
    padding: 20px;
    display: flex;
    height: 70px;
  }
  .section{
    display: flex;
    border-top:1px solid #EFEFEF;
    height: calc(100% - 150px);
    overflow: scroll;
    font-size: 14px;
  }

  .back {
      padding-right: 10px;
  }

  .leftSide {
      width: 130px;
      height: max-content;
      border-right: 1px solid #EFEFEF;
      .filterName {
        padding: 15px 20px;
        border-bottom: 1px solid #EFEFEF;
        &.active, &:hover{
          color: #D2AB66;
        }
        .img {
          width: 78px;
          height: 78px;
          object-fit: cover;
          overflow: hidden;
          border-radius: 50%;
        }

        .name {
            text-align: center;
            padding-top: 5px;
        }
    }
  }

  .rightSide {
      width: calc(100% - 130px);
      padding: 15px;

    input {
      padding: 0;
      height: initial;
      width: initial;
      margin-bottom: 0;
      display: none;
      cursor: pointer;
    }

    label {
        position: relative;
        cursor: pointer;
        font-weight: 400;
        font-size: 13px;
        display: flex;
        flex-direction: column;
    }

    label:before {
        content:'';
        -webkit-appearance: none;
        background-color: transparent;
        border: 1px solid #D5DBDE;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05), inset 0px -15px 10px -12px rgba(0, 0, 0, 0.05);
        padding: 10px;
        display: inline-block;
        position: absolute;
        vertical-align: middle;
        cursor: pointer;
        margin-right: 10px;
    }
    input:checked + label:before {
        background: #D2AB66;
        border: 1px solid #D2AB66;
    }
    input:checked + label:after {
        content: '';
        display: block;
        position: absolute;
        top: 2px;
        left: 8px;
        width: 6px;
        height: 14px;
        border: solid #fff;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
    input + label .type{
        display: none;
    }
    input:checked + label .type{
        display: block;

    }
    textarea{
        border: 1px solid #D5DBDE;
        margin-top: 20px;
    }
    .add{
        padding-left: 35px;
        padding-top: 2px;

    }

  }
  .applyFilter {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
    padding: 10px 20px;
    background: #fff;
    box-shadow: 0px -4px 10px -2px rgba(0, 0, 0, 0.05);
    height: 80px;
    .clear {
        width: 30%;
        text-align: center;
    }
    .apply {
        width: 70%;
    }

    .primary-button.golden {
        border-radius: 7px;
    }
  }
  .drop {
    position: absolute;
    right: 20px;
    top: 20px;
  }

  [type="radio"]:checked,
  [type="radio"]:not(:checked) {
      position: absolute;
      left: -9999px;
  }
  [type="radio"]:checked + label,
  [type="radio"]:not(:checked) + label
  {
      position: relative;
      padding-right: 38px;
      padding-left: 0;
      padding-top: 2px;
      cursor: pointer;
      line-height: 20px;
      display: inline-block;
      color: #666;
  }
  [type="radio"]:checked + label:before,
  [type="radio"]:not(:checked) + label:before {
      content: '';
      position: absolute;
      right: 0;
      top: 0;
      width: 25px;
      height: 25px;
      border: 2px solid #D2AB66;
      border-radius: 100%;
      background: #fff;
  }
  [type="radio"]:checked + label:after,
  [type="radio"]:not(:checked) + label:after {
      content: '';
      width: 13px;
      height: 13px;
      background: #D2AB66;
      position: absolute;
      top: 6px;
      right: 6px;
      border-radius: 100%;
      -webkit-transition: all 0.2s ease;
      transition: all 0.2s ease;
  }
  [type="radio"]:not(:checked) + label:after {
      opacity: 0;
      -webkit-transform: scale(0);
      transform: scale(0);
  }
  [type="radio"]:checked + label:after {
      opacity: 1;
      -webkit-transform: scale(1);
      transform: scale(1);
  }
  .radioBtn {
      display: flex;
      flex-direction: column;
      width: 100%;
      .checkbox+.checkbox, .radio {
          margin-top: 10px;
      }
      .radio {
          margin: 0 50px 0 0;
          color: #000;
          font-size: 14px;
          width: 100%;
      }

      label.radio-label {
          color: #000;
          width: 100%;
      }
  }
  &.sorting {
    height: 70%;
    top: auto;
    border-radius: 12px;
    bottom: 0;
    box-shadow: 4px -2px 11px rgb(0 0 0 / 6%);
    .radio {
      padding: 15px 20px;
      border-top: 1px solid #EFEFEF;
  }
  }

}





@media screen and (max-width:1560px){
  .filters .filtername {
    padding: 6px 8px;
    margin-right: 3px;
  }
}
@media screen and (max-width:1200px){

  .mobileFilter {
    display: flex;
  }
  .listingSection .filters{display: none;}
  .productsListing .items {
    width: 32.33%;
  }
  .productsListing .ads{
    width: calc(100% - 33.33%);
    padding-right: 1%;
  }
}
@media screen and (max-width:992px){
  .productsListing .items {
    width: 49%;
  }
  .productsListing .ads{
    width: calc(100% - 50%);
  }
  .listingSection {
    margin-bottom: 40px;
  }

  .heading{
    h1{
      font-size: 30px;
    }
  }
}
@media screen and (max-width:640px){
  .productsListing .items {
    width: 100%;
    margin-right: 0;
  }
  .productsListing .ads{
    width: 100%;
  }
}

@media screen and (max-width:480px){
  .heading{
    h1{
      font-size: 25px;
    }
  }
}
