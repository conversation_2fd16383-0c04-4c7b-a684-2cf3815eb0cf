.booking {
  padding: 40px 0 100px;

  h1 {
    font-size: 44px;
    margin-bottom: 25px;

    @media screen and (max-width: 992px) {
      font-size: 30px;
      margin-bottom: 10px;
    }

    @media screen and (max-width: 480px) {
      font-size: 25px;
    }

  }

  .paging {
    display: flex;
    justify-content: center;


    .number {
      background: #D6D6D6;
      width: 26px;
      height: 26px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      margin-right: 13px;
      color: #fff;
      justify-content: center;
    }

    .numbering {
      display: flex;
      flex-wrap: wrap;
      font-size: 14px;
      align-items: center;
      position: relative;
      span:nth-child(2){
        width: calc(100% - 40px);
      }
    }

    .number.active {
      background: #D0A962;
    }

    .numbering:first-child {
      padding-right: 130px;
    }

    .numbering:first-child::before {
      content: "";
      position: absolute;
      right: 30px;
      top: 13px;
      border-top: 1.5px dashed #E1E1E1;
      width: 20%;
      height: 1px;
    }
  }
}




.bookingForm {
  margin: 60px auto;
  max-width: 774px;

  .head {
    text-align: center;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 30px;
  }

  .sections {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin-bottom: 30px;
  }

  .leftSide {
    width: 49%;
  }

  .rightSide {
    width: 49%;

  }

  .field {
    .leftSide {
      display: flex;
      width: 50%;
    }

    input,
    textarea {
      border: 1px solid #D5DBDE;
    }

    textarea {
      width: 100%;
      height: 140px;
      margin-bottom: 10px;
    }

    .rightSide {
      display: flex;
      width: 50%;
      justify-content: flex-end;

      .filtername {
        display: flex;
        align-items: center;
      }
    }

    .name {
      color: #787878;
      font-size: 14px;
      padding-bottom: 6px;
    }

    .filtername {
      padding: 12px 20px;
      border: 1px solid #D5DBDE;
      margin-right: 15px;
      font-size: 14px;
      cursor: pointer;
      width: 100%;
      height: 49px;
      position: relative;
      margin-bottom: 20px;

      img {
        padding-right: 10px;
      }
    }

    .drop {
      display: none;
    }

    .selectOption {

      span {
        padding-right: 5px;
      }

      .selectMenu {
        font-size: 14px;
        color: #000;
        position: relative;
        padding-right: 25px;

        &.sort {
          &::before {
            content: "";
            background-image: url(/assets/images/angle-down.svg);
            position: absolute;
            right: 0px;
            top: 3px;
            width: 20px;
            height: 20px;
          }
        }
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;
        position: absolute;
        opacity: 0;
        box-shadow: 0px 2px 2px #D5DBDE;
        background: #fff;
        width: 100%;
        left: 0px;
        top: 49px;
        z-index: -1;

        &.active {
          z-index: 3;
          opacity: 1;
          transition: all linear .2s;
          z-index: 1;
        }

        &:before {
          content: "";
          position: absolute;
          left: 0;
          top: -12px;
          width: 100%;
          height: 20px;

        }

        li {
          padding: 0px 15px;

          &:hover {
            color: #D2AB66;
          }
        }
      }

    }
  }
   .text-danger {
    bottom: -3px;
}
  .country {
    position: relative;
    select {
      position: absolute;
      left: 0;
      width: 80px;
      margin-bottom: 0;
      height: 47px;
      top: 0;
      z-index: 1;
      border: none !important;
      padding: 0 10px !important;
  }

  input[type=number] {
    padding-left: 90px !important;
  }
  }


  .primary-button {
    max-width: 278px;
  }

  .box {
    flex-direction: column;
    display: flex;
    padding: 24px 30px;
    justify-content: center;
    border: 1px dashed #C9C9C9;
    margin-top: 27px;
  }

  .rateOn {
    font-size: 16px;
    padding-bottom: 5px;


  }

  .rt {
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 10px;
  }
  .box.finalDetails {
    display: flex;
    margin-bottom: 60px;
    flex-wrap: wrap;
    flex-direction: row;
    justify-content: flex-start;
    .rates {
      width: 50%;
  }
  }

.hereBy {
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: flex-end;

  input[type="checkbox"] {
    margin-right: 10px;
    margin-top: 1px;
  }
  a{
    color: #D0A962;
    padding-left: 4px;
  }
}
.buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  .primary-button{
    margin: 0 10px;
  }
}
}

.helpiline {
  margin-top: 100px;
  background: #D2AB66;
  text-align: center;
  padding: 15px 0;
  font-size: 25px;
  color: #fff;


  .content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
  }

  a {
    display: flex;
    align-items: center;
    color: #fff;
    margin-left: 30px;

    img {
      padding-right: 12px;
    }
  }
}



@media screen and (max-width:992px) {
  .booking  {
    padding: 30px 0;
    .paging  {
      flex-direction: column;
      max-width: 250px;
      margin: 0 auto;
      .numbering:first-child {
        padding-right: 0;
        padding-bottom: 50px;
      }

      .numbering:first-child::before {
        border-left: 1.5px dashed #E1E1E1;
        width: 1px;
        height: 30px;
        left: 13px;
        top: 35px;
      }
    }

  }

  .bookingForm {
    margin: 50px auto 30px;
    .leftSide {
      width: 100%;
    }
    .rightSide {
        width: 100%;
    }
    .head {
      font-size: 20px;
    }
  }

  .helpiline {
    margin-top: 20px;
    .content {
      flex-wrap: wrap;
      font-size: 12px;
    }

    a {
      margin-left: 20px;
      img {
        width: 27px;
      }
    }
}

}
@media screen and (max-width:768px) {
  .helpiline {
    margin-top: 0px;
    font-size: 17px;
  }
}
