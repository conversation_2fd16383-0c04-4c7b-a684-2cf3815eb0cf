<div
  class="adBanner"
  [ngStyle]="{ 'background-image': 'url(' + firstBanner + ')' }"
>
  <div class="content">
    <h1>{{ giftVoucherDetails?.top_banner?.title }}</h1>
  </div>
</div>

<div class="perfectGiftSection">
  <div class="container">
    <div class="title">
      <h2>{{ giftVoucherDetails?.section_2_title }}</h2>
    </div>
    <div class="perfectGift">
      <div class="content" *ngFor="let gift of giftVoucherDetails?.giftcards">
        <img src="{{ imageBaseUrl }}/{{ gift?.image }}" alt="gift voucher image" />
        <h6>{{ gift?.title }}</h6>

        <ul>
          <li [innerHTML]="gift?.terms"></li>
        </ul>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <div class="howItWorks">
    <div class="title">
      <h2>The perfect gift for any moment</h2>
    </div>
    <div class="contents">
      <div class="box">
        <img src="/assets/images/gift-voucher/explore.png" alt="icon" />
        <div class="step">Explore endless play</div>
        <p>Simply login using your mobile number.</p>
      </div>
      <div class="box">
        <img src="/assets/images/gift-voucher/no-fee.png" alt="icon" />
        <div class="step">No fees, No worries</div>
        <p>Select your occasion and preferred amount .</p>
      </div>
      <div class="box">
        <img src="/assets/images/gift-voucher/pay.png" alt="icon" />
        <div class="step">Pay head</div>
        <p>
          Do the payment and gift your card to the loved ones.
        </p>
      </div>
    </div>
  </div>
</div>

<div class="container">
  <div
    (click)="
      bannerRedirections(
        giftVoucherDetails?.bottom_banner?.redirection_type,
        giftVoucherDetails?.bottom_banner?.redirection_id,
        giftVoucherDetails?.bottom_banner?.title
      )
    "
    class="adBanner bottom"
    [ngStyle]="{ 'background-image': 'url(' + secondBanner + ')' }"
  >
    <div class="content">
      <h1>{{ giftVoucherDetails?.bottom_banner?.title }}</h1>
      <p>
        {{ giftVoucherDetails?.bottom_banner?.sub_title }}
      </p>
      <a routerLink="/gift-cards" class="buy">Buy Now</a>
    </div>
  </div>
</div>
