<div class="loader" *ngIf="isLoading">
  <img src="/assets/images/Tt-Loader.gif">
</div>

<div class="orderDetail">
  <div class="container">
    <div class="head">My Order </div>
    <div class="contentSec">
      <div class="sideBar">
        <div class="field">Delivery Address</div>
        <div class="address" *ngIf="data?.address_available == 1">
          <div>{{deliveryAddress?.name}}</div>
          <div>{{deliveryAddress?.address}}</div>
          <div>{{deliveryAddress?.city}}-{{deliveryAddress?.pincode}}</div>
          <div>{{deliveryAddress?.state}}, {{deliveryAddress?.country}} </div>
          <div>Mobile Number : {{deliveryAddress?.mobile}}</div>
        </div>
        <div class="line"></div>
        <div class="field">Billing Address</div>
        <div class="address" *ngIf="data?.address_available == 1">
          <div>{{billingAddress?.name}}</div>
          <div>{{billingAddress?.address}}</div>
          <div>{{billingAddress?.city}}-{{billingAddress?.pincode}}</div>
          <div>{{billingAddress?.state}}, {{billingAddress?.country}} </div>
          <div>Mobile Number : {{billingAddress?.mobile}}</div>
        </div>
        <div class="line"></div>
        <div class="field">Delivery Details</div>
        <div class="address">
          Order Id : {{productdata?.Order_id}}
        </div>
        <!-- <div class="line"></div> -->
        <!-- <div class="field">Payment Details</div>
          <div class="address">
            {{data?.price_details?.key}}: ₹ {{data?.price_details?.value}}
          </div> -->
        <div class="amountTotal">
          <div class="line"></div>
          <div class="field">Amount</div>
          <div class="amount">
            <div class="d-flex" *ngFor="let item of data?.amount_data">
              <span>{{item?.label}} : </span>
              <span>₹ {{item?.amount}}</span>
            </div>
          </div>
          <div class="primary-button golden mt-5" *ngIf="data?.payment_possible == 1" (click)="payBalance()">Pay Balance
          </div>
        </div>
      </div>
      <!-- <form *ngIf="!checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
        action="{{ccAvenueUrl}}?command=initiateTransaction&enc_request={{encRequest}}&access_code={{accessCode}}&request_type=XML&response_type=XML&version=1.1">
        <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
        <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
      </form> -->
      <!-- <form *ngIf="checkDomain" #form ngNoForm id="nonseamless" method="post" name="redirect"
      action="{{ccAvenueBeta}}?command=initiateTransaction">
      <input type="hidden" id="encRequest" name="encRequest" [ngModel]="encRequest">
      <input type="hidden" name="access_code" id="access_code" [ngModel]="accessCode">
    </form> -->
      <div class="content">
        <div class="orders">
          <span class="image"><img src="{{imageBase}}/{{productdata?.thumbnail}}" alt="product image"></span>
          <div class="orderDetails">
            <div class="orderId">Order Id : {{productdata?.Order_id}}</div>
            <div class="item">{{productdata?.name}}</div>
            <div class="price">₹{{productdata?.price}}</div>
          </div>
          <div class="button" *ngIf="data?.cancellation_possible == 1" (click)="cancelOrder()">Cancel item</div>
        </div>
        <div class="orderedStatus">
          <div class="statusBar">
            <div class="sections"
              [ngClass]="{'active': item.is_active == 1,'cancelled' : item.slug == 'cancelled' || item.slug == 'returned'}"
              *ngFor="let item of data?.statuses">
              <div class="text">{{item?.name}}</div>
              <div class="line"></div>
              <div class="date">
                <div *ngIf="item.date != null">{{item?.date}}</div>
                <div *ngIf="item.time != null">{{item?.time}}</div>
              </div>
            </div>
          </div>
          <div class="bottom">
            <div class="itemStatus">
              <!-- <div> Your item has been Assembling </div> -->
              <div class="return" (click)="returnOrder()" *ngIf="data?.return_possible == 1"><img
                  src="\assets\images\my-account\return.svg" alt="return"> Return</div>
            </div>
            <div class="statusAs">
              <div class="stat">
                <!-- <span class="color"></span>
                  <div class="rightSide">
                    <div class="date">Delivery Expected by Feb 2 2022</div>
                  </div> -->
              </div>
            </div>
          </div>
        </div>
        <div class="cartPage" *ngIf="isAddress">
          <div class="contentSection">
            <div class="deliveryDetail" [ngClass]="deliveryShow?'show':'hide'">
              <div class="head">Delivery Address <div class="addNew" (click)="add()"> <img
                    src="\assets\images\my-account\add.svg" alt="add icon" style="padding-right: 4px;"> Add New</div>
              </div>
              <ng-container *ngFor="let address of addressData">
                <div class="box">
                  <div class="address">
                    <input type="radio" id="{{address.id}}" name="radio-group" [checked]="address?.is_default == 1"
                      (change)="setDefaultAddress(address.id)">
                    <label for="{{address.id}}" class="name">{{address?.name}}</label>
                    <div>{{address?.address}}</div>
                    <!-- <div>Vengoor</div> -->
                    <div>{{address?.city}}-{{address?.pincode}}</div>
                    <div>{{address?.state}}, {{address?.country}} </div>
                    <div>Mobile Number : {{address?.mobile}}</div>
                  </div>
                  <div class="edit" (click)="editAddress(address?.id)"> <img src="\assets\images\my-account\edit.svg"
                      alt="edit icon" style="padding-right: 4px;">Edit Address </div>
                  <div class="delete" (click)="deleteAddres(address?.id)"> <img
                      src="\assets\images\my-account\delete.svg" alt="delete icon"></div>
                </div>
              </ng-container>
              <div class="head">Billing Address
                <div class="sameAs">
                  <input type="checkbox" id="instruction" [(ngModel)]="sameAsDelivery"
                    (ngModelChange)="setBillingAddress()">
                  <label for="instruction">
                    <div class="add">Same As Delivery Address </div>
                  </label>
                </div>
              </div>
              <div class="box">
                <div class="form">
                  <form [formGroup]="billingForm">
                    <div class="twoSection">
                      <div class="field">
                        <span>Full Name</span>
                        <input type="text" formControlName="name" />
                        <span *ngIf="bf.name.invalid && bf.name.touched">
                          <span class="text-danger" *ngIf="bf.name.errors?.required"> Name is required </span>
                        </span>
                      </div>
                      <div class="field">
                        <span>country</span>
                        <select name="" id="" formControlName="country" (change)="selectedCountry($event,'add')">
                          <option [value]="country.id" *ngFor="let country of countries">
                            {{country.name}}
                          </option>
                        </select>
                        <span *ngIf="bf.country.invalid && bf.country.touched">
                          <span class="text-danger" *ngIf="bf.country.errors?.required"> Country is required </span>
                        </span>
                      </div>
                    </div>
                    <div class="twoSection">
                      <div class="field">
                        <span>pincode</span>
                        <input type="text" formControlName="pincode" />
                        <span *ngIf="bf.pincode.invalid && bf.pincode.touched">
                          <span class="text-danger" *ngIf="bf.pincode.errors?.required"> Pincode is required </span>
                        </span>
                        <span class="text-danger" *ngIf="bf.pincode.errors?.pattern">Please Enter Valid Pincode </span>
                      </div>
                      <div class="field">
                        <span>state</span>
                        <select name="" id="" formControlName="state">
                          <option [value]="state.id" *ngFor="let state of states">
                            {{state.name}}
                          </option>
                        </select>
                        <span *ngIf="bf.state.invalid && bf.state.touched">
                          <span class="text-danger" *ngIf="bf.state.errors?.required"> State is required </span>
                        </span>
                      </div>
                    </div>

                    <div class="twoSection">
                      <div class="field">
                        <div style="position: relative;">
                          <span>city</span>
                          <input type="text" formControlName="city" />
                          <span *ngIf="bf.city.invalid && bf.city.touched">
                            <span class="text-danger" *ngIf="bf.city.errors?.required"> City is required </span>
                          </span>
                        </div>
                        <div style="position: relative;height: 99px;">
                          <span>Mobile Number</span>
                          <div class="country">
                            <select name="" id="" formControlName="code">
                              <option [value]="country.phonecode" *ngFor="let country of countries">
                                +{{country.phonecode}}
                              </option>
                            </select>
                            <input type="tel" formControlName="mobile" />
                          </div>
                          <span *ngIf="bf.mobile.invalid && bf.mobile.touched">
                            <span class="text-danger" *ngIf="bf.mobile.errors?.required"> Mobile Number is required
                            </span>
                          </span>
                          <span class="text-danger" *ngIf="bf.mobile.errors?.pattern">Please Enter Valid Mobile Number
                          </span>
                        </div>
                      </div>
                      <div class="field">
                        <span>Address</span>
                        <textarea name="" id="" cols="30" rows="6" formControlName="address"
                          style="height: 146px;"></textarea>
                        <span *ngIf="bf.address.invalid && bf.address.touched">
                          <span class="text-danger" *ngIf="bf.address.errors?.required"> Address is required </span>
                        </span>
                      </div>
                    </div>
                    <div class="field">
                      <span>GST Number</span>
                      <input type="text" formControlName="gst" />
                    </div>
                    <div class="buttons">
                      <div class="primary-button golden" (click)="addBillingDetails()">Save</div>
                    </div>
                  </form>
                </div>
              </div>

            </div>
            <div class="addDetail hide" [ngClass]="detailShow?'show':'hide'">
              <div class="head">Add Address </div>
              <div class="box">
                <div class="form">
                  <form [formGroup]="addressForm">
                    <div class="twoSection">
                      <div class="field">
                        <span>Full Name</span>
                        <input type="text" formControlName="name" />
                        <span *ngIf="af.name.invalid && af.name.touched">
                          <span class="text-danger" *ngIf="af.name.errors?.required"> Name is required </span>
                        </span>
                      </div>
                      <div class="field">
                        <span>country</span>
                        <select name="" id="" formControlName="country" (change)="selectedCountry($event,'add')">
                          <option [value]="country.id" *ngFor="let country of countries">
                            {{country.name}}
                          </option>
                        </select>
                        <span *ngIf="af.country.invalid && af.country.touched">
                          <span class="text-danger" *ngIf="af.country.errors?.required"> Country is required </span>
                        </span>
                      </div>
                    </div>
                    <div class="twoSection">
                      <div class="field">
                        <span>pincode</span>
                        <input type="text" formControlName="pincode" (change)="checkPincode()" />
                        <span *ngIf="af.pincode.invalid && af.pincode.touched">
                          <span class="text-danger" *ngIf="af.pincode.errors?.required"> Pincode is required </span>
                        </span>
                        <span class="text-danger" *ngIf="af.pincode.errors?.pattern">Please Enter Valid Pincode </span>
                      </div>
                      <div class="field">
                        <span>state</span>
                        <select name="" id="" formControlName="state">
                          <option [value]="state.id" *ngFor="let state of states">
                            {{state.name}}
                          </option>
                        </select>
                        <span *ngIf="af.state.invalid && af.state.touched">
                          <span class="text-danger" *ngIf="af.state.errors?.required"> State is required </span>
                        </span>
                      </div>
                    </div>
                    <div class="twoSection">
                      <div class="field">
                        <div style="position: relative;">
                          <span>city</span>
                          <input type="text" formControlName="city" />
                          <span *ngIf="af.city.invalid && af.city.touched">
                            <span class="text-danger" *ngIf="af.city.errors?.required"> City is required </span>
                          </span>
                        </div>
                        <div style="position: relative; height: 99px;">
                          <span>Mobile Number</span>
                          <div class="country">
                            <select name="" id="" formControlName="code">
                              <option [value]="country.phonecode" *ngFor="let country of countries">
                                +{{country.phonecode}}
                              </option>
                            </select>

                            <input type="tel" formControlName="mobile" />
                          </div>
                          <span *ngIf="af.mobile.invalid && af.mobile.touched">
                            <span class="text-danger" *ngIf="af.mobile.errors?.required"> Mobile Number is required
                            </span>
                          </span>
                          <span class="text-danger" *ngIf="af.mobile.errors?.pattern">Please Enter Valid Mobile Number
                          </span>
                        </div>
                      </div>
                      <div class="field">
                        <span>Address</span>
                        <textarea name="" id="" cols="30" rows="6" formControlName="address"
                          style="height: 146px;"></textarea>
                        <span *ngIf="af.address.invalid && af.address.touched">
                          <span class="text-danger" *ngIf="af.address.errors?.required"> Address is required </span>
                        </span>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
              <div class="buttons">
                <div class="primary-button golden" (click)="submitAddress()">Save</div>
              </div>
            </div>
            <div class="addDetail hide" [ngClass]="editShow?'show':'hide'">
              <div class="head">Edit Address
              </div>
              <div class="box">
                <div class="form">
                  <form [formGroup]="editAddressForm">
                    <div class="twoSection">
                      <div class="field">
                        <span>Full Name</span>
                        <input type="text" formControlName="name" />
                      </div>
                      <div class="field">
                        <span>country</span>
                        <select name="" id="" formControlName="country" (change)="selectedCountry($event,'add')">
                          <option [value]="country.id" *ngFor="let country of countries">
                            {{country.name}}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="twoSection">
                      <div class="field">
                        <span>pincode</span>
                        <input type="text" formControlName="pincode" />
                        <span *ngIf="af.pincode.invalid && eaf.pincode.touched">
                          <span class="text-danger" *ngIf="eaf.pincode.errors?.required"> Pincode is required </span>
                        </span>
                        <span class="text-danger" *ngIf="eaf.pincode.errors?.pattern">Please Enter Valid Pincode </span>
                      </div>
                      <div class="field">
                        <span>state</span>
                        <select name="" id="" formControlName="state">
                          <option [value]="state.id" *ngFor="let state of states">
                            {{state.name}}
                          </option>
                        </select>
                      </div>
                    </div>
                    <div class="twoSection">
                      <div class="field">
                        <span>city</span>
                        <input type="text" formControlName="city" />
                        <span>Mobile Number</span>
                        <div class="country">
                          <select name="" id="" formControlName="code">
                            <option [value]="country.phonecode" *ngFor="let country of countries">
                              +{{country.phonecode}}
                            </option>
                          </select>
                          <input type="tel" formControlName="mobile" />
                        </div>
                        <span *ngIf="eaf.mobile.invalid && eaf.mobile.touched">
                          <span class="text-danger" *ngIf="eaf.mobile.errors?.required"> Mobile Number is required
                          </span>
                        </span>
                        <span class="text-danger" *ngIf="eaf.mobile.errors?.pattern">Please Enter Valid Mobile Number
                        </span>
                      </div>
                      <div class="field">
                        <span>Address</span>
                        <textarea name="" id="" cols="30" rows="6" formControlName="address"></textarea>
                      </div>
                    </div>
                  </form>
                </div>
              </div>
              <div class="buttons">
                <div class="primary-button golden" (click)="updateAddress()">Save</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
