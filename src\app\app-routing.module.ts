import { AdvanceBookingFailedComponent } from './advance-booking-failed/advance-booking-failed.component';
import { AuthguardGuard } from './authguard.guard';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { HomeComponent } from './home/<USER>';
import { AboutComponent } from './about/about.component';
import { FaqComponent } from './faq/faq.component';
import { BookAppointmentComponent } from './book-appointment/book-appointment.component';
import { ListingComponent } from './listing/listing.component';
import { DetailComponent } from './detail/detail.component';
import { ZoraCollectionComponent } from './zora-collection/zora-collection.component';
import { ContactComponent } from './contact/contact.component';
import { AdvanceBookingComponent } from './advance-booking/advance-booking.component';
import { BookingComponent } from './booking/booking.component';
import { GoldSchemesComponent } from './gold-schemes/gold-schemes.component';
import { GoldAccountComponent } from './gold-account/gold-account.component';
import { PersonalInformationComponent } from './personal-information/personal-information.component';
import { WishlistComponent } from './wishlist/wishlist.component';
import { MyOrdersComponent } from './my-orders/my-orders.component';
import { OrderDetailComponent } from './order-detail/order-detail.component';
import { ReferalsComponent } from './referals/referals.component';
import { MyGoldSchemesComponent } from './my-gold-schemes/my-gold-schemes.component';
import { GiftCardsComponent } from './gift-cards/gift-cards.component';
import { PersonalizeComponent } from './gift-cards/personalize/personalize.component';
import { WalletComponent } from './wallet/wallet.component';
import { MyAdvanceBookingsComponent } from './my-advance-bookings/my-advance-bookings.component';
import { DesignYourJewelleryComponent } from './design-your-jewellery/design-your-jewellery.component';
import { VirtualShoppingComponent } from './virtual-shopping/virtual-shopping.component';
import { BlogComponent } from './blog/blog.component';
import { BlogDetailsComponent } from './blog-details/blog-details.component';
import { RefundPolicyComponent } from './refund-policy/refund-policy.component';
import { PrivacyPolicyComponent } from './privacy-policy/privacy-policy.component';
import { CancelationPolicyComponent } from './cancelation-policy/cancelation-policy.component';
import { BuybackPolicyComponent } from './buyback-policy/buyback-policy.component';
import { TermsComponent } from './terms/terms.component';
import { OrderNowComponent } from './order-now/order-now.component';
import { DiamondEducationComponent } from './diamond-education/diamond-education.component';
import { OurCertificationsComponent } from './our-certifications/our-certifications.component';
import { AdvanceBookingPlacedComponent } from './advance-booking-placed/advance-booking-placed.component';
import { OrderNowDetailComponent } from './order-now-detail/order-now-detail.component';
import { GiftCardSuccessComponent } from './gift-card-success/gift-card-success.component';
import { GiftCardFailedComponent } from './gift-card-failed/gift-card-failed.component';
import { OrderNowSuccessComponent } from './order-now-success/order-now-success.component';
import { OrderFailedComponent } from './checkout/order-failed/order-failed.component';
import { GiftCardDetailComponent } from './gift-card-detail/gift-card-detail.component';
import { VirtualTryComponent } from './virtual-try/virtual-try.component';
import { GiftVoucherComponent } from './gift-voucher/gift-voucher.component';
import { DigitalGoldComponent } from './digital-gold/digital-gold.component';
import { NotFoundComponent } from './not-found/not-found.component';
import { ServerErrorComponent } from './server-error/server-error.component';
import { RingSizeGuideComponent } from './ring-size-guide/ring-size-guide.component';
import { BangleSizeGuideComponent } from './bangle-size-guide/bangle-size-guide.component';
import { ShippingPolicyComponent } from './shipping-policy/shipping-policy.component';
import { CareerComponent } from './career/career.component';
import { VideoPlayerComponent } from './shared/video-player/video-player.component';

const routes: Routes = [
  { path: '', component: HomeComponent },
  // { path: '', redirectTo: 'home', pathMatch: 'full' },
  { path: 'about', component: AboutComponent },
  { path: 'faq', component: FaqComponent },
  {
    path: 'book-appointment',
    component: BookAppointmentComponent,
    canActivate: [AuthguardGuard],
  },
  { path: 'listing/:type/:id', component: ListingComponent },
  { path: 'detail/:id', component: DetailComponent },
  {
    path: 'zora-collection',
    component: ZoraCollectionComponent,
    data: { id: 1 },
  },
  {
    path: 'divo-collection',
    component: ZoraCollectionComponent,
    data: { id: 2 },
  },
  {
    path: 'poyem-collection',
    component: ZoraCollectionComponent,
    data: { id: 3 },
  },
  {
    path: 'orlin-collection',
    component: ZoraCollectionComponent,
    data: { id: 4 },
  },
  { path: 'contact', component: ContactComponent },
  { path: 'advance-booking', component: AdvanceBookingComponent },
  {
    path: 'booking',
    component: BookingComponent,
    canActivate: [AuthguardGuard],
  },
  { path: 'gold-schemes', component: GoldSchemesComponent },
  {
    path: 'gold-account',
    component: GoldAccountComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'personal-information',
    component: PersonalInformationComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'wishlist',
    component: WishlistComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'my-orders',
    component: MyOrdersComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'order-detail/:id',
    component: OrderDetailComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'referals',
    component: ReferalsComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'my-gold-schemes',
    component: MyGoldSchemesComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'gift-cards',
    component: GiftCardsComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'personalize/:id',
    component: PersonalizeComponent,
    canActivate: [AuthguardGuard],
  },
  { path: 'wallet', component: WalletComponent, canActivate: [AuthguardGuard] },
  {
    path: 'my-advance-bookings',
    component: MyAdvanceBookingsComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'cart',
    loadChildren: () =>
      import('./checkout/checkout.module').then((m) => m.CheckoutModule),
    canActivate: [AuthguardGuard],
  },
  {
    path: 'design-your-jewellery',
    component: DesignYourJewelleryComponent,
    canActivate: [AuthguardGuard],
  },
  {
    path: 'virtual-shopping',
    component: VirtualShoppingComponent,
    canActivate: [AuthguardGuard],
  },
  { path: 'blog', component: BlogComponent },
  { path: 'blog-details/:id', component: BlogDetailsComponent },
  { path: 'refund-policy', component: RefundPolicyComponent },
  { path: 'privacy-policy', component: PrivacyPolicyComponent },
  { path: 'cancelation-policy', component: CancelationPolicyComponent },
  { path: 'buyback-policy', component: BuybackPolicyComponent },
  { path: 'terms', component: TermsComponent },
  {
    path: 'order-now/:id',
    component: OrderNowComponent,
    canActivate: [AuthguardGuard],
  },
  { path: 'diamond-education', component: DiamondEducationComponent },
  { path: 'ring-size-guide', component: RingSizeGuideComponent },
  { path: 'bangle-size-guide', component: BangleSizeGuideComponent },
  { path: 'our-certifications', component: OurCertificationsComponent },
  { path: 'advance-booking-placed', component: AdvanceBookingPlacedComponent },
  { path: 'advance-booking-failed', component: AdvanceBookingFailedComponent },
  { path: 'order-now-success', component: OrderNowSuccessComponent },
  { path: 'order-now-failed', component: OrderFailedComponent },
  {
    path: 'order-now-detail/:id',
    component: OrderNowDetailComponent,
    canActivate: [AuthguardGuard],
  },
  { path: 'gift-card-success', component: GiftCardSuccessComponent },
  { path: 'gift-card-failed', component: GiftCardFailedComponent },
  {
    path: 'gift-card-detail/:id',
    component: GiftCardDetailComponent,
    canActivate: [AuthguardGuard],
  },
  { path: 'virtual-try/:tryon_id', component: VirtualTryComponent },
  { path: 'gift-voucher', component: GiftVoucherComponent },
  { path: 'digital-gold', component: DigitalGoldComponent },
  { path: 'shipping-policy', component: ShippingPolicyComponent },
  { path: 'career', component: CareerComponent },
  { path: 'video-player', component: VideoPlayerComponent },
  { path: 'server-error', component: ServerErrorComponent },
  { path: '**', component: NotFoundComponent },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      scrollPositionRestoration: 'enabled',
      useHash: false,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
