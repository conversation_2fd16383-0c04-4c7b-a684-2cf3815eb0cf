<div class="container">
  <div class="contactUs">
    <div class="weHelp">
      <!-- <h1>We Help You</h1> -->
      <form [formGroup]="contactForm">
        <div class="box">
          <div class="close">
            <img src="\assets\images\close-white.svg" alt="close icon" />
          </div>
          <div class="rightSide">
            <div class="head">Get in touch</div>
            <div class="field">
              <div>Name</div>
              <input type="text" formControlName="name" />
              <span *ngIf="cf.name.invalid && cf.name.touched">
                <span class="text-danger" *ngIf="cf.name.errors?.required">
                  Name is required
                </span>
              </span>
            </div>
            <div class="field">
              <div>Email Id</div>
              <input type="email" formControlName="email" />
              <span *ngIf="cf.email.invalid && cf.email.touched">
                <span class="text-danger" *ngIf="cf.email.errors?.required">
                  Email is required
                </span>
              </span>
              <span class="text-danger" *ngIf="cf.email.errors?.pattern"
                >Please Enter Valid Email
              </span>
            </div>
            <div class="field">
              <div>Mobile Number</div>
              <input type="tel" formControlName="mobile" />
              <span *ngIf="cf.mobile.invalid && cf.mobile.touched">
                <span class="text-danger" *ngIf="cf.mobile.errors?.required">
                  Mobile number is required
                </span>
              </span>
              <span class="text-danger" *ngIf="cf.mobile.errors?.pattern"
                >Please Enter Valid Mobile number
              </span>
            </div>
            <div class="field">
              <div>Message</div>
              <textarea
                name=""
                id=""
                cols="30"
                rows="3"
                formControlName="message"
              ></textarea>
              <span *ngIf="cf.message.invalid && cf.message.touched">
                <span class="text-danger" *ngIf="cf.message.errors?.required">
                  Message is required
                </span>
              </span>
            </div>
            <label class="check"
              ><span
                ><input
                  type="checkbox"
                  [(ngModel)]="isChecked"
                  [ngModelOptions]="{ standalone: true }" /></span
              ><span
                >By clicking I give my consent to receive messages/ email and
                updates to my number or email id
              </span></label
            >

            <div class="field">
              <input type="submit" value="Register" (click)="submitData()" />
            </div>
          </div>
        </div>
      </form>
    </div>
    <div class="contact">
      <div class="tt">
        <img src="\assets\images\about\Rectangle 283.svg" alt="tt shop image" />
      </div>
      <div class="contacts">
        <div class="section" *ngFor="let contact of contactData">
          <div class="location">{{ contact.contact_title }}</div>
          <p>{{ contact.address }}</p>
        </div>
      </div>
      <div class="map">
        <!-- <img src="\assets\images\about\image 20.svg" alt=""> -->
        <iframe
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3921.0216608024457!2d76.06705107481694!3d10.65542496141444!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3ba7955b3f4ab451%3A0x426c593371fbfb2d!2sT.%20T.%20Devassy%20Jewellery!5e0!3m2!1sen!2sin!4v1690532928692!5m2!1sen!2sin"
          width="600"
          height="450"
          style="border: 0"
          allowfullscreen=""
          loading="lazy"
          referrerpolicy="no-referrer-when-downgrade"
        ></iframe>
      </div>
    </div>
  </div>
</div>
