.myAccount {
    padding: 40px 0 100px;
}
.sideMenu {
    width: 270px;
}
.contentSection {
    display: flex;

    .contents {
        width: calc(100% - 270px);
        padding-left: 50px;
    }
}

h1{
    font-size: 44px;
    margin-bottom: 25px;
}

.head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    span{font-weight: 600;}
    .add {
        width: auto;
    }
    .add {
        font-size: 14px;
        font-weight: 600;
        position: relative;
        text-align: right;
        cursor: pointer;
        &::before {
            content: "+";
            position: absolute;
            left: -25px;
            top: 1px;
            border: 1px solid #000;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            text-align: end;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

}

.goldSchemes {
    padding: 30px;
    background-image: url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3crect width='100%25' height='100%25' fill='none' stroke='%23333' stroke-width='1' stroke-dasharray='6%2c 14' stroke-dashoffset='0' stroke-linecap='square'/%3e%3c/svg%3e");
    margin: 1px;

    .id {
        font-size: 16px;
        padding-bottom: 15px;
    }

    .leftSide {
        width: 70%;
    }

    .box {
        display: flex;
    }

    .field {
        width: 50%;
    }

    .data {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
    }
    .text{
        padding-top: 20px;
    }
    .name {
        font-weight: 600;
        padding-top: 3px;
    }

    .rightSide {
        width: 30%;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    }

    .primary-button {
        margin-bottom: 15px;
        width: 184px;
    }
}

.transactionDetails {
    padding-top: 50px;
}

.heading {
    padding-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
}


.button {
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
    .primary-button {
        width: 260px;
        margin-left: 10px;
    }
}
.addAccount{
    .leftField{
        padding-right: 10px;
    }
    .rightField{
        padding-left: 10px;
    }
    label{
        color: #787878;
        font-size: 14px;
    }
    input{
        border: 1px solid #D5DBDE;
    }
}

.registerPopup {
    position: fixed;
    width: 100%;
    height: 100%;
    background: #********;
    top: 0;
    left: 0;
    z-index: 99;
    .recieve span {
      text-align: left;
      font-size: 12px;
      padding-left: 8px;
    }
    .box {
      display: flex;
      flex-direction: column;
      border-radius: 12px;
      padding: 30px;
      text-align: center;
      max-width: 434px;
      margin: 0 auto;
      box-shadow: 2px 2px 22px rgba(0, 0, 0, 0.06);
      position: fixed;
      top: 50%;
      left: 50%;
      z-index: 9;
      background: #fff;
      transform: translate(-50%, -50%);
    }

    &.show {
      display: flex !important;
    }

    &.hide {
      display: none;
    }
    .head {
        text-align: center;
        font-size: 20px;
        font-weight: 600;
        padding-bottom: 10px;
        display: block;
      }

      p {
        text-align: center;
        font-size: 14px;
        margin-bottom: 15px;
      }

      .field {
        width: 100%;
      }

      .check {
        text-align: center;
        margin-bottom: 10px;
        margin-top: 35px;

        span {
          padding-left: 10px;
          font-size: 13px;
        }

        a {
          color: #CB9F52;
          text-decoration: underline;
        }
      }


      input[type=text],
      input[type=url],
      input[type=tel],
      input[type=email],
      input[type=password],
      input[type=number],
      select,
      textarea,
      input[type=date] {
        border: 1px solid #D5DBDE;
        padding: 20px 16px;
      }

      .bottom {
        width: 100%;
        font-size: 12px;
        padding-top: 30px;

        .resend {
          cursor: pointer;
          color: #D2AB66;
        }
      }


    .close {
      position: absolute;
      right: -34px;
      top: -18px;
      cursor: pointer;
      opacity: unset;
      padding: 10px;
    }


  }

.rwd-table {
    margin: auto;
    min-width: 300px;
    max-width: 100%;
    width: 100%;
    border-collapse: collapse;
    font-size: 16px;
    color: #333;
    overflow: hidden;
   tr:first-child {
    border-top: none;
    background: #F9F9F9;
    color: #000;
    th{
        padding-bottom: 40px !important;
        position: relative;
        &:before{
            position: absolute;
            content: "";
            width: 101%;
            height: 20px;
            background-color: #fff;
            left: 0;
            bottom: 0;
        }
    }

  }

  tr {
    background-color: #F9F9F9;
  }


   th {
    display: none;
    border-right: 1px solid rgba(0, 0, 0, 0.19);
    &:last-child{
        border-right: none;
    }
  }

   td {
    display: block;
    border-right: 1px solid rgba(0, 0, 0, 0.19);
    &:last-child{
        border-right: none;
    }
  }

  td:first-child {
    margin-top: .5em;
  }

   td:last-child {
    margin-bottom: .5em;
  }

  td:before {
    content: attr(data-th) ": ";
    font-weight: bold;
    width: 120px;
    display: inline-block;
    color: #000;
  }

   th,
   td {
    text-align: center;
  }


   tr {
    border-color: #bfbfbf;
  }

   th,
   td {
    padding: .5em 1em;
  }
}
  @media screen and (min-width: 600px) {
    .rwd-table td:before {
      display: none;
    }
    .rwd-table th,
    .rwd-table td {
      display: table-cell;
      padding: .25em .5em;
    }
    .rwd-table th:first-child,
    .rwd-table td:first-child {
      padding-left: 0;
    }
    .rwd-table th:last-child,
    .rwd-table td:last-child {
      padding-right: 0;
    }
    .rwd-table th,
    .rwd-table td {
      padding: 1.2em !important;
    }
  }



@media screen and (max-width: 1260px) {
  .goldSchemes .data {
    display: flex;
    flex-direction: column;
  }

  .goldSchemes .leftSide {
      width: 100%;
      padding-bottom: 30px;
  }

  .goldSchemes .rightSide {
      width: 100%;
  }
}

@media screen and (max-width: 992px) {
  .myAccount {
    padding: 20px 0 50px;
  }
  .sideMenu {
      width: 60px;
  }
  .contentSection .contents {
      width: calc(100% - 60px);
  }

  h1 {
    font-size: 30px;
    margin-bottom: 10px;
  }
}
@media screen and (max-width: 640px) {
  .contentSection {
    flex-direction: column;
    .contents {
      width: 100%;
      padding-left: 0;
      padding-top: 30px;
    }

  }
  .sideMenu {
      width: 100%;
  }

}

@media screen and (max-width: 480px) {
  h1 {
      font-size: 25px;
  }

}
