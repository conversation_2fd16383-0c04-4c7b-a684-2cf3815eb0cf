import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { apiEndPoints } from '../ApiEndPoints';
import { ToastrService } from 'ngx-toastr';
import { FormBuilder, FormGroup } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { CommonService } from '../Services/common.service';
import { Meta } from '@angular/platform-browser';

@Component({
  selector: 'app-blog-details',
  templateUrl: './blog-details.component.html',
  styleUrls: ['./blog-details.component.scss']
})
export class BlogDetailsComponent implements OnInit {
  blog_id: any;
  data: any;
  recentBlogs: any;
  blogCategories: any;
  commentForm!: FormGroup
  isLoading : boolean = false
  showModal : boolean = false;

  constructor(
    private route: ActivatedRoute, private ApiService: ApiService, private toast: ToastrService, private fb: FormBuilder,
    private spinner: NgxSpinnerService, private commonService : CommonService, private meta : Meta
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.blog_id = params['id']
      this.blogDetail();
    })
    this.initCommentForm()
    this.getBlogCategory()
    this.getRecentBlogs()
  }

  blogDetail() {
    // this.isLoading = true
    this.ApiService.postData(apiEndPoints.BLOG_DETAIL, { slug: this.blog_id }).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.data = res?.data
        this.updateMeta()
        // this.isLoading = false
      }
    })
  }

  updateMeta(){
    this.meta.updateTag({name: 'title', content: this.data?.blog_title});
    this.meta.updateTag({name: 'description', content: this.data?.blog_description});
    this.meta.updateTag({property: 'og:image', content: this.data?.blog_image});
    this.meta.updateTag({property: 'og:title', content: this.data?.blog_title});
    this.meta.updateTag({property: 'og:description', content: this.data?.blog_description});
    this.meta.updateTag({rel : 'canonical', href : 'https://ttdevassyjewellery.com/blog-details/'+this.data?.blog_id});
  }

  getBlogCategory() {
    this.ApiService.getData(apiEndPoints.BLOG_CATEGORY).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.blogCategories = res?.data;
      }
    });
  }

  getRecentBlogs() {
    this.ApiService.getData(apiEndPoints.RECENT_BLOGS).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.recentBlogs = res?.data;
      }
    });
  }

  initCommentForm() {
    this.commentForm = this.fb.group({
      comment: ['']
    })
  }

  postComment() {
    let userid = localStorage.getItem('userId');
    let comment = this.commentForm.get('comment')?.value

    if (!userid) {
      this.toggleModal()
      return
    }

    // this.isLoading = true
    let data = { blog_id: this.data?.blog_id, comment: comment, user_id: userid }
    this.ApiService.postData(apiEndPoints.ADD_COMMENT, data).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.blogDetail()
        this.commentForm.reset()
        // this.isLoading = false
      }
    });
  }

  toggleModal() {
    this.commonService.sendClickEvent();
    this.showModal = true;
  }


}
