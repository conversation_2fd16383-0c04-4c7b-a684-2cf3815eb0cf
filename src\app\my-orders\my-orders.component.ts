import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';
import { environment } from 'src/environments/environment.prod';
import { Router } from '@angular/router';

@Component({
  selector: 'app-my-orders',
  templateUrl: './my-orders.component.html',
  styleUrls: ['./my-orders.component.scss'],
})
export class MyOrdersComponent implements OnInit {
  isLoading: boolean = true;
  orders: any;
  imageBase: any;

  constructor(private ApiService: ApiService, private router: Router) {}

  ngOnInit(): void {
    this.getMyOrders();
    this.imageBase = environment.imageBase;
  }

  getMyOrders() {
    this.isLoading = true;
    this.ApiService.postData(apiEndPoints.my_orders, {}).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          this.orders = res?.Data || [];
        }
        this.isLoading = false;
      }
    );
  }

  redirection(item: any) {
    if (item.type == 'order_now') {
      this.router.navigate(['/order-now-detail', item?.order_product_id]);
    } else if (item?.type == 'giftcard') {
      this.router.navigate(['/gift-card-detail', item?.order_product_id]);
    } else {
      this.router.navigate(['/order-detail', item?.order_product_id]);
    }
  }
}
