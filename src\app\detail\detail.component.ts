import { Form<PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { environment } from './../../environments/environment.prod';
import { apiEndPoints } from './../ApiEndPoints';
import { ApiService } from './../Services/api.service';
import {
  Component,
  ElementRef,
  HostListener,
  Input,
  OnInit,
  Renderer2,
  ViewChild,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import SwiperCore, {
  SwiperOptions,
  Virtual,
  Navigation,
  FreeMode,
  Pagination,
} from 'swiper';
import { NgxSpinnerService } from 'ngx-spinner';
import { CommonService } from '../Services/common.service';
import { Meta } from '@angular/platform-browser';
SwiperCore.use([Virtual, FreeMode, Navigation, Pagination]);

@Component({
  selector: 'app-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
})
export class DetailComponent implements OnInit {
  PopupStatus: any = false;
  selectedIndex: any;
  selected: any;
  PopupProduct: any = false;
  images: any;
  addOnIds: any = [];
  myThumbnail: any;
  myFullresImage: any;
  addOnsum: number = 0;
  grandTotal: any;
  enquireForm!: FormGroup;
  pinForm!: FormGroup;
  isLoading: boolean = false;
  isMobile: boolean = false;
  isImagePopupOpen: boolean = false;
  selectedImageIndex: number = 0;

  //the text that need to be put in the container
  @Input() text: string | undefined;

  //maximum height of the container
  @Input() maxHeight: number = 50;

  //set these to false to get the height of the expended container
  public isCollapsed: boolean = true;
  public isCollapsable: boolean = true;

  breakSlider: SwiperOptions = {
    spaceBetween: 20,
    slidesPerView: 5,
    freeMode: false,
    watchSlidesProgress: true,
    navigation: false,
    breakpoints: {
      320: {
        slidesPerView: 3,
      },
      480: {
        slidesPerView: 4,
      },
      640: {
        slidesPerView: 4,
      },
      992: {
        slidesPerView: 2,
      },
      1200: {
        slidesPerView: 3,
      },
      1300: {
        slidesPerView: 4,
      },
      1600: {
        slidesPerView: 5,
      },
    },
  };

  productSlide: SwiperOptions = {
    spaceBetween: 20,
    slidesPerView: 4,
    freeMode: false,
    watchSlidesProgress: true,
    navigation: false,
    breakpoints: {
      320: {
        slidesPerView: 2,
      },
      480: {
        slidesPerView: 3,
      },
      640: {
        slidesPerView: 4,
      },
    },
  };
  product_id: any;
  data: any;
  productDetail: any;
  imageBase: any;
  similarproducts: any;
  addOns: any;
  addOnLength: any;
  deliveryStatus: any;
  @Input() psku!: string;
  showModal: boolean = false;
  windowRef: any;
  @ViewChild('imageSlider') slider!: ElementRef;

  private touchStartX: number = 0;
  private touchEndX: number = 0;
  private minSwipeDistance: number = 50;
  sliderWidth: number = 0;

  constructor(
    private elementRef: ElementRef,
    private route: ActivatedRoute,
    private apiService: ApiService,
    private toastr: ToastrService,
    private fb: FormBuilder,
    private renderer: Renderer2,
    private router: Router,
    private commonService: CommonService,
    private metaService: Meta
  ) {
    sessionStorage.setItem('detailPage', JSON.stringify(router.url));
    this.checkScreenSize();
  }

  @HostListener('window:resize', ['$event'])
  ngOnInit(): void {
    this.route.params.subscribe((params) => {
      this.product_id = params['id'];
      this.getProductDetail();
    });
    this.imageBase = environment.imageBase;
    this.initEnquireForm();
    this.initPincodeForm();
  }

  onResize() {
    this.checkScreenSize();
  }

  checkScreenSize() {
    this.isMobile = window.innerWidth < 768; // Adjust breakpoint as needed
  }

  openImagePopup(index: number) {
    if (this.isMobile) {
      this.selectedImageIndex = index;
      this.isImagePopupOpen = true;
      // Prevent body scrolling when popup is open
      document.body.style.overflow = 'hidden';
    }
  }

  closeImagePopup() {
    this.isImagePopupOpen = false;
    document.body.style.overflow = 'auto';
  }

  nextImage() {
    if (this.selectedImageIndex < this.images.length - 1) {
      this.selectedImageIndex++;
    }
  }

  previousImage() {
    if (this.selectedImageIndex > 0) {
      this.selectedImageIndex--;
    }
  }

  // Modify existing changeImage method
  changeImage(key: number) {
    if (this.isMobile) {
      this.openImagePopup(key);
    } else {
      this.myThumbnail = this.imageBase + '/' + this.images[key]['thumbnail'];
      this.myFullresImage = this.imageBase + '/' + this.images[key]['image'];
    }
  }

  addMetaTags() {
    this.metaService.updateTag({
      name: 'og:title',
      content: this.data?.productName,
    });
    this.metaService.updateTag({
      name: 'og:description',
      content: this.data?.description,
    });
    this.metaService.updateTag({
      name: 'og:image',
      content: this.myThumbnail,
    });
  }

  getProductDetail() {
    // this.isLoading = true
    let data = { slug: this.product_id };
    this.apiService
      .postData(apiEndPoints.PRODUCT_DETAIL, data)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.data = res?.Data;
          this.productDetail = this.data?.productDetail?.items;
          this.images = this.data?.imageList;
          this.myThumbnail = this.imageBase + '/' + this.images[0]?.thumbnail;
          this.myFullresImage = this.imageBase + '/' + this.images[0]?.image;
          this.similarproducts = this.data?.similarProducts;
          this.psku = this.data?.tryon_id;
          this.addOns = this.data?.addonProducts;
          this.addMetaTags();
          for (let product of this.addOns) {
            this.addOnIds.push(product.id);
            this.addOnsum += parseInt(product.price);
          }
          this.addOnLength = this.addOnIds.length;
          this.grandTotal =
            parseInt(this.data?.addon_calculations?.productPrice) +
            this.addOnsum;

          //Webengage
          this.windowRef = window;
          this.windowRef.webengage.track('Product Viewed', {
            'Product ID': data.slug,
            'Product Name': this.data?.productName,
            'L1 Category Name': this.data?.maincategory_name,
            'L1 Category ID': this.data?.maincategory_id,
            'L2 Category Name': this.data?.subcategory_name,
            'L2 Category ID': this.data?.subcategory_id,
            Availability: this.data?.outOfStock == false ? true : false,
            isBundle: false,
            isSample: false,
            MRP: parseFloat(this.data?.price),
            Currency: 'INR',
            Size: this.data?.size,
            'Item Weight': this.data?.netweight,
            Image: this.images.map((element: any) => {
              return this.imageBase + '/' + element?.thumbnail;
            }),
            Source: 'Website',
            'Page URL': window.location.href,
          });
          //Webengage

          const script = this.renderer.createElement('script');
          // this.renderer.setAttribute(script, 'src', 'https://camweara.com/integrations/camweara_api.js');
          this.renderer.setAttribute(
            script,
            'onload',
            `loadTryOnButton({
          psku:'${this.psku}',
          page:'product',
          company:'Ttdevassyjewellery',
          nonTryOnProductsReport:'true',
          buynow:{enable:'true'},
          appendButton:{class:'info'},
          MBappendButton:{class:'info'},
          styles:{
          tryonbutton:{backgroundColor:'white',color:'black'},
          tryonbuttonHover:{backgroundColor:'black',color:'white',border:'none',},
          MBtryonbutton:{width:'100%',borderRadius:'25px'}
          },
          tryonButtonData:{text:'Digital wear',faIcon:'fa fa-camera'},
        });`
          );
          this.renderer.appendChild(document.body, script);
          // this.isLoading = false
        }
      });
  }

  checkPincode() {
    // if (localStorage.getItem('isLogged')) {
    //   if (this.pinForm.invalid) return
    let pincode = this.pinForm.get('pincode')?.value;
    this.apiService
      .postData(apiEndPoints.DELIVERY_CHECK, { pincode: pincode })
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.deliveryStatus = res?.Data;
        }
      });
    //}
    // else {
    //   this.toggleModal()
    // }
  }

  // changeImage(key: any) {
  //   this.myThumbnail = this.imageBase + '/' + this.images[key]['thumbnail'];
  //   this.myFullresImage = this.imageBase + '/' + this.images[key]['image'];
  // }

  ngAfterViewInit() {
    let currentHeight =
      this.elementRef.nativeElement.getElementsByTagName('div')[0].offsetHeight;
    //collapsable only if the contents make container exceed the max height
    if (currentHeight > this.maxHeight) {
      this.isCollapsed = true;
      this.isCollapsable = true;
    }

    this.sliderWidth = this.slider?.nativeElement.offsetWidth;
  }

  showAccordion(index: any) {
    if (this.selectedIndex == index) {
      this.selectedIndex = '';
    } else {
      this.selectedIndex = index;
    }
  }

  popUp() {
    this.PopupStatus = !this.PopupStatus;
  }

  popUpClose() {
    this.PopupStatus = false;
  }

  more(index: any) {
    if (this.selected == index) {
      this.selected = '';
    } else {
      this.selected = index;
    }
  }

  show() {
    this.PopupProduct = !this.PopupProduct;
  }

  PopupProductClose() {
    this.PopupProduct = false;
  }

  addToWishlist(id: any, isfavorite: any) {
    if (localStorage.getItem('isLogged')) {
      let data = { type: 0, productId: id };
      if (!isfavorite) data['type'] = 1;
      this.apiService
        .postData(apiEndPoints.WISHLIST, data)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) {
            this.getProductDetail();
            this.commonService.sendWishlistEvent();

            //webengage Start
            this.windowRef = window;
            const event = isfavorite
              ? 'Removed From Wishlist'
              : 'Added to Wishlist';
            this.windowRef.webengage.track(event, {
              'Product ID': isfavorite ? this.data?.id : this.data?.slug,
              'Product Name': this.data?.name,
              // 'L1 Category Name': '',
              // 'L1 Category ID': '',
              // 'L2 Category Name': '',
              // 'L2 Category ID': '',
              Availability: this.data?.outOfStock == false ? true : false,
              isBundle: false,
              Quantity: 1,
              MRP: parseFloat(this.data?.price),
              Currency: 'INR',
              Source: 'Website',
              'Page URL': window.location.href,
            });
            //webengage End

            this.toastr.success(
              this.data.isfavorite
                ? 'Removed from wishlist'
                : 'Added to wishlist',
              'Success'
            );
          }
        });
    } else {
      this.toggleModal();
    }
  }

  addToCart(id: any) {
    if (localStorage.getItem('isLogged')) {
      let data = { productId: id };
      this.apiService
        .postData(apiEndPoints.ADD_TO_CART, data)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) {
            //Webengage Start
            this.windowRef = window;
            this.windowRef.webengage.track('Added To Cart', {
              'Cart ID': '',
              'Product ID': this.data?.slug,
              'Product Name': this.data?.productName,
              'L1 Category Name': this.data?.maincategory_name,
              'L1 Category ID': String(this.data?.maincategory_id),
              'L2 Category Name': this.data?.subcategory_name,
              'L2 Category ID': String(this.data?.subcategory_id),
              Availability: this.data?.outOfStock == false ? true : false,
              isBundle: false,
              isSample: false,
              Quantity: 1,
              MRP: parseFloat(this.data?.price).toFixed(2),
              Currency: 'INR',
              Image: this.images.map((element: any) => {
                return this.imageBase + '/' + element['image'];
              }),
              Source: 'Website',
              'Page URL': window.location.href,
            });
            //Webengage End

            this.toastr.success('Product Added To Cart');
            this.getProductDetail();
            this.commonService.sendCartEvent();
          } else this.toastr.info(res.Message);
        });
    } else {
      this.toggleModal();
    }
  }

  selectAddons(id: any, price: any) {
    if (id && this.addOnIds.indexOf(id) !== -1) {
      this.addOnIds.splice(this.addOnIds.indexOf(id), 1);
      this.addOnsum -= parseInt(price);
      this.grandTotal =
        parseInt(this.data?.addon_calculations?.productPrice) + this.addOnsum;
    } else {
      this.addOnIds.push(id);
      this.addOnsum += parseInt(price);
      this.grandTotal =
        parseInt(this.data?.addon_calculations?.productPrice) + this.addOnsum;
    }
    this.addOnLength = this.addOnIds.length;
  }

  multipleAddtoCart() {
    if (localStorage.getItem('isLogged')) {
      let data = { productId: this.addOnIds };
      this.apiService
        .postData(apiEndPoints.MULTIPLE_CART_ADD, data)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) this.toastr.success('Product Added To Cart');
          else this.toastr.info(res.Message);
        });
    } else {
      this.toggleModal();
    }
  }

  initEnquireForm() {
    this.enquireForm = this.fb.group({
      mobile: [''],
      description: [''],
    });
  }

  initPincodeForm() {
    this.pinForm = this.fb.group({
      pincode: ['', [Validators.pattern('^[0-9]{6}$')]],
    });
  }

  get pf() {
    return this.pinForm.controls;
  }

  submitEnquiry() {
    if (localStorage.getItem('isLogged')) {
      let data = {
        mobile: this.enquireForm.get('mobile')?.value,
        description: this.enquireForm.get('description')?.value,
      };
      this.apiService
        .postData(apiEndPoints.CUSTOMIZE_ENQUIRY, data)
        .subscribe((res: any) => {
          if (res?.ErrorCode == 0) {
            this.toastr.success('Enquiry Submitted');
            this.PopupStatus = false;
          }
        });
    } else {
      this.toggleModal();
    }
  }

  shareToFacebook() {
    let currentUrl = window.location.href;
    let facebookShareUrl = `https://www.facebook.com/sharer/sharer.php?u=${currentUrl}`;
    window.open(facebookShareUrl, '_blank');
  }

  shareOnTwitter() {
    let url = encodeURIComponent(window.location.href);
    let tweetUrl = `https://twitter.com/intent/tweet?url=${url}`;
    window.open(tweetUrl, '_blank');
  }

  shareOnInstagram() {
    let url = encodeURIComponent(this.router.url);
    let caption = encodeURIComponent(
      'Check out this website: ' + this.router.url
    );
    let instagramUrl = `https://www.instagram.com/?url=${url}&caption=${caption}`;
    window.open(instagramUrl, '_blank');
  }

  shareOnPinterest() {
    let url = encodeURIComponent(window.location.href);
    let shareUrl = `https://pinterest.com/pin/create/button/?url=${url}`;
    window.open(shareUrl, '_blank');
  }

  toggleModal() {
    this.commonService.sendClickEvent();
    this.showModal = true;
  }

  onTouchStart(event: TouchEvent) {
    this.touchStartX = event.touches[0].clientX;
  }

  onTouchMove(event: TouchEvent) {
    this.touchEndX = event.touches[0].clientX;
  }

  onTouchEnd() {
    const swipeDistance = this.touchStartX - this.touchEndX;
    const isLeftSwipe = swipeDistance > this.minSwipeDistance;
    const isRightSwipe = swipeDistance < -this.minSwipeDistance;

    if (isLeftSwipe && this.selectedImageIndex < this.images.length - 1) {
      this.nextImage();
    }

    if (isRightSwipe && this.selectedImageIndex > 0) {
      this.previousImage();
    }

    // Reset touch coordinates
    this.touchStartX = 0;
    this.touchEndX = 0;
  }
}
