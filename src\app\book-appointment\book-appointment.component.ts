import { ToastrService } from 'ngx-toastr';
import { ApiService } from './../Services/api.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ate, Ng<PERSON>Datepicker, NgbDateStruct, NgbInputDatepickerConfig } from '@ng-bootstrap/ng-bootstrap';
import { noop } from 'rxjs';
import { DatepickerOptions } from 'ng2-datepicker';
import { getYear } from 'date-fns';
import locale from 'date-fns/locale/en-US';
import { apiEndPoints } from '../ApiEndPoints';
import * as moment from 'moment';
import { NgxSpinnerService } from 'ngx-spinner';
import { environment } from 'src/environments/environment.prod';

@Component({
  selector: 'app-book-appointment',
  templateUrl: './book-appointment.component.html',
  styleUrls: ['./book-appointment.component.scss'],
  providers: [NgbInputDatepickerConfig]
})


export class BookAppointmentComponent implements OnInit {
  recaptcha_key: any = environment.recaptcha_key
  date = new Date();
  dateString: any;
  firstTimeAssign: any;
  private onChange: (_: any) => void = noop;
  showTimePickerToggle: Boolean = false;
  bookingForm!: FormGroup;
  selectedStore: any;
  stores: any;
  isLoading : boolean = false

  options: DatepickerOptions = {
    minYear: getYear(new Date()) - 30, // minimum available and selectable year
    maxYear: getYear(new Date()) + 30, // maximum available and selectable year
    placeholder: '', // placeholder in case date model is null | undefined, example: 'Please pick a date'
    format: 'LLLL do yyyy', // date format to display in input
    formatTitle: 'LLLL yyyy',
    formatDays: 'EEEEE',
    firstCalendarDay: 0, // 0 - Sunday, 1 - Monday
    locale: locale, // date-fns locale
    position: 'bottom',
    inputClass: '', // custom input CSS class to be applied
    calendarClass: 'datepicker-default', // custom datepicker calendar CSS class to be applied
    scrollBarColor: '#dfe3e9', // in case you customize you theme, here you define scroll bar color
    // keyboardEvents: true // enable keyboard events
  };
  storeValidation: any;

  constructor(private formbuilder: FormBuilder, private ApiService: ApiService, private toast: ToastrService,
    private spinner: NgxSpinnerService) { }

  ngOnInit(): void {
    this.initBookingForm();
    this.getStores()
  }

  initBookingForm() {
    this.bookingForm = this.formbuilder.group({
      name: ['', [Validators.required]],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      date: ['', [Validators.required]],
      time: ['', [Validators.required]],
      lookingFor: [''],
      recaptchaReactive: ['', Validators.required]
    })
  }

  get bf() {
    return this.bookingForm.controls
  }

  getStores() {
    this.ApiService.getData(apiEndPoints.GET_STORES).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.stores = res?.Data
      }
    })
  }

  submitBooking() {
    if (!this.selectedStore) {
      this.storeValidation = "Store is required"
    }

    if (this.bookingForm.invalid) {
      this.bookingForm.markAllAsTouched()
      return
    }
    let date = moment(this.bookingForm.get('date')?.value).format('YYYY-MM-DD')
    let time = moment(this.bookingForm.get('time')?.value, 'h:mm A').format('hh:mm a');

    let data = {
      name: this.bookingForm.get('name')?.value,
      store_id: this.selectedStore,
      mobile: this.bookingForm.get('mobile')?.value,
      date: date,
      time: time,
      description: this.bookingForm.get('lookingFor')?.value
    }
    if (this.selectedStore) {
      // this.isLoading = true
      this.ApiService.postData(apiEndPoints.BOOK_APPOINTMENT, data).subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.toast.success("Appointment Booked Successfully")
          window.location.reload()
          // this.isLoading = false
        } else {
          // this.isLoading = false
          this.toast.error(res?.Message?.date)
        }
      })
    }
  }

}

