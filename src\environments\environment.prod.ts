export const environment = {
  production: true,
  ccAvenueProduction: 'https://secure.ccavenue.com/transaction/transaction.do',
  ccAvenueStaging: 'https://test.ccavenue.com/transaction/transaction.do',

  // apiUrl : 'https://ttdevassy.s416.previewbay.com/api/',
  // imageBase :'https://ttdevassy.s416.previewbay.com/storage',

  // apiUrl: 'https://admin.beta.ttdevassyjewellery.com/api/',
  // imageBase: 'https://admin.beta.ttdevassyjewellery.com/storage',

  apiUrl: 'https://admin.ttdevassyjewellery.com/api/',
  imageBase: 'https://admin.ttdevassyjewellery.com/storage',

  recaptcha_key: '6LdDujsmAAAAAOgOBdTiV2S-1a8vnNHqpYO8QEa9',

  firebaseConfig: {
    apiKey: 'AIzaSyD1mIJIBYOEVRGxehY6iHh1KWvefY92-wk',
    authDomain: 'tt-devassy-5f806.firebaseapp.com',
    projectId: 'tt-devassy-5f806',
    storageBucket: 'tt-devassy-5f806.appspot.com',
    messagingSenderId: '279050454083',
    appId: '1:279050454083:web:8dba9286dcb1df70fb80f6',
    measurementId: 'G-QK9EL6M05V',
  },

  gtmScript: `
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-CF9YFQF7PJ');
`,

  tagScript: `<script async src="https://www.googletagmanager.com/gtag/js?id=G-CF9YFQF7PJ"></script>`,
};
