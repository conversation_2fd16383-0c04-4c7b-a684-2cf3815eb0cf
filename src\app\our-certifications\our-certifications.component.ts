import { ApiService } from 'src/app/Services/api.service';
import { Component, OnInit } from '@angular/core';
import { apiEndPoints } from '../ApiEndPoints';
import { ToastrService } from 'ngx-toastr';

@Component({
  selector: 'app-our-certifications',
  templateUrl: './our-certifications.component.html',
  styleUrls: ['./our-certifications.component.scss']
})
export class OurCertificationsComponent implements OnInit {
  data: any;

  constructor(private ApiService: ApiService, private toast: ToastrService) { }

  ngOnInit(): void {
    this.ApiService.getData(apiEndPoints.ourCertification).subscribe((res: any) => {
      if (res?.errorcode == 0) {
        this.data = res?.data
      } else this.toast.error(res?.message)
    })
  }

}
