import { ToastrService } from 'ngx-toastr';
import { apiEndPoints } from 'src/app/ApiEndPoints';
import { ApiService } from 'src/app/Services/api.service';
import { ActivatedRoute, Router } from '@angular/router';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import {
  Component,
  ElementRef,
  NgZone,
  OnInit,
  ViewChild,
} from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { environment } from 'src/environments/environment.prod';
declare var Razorpay: any;

@Component({
  selector: 'app-personalize',
  templateUrl: './personalize.component.html',
  styleUrls: ['./personalize.component.scss'],
})
export class PersonalizeComponent implements OnInit {
  @ViewChild('form') form!: ElementRef;
  PopupGift: boolean = false;
  giftForm!: FormGroup;
  isBilling: boolean = false;
  isGiftForm: boolean = true;
  billingForm!: FormGroup;

  countryname = ['India', 'Dubai'];
  statename = ['Kerala', 'Tamilnadu', 'Goa'];
  cityname = ['Kerala', 'Tamilnadu', 'Goa'];
  gift_id: any;
  data: any;
  selectedCountry: any;
  states: any;
  countries: any;
  selectedState: any;
  amount: any;
  sender_name: any;
  message: any;
  encRequest: any;
  accessCode: any;
  countryValidation: any;
  stateValidation: any;
  isLoading: boolean = false;
  ccAvenueUrl: string = environment.ccAvenueProduction;
  ccAvenueBeta: string = environment.ccAvenueStaging;
  checkDomain: boolean = false;
  private rzp: any;

  constructor(
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private ApiService: ApiService,
    private toast: ToastrService,
    private spinner: NgxSpinnerService,
    private router: Router,
    private ngZone: NgZone
  ) {}

  ngOnInit(): void {
    this.checkDomain = window.location.hostname.includes('beta');
    this.route.paramMap.subscribe((params) => {
      this.gift_id = params.get('id');
    });
    this.getCardDetails();
    this.initGiftForm();
    this.initBillingForm();
    this.getCountries();
  }

  getCardDetails() {
    this.ApiService.postData(apiEndPoints.GIFT_DETAIL, {
      id: this.gift_id,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.data = res?.Data;
      }
    });
  }

  initGiftForm() {
    this.giftForm = this.formBuilder.group({
      amount: ['', [Validators.required, Validators.pattern('^[0-9]*$')]],
      recipName: ['', Validators.required],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      message: ['', Validators.required],
      senderName: ['', Validators.required],
    });
  }

  get gf() {
    return this.giftForm.controls;
  }

  initBillingForm() {
    this.billingForm = this.formBuilder.group({
      name: ['', Validators.required],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      address: ['', Validators.required],
      pincode: ['', [Validators.required, Validators.pattern(/^\d{6}$/)]],
      city: ['', Validators.required],
      mobile: ['', [Validators.required, Validators.pattern('^[0-9]{10}$')]],
    });
  }

  get bf() {
    return this.billingForm.controls;
  }

  giftcard() {
    if (this.giftForm.valid) {
      this.PopupGift = !this.PopupGift;
      this.amount = this.giftForm.get('amount')?.value;
      this.sender_name = this.giftForm.get('senderName')?.value;
      this.message = this.giftForm.get('message')?.value;
    }
  }

  popUpClose() {
    this.PopupGift = false;
  }

  getCountries() {
    this.ApiService.getData(apiEndPoints.COUNTRY).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.countries = res?.Data;
      }
    });
  }

  selectCountry() {
    const countryId = this.selectedCountry;
    this.ApiService.postData(apiEndPoints.STATE, {
      countryId: countryId,
    }).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.states = res?.Data;
        this.billingForm?.get('state')?.enable();
        this.selectedState = this.states[0]['id'];
      }
    });
  }

  submit() {
    if (this.giftForm.invalid) {
      this.giftForm.markAllAsTouched();
      return;
    } else {
      this.isGiftForm = false;
      this.isBilling = true;
    }
  }

  completeOrder() {
    if (!this.selectedCountry) {
      this.countryValidation = 'Country is required';
    }

    if (!this.selectedState) {
      this.stateValidation = 'State is required';
    }

    if (this.billingForm.invalid) {
      this.billingForm.markAllAsTouched();
      return;
    }

    let data = {
      giftcard_id: this.gift_id,
      amount: this.giftForm.get('amount')?.value,
      recip_name: this.giftForm.get('recipName')?.value,
      mobile: this.giftForm.get('mobile')?.value,
      email: this.giftForm.get('email')?.value,
      message: this.giftForm.get('message')?.value,
      sender_name: this.giftForm.get('senderName')?.value,
      billing_name: this.billingForm.get('name')?.value,
      billing_email: this.billingForm.get('email')?.value,
      billing_address: this.billingForm.get('address')?.value,
      billing_country: this.selectedCountry,
      billing_state: this.selectedState,
      billing_pincode: this.billingForm.get('pincode')?.value,
      billing_city: this.billingForm.get('city')?.value,
      billing_mobile: this.billingForm.get('mobile')?.value,
    };

    // this.isLoading = true
    this.ApiService.postData(apiEndPoints.EDIT_GIFT, data).subscribe(
      (res: any) => {
        if (res?.ErrorCode == 0) {
          const data = res?.Data?.data;
          const options = {
            key: data.api_key,
            order_id: data.razorpay_order_id,
            amount: data.amount, // amount in paise
            currency: 'INR',
            name: 'TT Devassy',
            description: 'Gift Card',
            handler: (response: any) => {
              this.isLoading = true;
              // Handle success
              this.ApiService.postData('gift-card-payment', {
                order_id: data.order_id,
                ...response,
              }).subscribe((res: any) => {
                this.isLoading = false;
                if (res?.ErrorCode == 0) {
                  this.router.navigate(['/gift-card-success']);
                } else {
                  this.router.navigate(['/gift-card-failed']);
                }
              });
            },
            prefill: {
              name: this.billingForm.get('name')?.value,
              email: this.billingForm.get('email')?.value,
              contact: this.billingForm.get('mobile')?.value,
            },
            notes: {
              address: this.billingForm.get('address')?.value,
            },
            theme: {
              color: '#F37254',
            },
            modal: {
              ondismiss: () => {
                this.ngZone.run(() => {
                  this.handlePaymentFailure(data.order_id);
                });
              },
            },
          };

          this.rzp = new Razorpay(options);
          this.rzp.on('payment.failed', (response: any) => {
            this.ngZone.run(() => {
              this.handlePaymentFailure(data.order_id);
            });
          });
          this.rzp.open();
          // this.accessCode = res?.Data?.accesscode
          // this.encRequest = res?.Data?.encrypted_data
          // setTimeout(() => {
          //   this.form.nativeElement.submit()
          // }, 500)
          // this.isLoading = false
        } else {
          // this.isLoading = false
          this.toast.error(res?.Message);
        }
      }
    );
  }

  private handlePaymentFailure(orderId: string) {
    // Close the Razorpay modal if it's open
    if (this.rzp) {
      this.rzp.close();
    }

    this.isLoading = true;
    // Call the API to cancel the payment
    this.ApiService.postData('gift-card-payment-cancel', {
      order_id: orderId,
    }).subscribe(
      (res: any) => {
        this.isLoading = false;
        this.router.navigateByUrl('/gift-card-failed');
      },
      (error) => {
        this.isLoading = false;
        this.toast.error(`Payment failed`);
        this.router.navigateByUrl('/gift-card-failed');
      }
    );
  }

  onKeyDown(event: KeyboardEvent) {
    if (event.key == 'e') {
      event.preventDefault();
    }
  }
}
