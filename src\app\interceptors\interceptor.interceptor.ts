import { Injectable } from '@angular/core';
import { HttpRequest, HttpHandler, HttpEvent, HttpInterceptor,HttpResponse, HttpErrorResponse} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Router } from '@angular/router';

@Injectable()
export class InterceptorInterceptor implements HttpInterceptor {

  constructor(private router: Router) { }

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    const token = localStorage.getItem('token');
    if (token) {
      request = request.clone({
        setHeaders: {
          Authorization: `Bearer ${token}`
        },
      });
    }
    return next.handle(request).pipe(
      tap((event:HttpEvent<any>) => {
        if (event instanceof HttpResponse) {
          // Check for specific response status codes here if needed
        }
      }),
      catchError((error: HttpErrorResponse) => {
        if (error.status === 500 ) {
          this.router.navigateByUrl('/server-error')
        }
        // if (error.status === 401) {
        //   localStorage.removeItem("isLogged")
        //   localStorage.removeItem("token")
        //   localStorage.removeItem("userId")
        //   this.router.navigateByUrl('')
        // }
        return throwError(error);
      })
    );
  }
}
