import { ToastrService } from 'ngx-toastr';
import { Component, OnInit } from '@angular/core';
import { ChartOptions } from 'chart.js';
import { apiEndPoints } from '../ApiEndPoints';
import { ApiService } from '../Services/api.service';
import { Router } from '@angular/router';
import { CommonService } from '../Services/common.service';
import { environment } from 'src/environments/environment.prod';
import { goldScheme } from '../shared/models/gold-scheme.model';
import { Meta } from '@angular/platform-browser';

@Component({
  selector: 'app-gold-schemes',
  templateUrl: './gold-schemes.component.html',
  styleUrls: ['./gold-schemes.component.scss'],
})
export class GoldSchemesComponent implements OnInit {
  selectedIndex: any;
  pieData: any = [1000, 2000];

  public pieChartOptions: ChartOptions<'pie'> = {
    responsive: false,
  };
  public pieChartLabels = ['Total Payment', 'customer Bonus'];
  pieChartDatasets: any = [
    {
      data: this.pieData,
      backgroundColor: ['#D2AB66', '#D9D9D9'],
      hoverBackgroundColor: ['#D2AB66', '#D9D9D9'],
      hoverBorderColor: ['#D2AB66', '#D9D9D9'],
    },
  ];
  public pieChartLegend = true;
  public pieChartPlugins = [];
  public chartColors: Array<any> = [
    {
      // all colors in order
      backgroundColor: ['#d13537', '#b000b5', '#c0ffee'],
    },
  ];
  plans: any;
  selectedPlan: any;
  schemeAmounts: any;
  selectedAmount: any;
  calculation: any;
  faqs: any;
  features: any;
  showModal: boolean = false;
  banner1: any;
  banner2: any;
  imageBase = environment.imageBase;
  goldSchemeData!: goldScheme;
  firstBanner!: string;
  windowRef = window;

  constructor(
    private apiservice: ApiService,
    private toast: ToastrService,
    private router: Router,
    private commonService: CommonService,
    private meta: Meta
  ) { }

  ngOnInit(): void {
    this.getSchemePlans();
    this.getSchemeFaq();
    this.getPlanFeatures();
    this.getBanners();
    this.getGoldSchemeData();
  }

  loginCheck() {

    //Webengage Start
    this.windowRef.webengage.track('Open New Account', {});
    //Webengage End

    if (localStorage.getItem('isLogged')) {
      this.router.navigate(['/gold-account']);
    } else {
      this.toggleModal();
    }
  }

  getSchemePlans() {
    this.apiservice.getData(apiEndPoints.SCHEME_PLANS).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.plans = res?.Data;
        this.selectedPlan = this.plans[0]['id'];
        this.getAmounts();
      }
    });
  }

  getAmounts() {
    if (this.selectedPlan) {
      this.apiservice
        .postData(apiEndPoints.SCHEME_AMOUNTS, { plan_id: this.selectedPlan })
        .subscribe((res: any) => {
          if (res.ErrorCode == 0) {
            this.schemeAmounts = res?.Data;
            this.selectedAmount = this.schemeAmounts?.amounts[0];
            if (this.selectedAmount && this.selectedPlan)
              this.getSchemeCalculation();
          }
        });
    }
  }

  getSchemeCalculation() {
    if (this.selectedPlan && this.selectedAmount) {
      this.apiservice
        .postData(apiEndPoints.SCHEME_CALCULATOR, {
          plan_id: this.selectedPlan,
          monthly_amount: this.selectedAmount,
        })
        .subscribe((res: any) => {
          if (res.ErrorCode == 0) {
            this.pieData = [];
            this.calculation = res.Data;
            this.pieChartDatasets[0].data = this.pieData;
            this.pieData.push(this.calculation.total_payment);
            this.pieData.push(this.calculation.bonus);
            this.pieChartDatasets[0].data = this.pieData;
            this.pieChartDatasets = [
              {
                data: this.pieData,
                backgroundColor: ['#D2AB66', '#D9D9D9'],
                hoverBackgroundColor: ['#D2AB66', '#D9D9D9'],
                hoverBorderColor: ['#D2AB66', '#D9D9D9'],
              },
            ];
          } else {
            this.toast.error(res?.Message);
          }
        });
    }
  }

  getGoldSchemeData() {
    this.apiservice.getGoldSchemeData(apiEndPoints.gold_scheme).subscribe(
      (res) => {
        if (res.ErrorCode === 0) {
          this.goldSchemeData = res.Data;
          this.firstBanner =
            this.imageBase + '/' + this.goldSchemeData?.scheme_banner;
          this.updateMeta();
        } else {
          this.toast.error('Something went wrong', 'Error');
        }
      },
      (error) => {
        this.toast.error(error, 'Error');
      }
    );
  }

  updateMeta() {
    this.meta.updateTag({ name: 'title', content: this.goldSchemeData?.scheme_title })
    this.meta.updateTag({ name: 'description', content: this.goldSchemeData?.scheme_description })
    this.meta.updateTag({ property: 'og:image', content: this.imageBase + '/' + this.goldSchemeData?.scheme_banner })
    this.meta.updateTag({ property: 'og:title', content: this.goldSchemeData?.scheme_title })
    this.meta.updateTag({ property: 'og:description', content: this.goldSchemeData?.scheme_description })
    this.meta.updateTag({ rel: 'canonical', href: 'https://ttdevassyjewellery.com/gold-schemes' })
  }

  showAccordion(index: any) {
    if (this.selectedIndex == index) {
      this.selectedIndex = '';
    } else {
      this.selectedIndex = index;
    }
  }

  schemePopUp() {
    //Webengage Start
    this.windowRef.webengage.track('Existing Account', {});
    //Webengage End

    if (localStorage.getItem('isLogged')) {
      localStorage.setItem('schemePopup', '0');
      this.router.navigateByUrl('/my-gold-schemes');
    } else {
      this.toggleModal();
    }
  }

  getSchemeFaq() {
    this.apiservice
      .getData(apiEndPoints.goldSchemeFaq)
      .subscribe((res: any) => {
        if (res.errorcode == 0) {
          this.faqs = res?.data;
        }
      });
  }

  getPlanFeatures() {
    this.apiservice
      .getData(apiEndPoints.getSchemePlans)
      .subscribe((res: any) => {
        if (res?.ErrorCode == 0) {
          this.features = res?.Data;
        }
      });
  }

  toggleModal() {
    this.commonService.sendClickEvent();
    this.showModal = true;
  }

  getBanners() {
    this.apiservice.getData(apiEndPoints.page_banners).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.banner1 = res?.Data?.scheme_page_first_banner;
        this.banner2 = res?.Data?.scheme_page_second_banner;
      }
    });
  }

  bannerRedirections(type: any, id: any) {
    if (!id && type == 0) {
      this.router.navigateByUrl('/gold-account');
      return
    }
    if (type == 1) this.router.navigate(['/detail', id]);
    else if (type == 2 || 3 || 4) this.router.navigate(['/listing', type, id]);

    if (type == 5 && id == 3) {
      this.router.navigateByUrl('/virtual-shopping');
    } else if (type == 5 && id == 4) {
      this.router.navigateByUrl('/zora-collection');
    } else if (type == 5 && id == 5) {
      this.router.navigateByUrl('/divo-collection');
    } else if (type == 5 && id == 6) {
      this.router.navigateByUrl('/poyem-collection');
    } else if (type == 5 && id == 7) {
      this.router.navigateByUrl('/orlin-collection');
    } else if (type == 5 && id == 8) {
      this.router.navigateByUrl('/book-appointment');
    } else if (type == 5 && id == 2) {
      this.router.navigateByUrl('/gold-schemes');
    } else if (type == 5 && id == 1) {
      this.router.navigateByUrl('/advance-booking');
    } else if (type == 5 && id == 9) {
      this.router.navigateByUrl('/gift-voucher');
    } else if (type == 5 && id == 10) {
      this.router.navigateByUrl('/digital-gold');
    } else if (type == 5 && id == 11) {
      this.router.navigateByUrl('/about');
    }
  }
}
