import { ToastrService } from 'ngx-toastr';
import { ApiService } from './../Services/api.service';
import { Component, OnInit } from '@angular/core';
import {
  NgbCalendar,
  NgbDate,
  NgbDatepicker,
  NgbDateStruct,
  NgbInputDatepickerConfig,
} from '@ng-bootstrap/ng-bootstrap';
import { noop } from 'rxjs';
import { DatepickerOptions } from 'ng2-datepicker';
import { getYear } from 'date-fns';
import locale from 'date-fns/locale/en-US';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { apiEndPoints } from '../ApiEndPoints';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment.prod';

@Component({
  selector: 'app-virtual-shopping',
  templateUrl: './virtual-shopping.component.html',
  styleUrls: ['./virtual-shopping.component.scss'],
  providers: [NgbInputDatepickerConfig],
})
export class VirtualShoppingComponent implements OnInit {
  virtualForm!: FormGroup;
  model!: Boolean | NgbDateStruct;
  date = new Date();
  dateString: any;
  firstTimeAssign: any;
  private onChange: (_: any) => void = noop;
  showTimePickerToggle: Boolean = false;
  selectedStore: any;

  // options sample with default values
  options: DatepickerOptions = {
    minYear: getYear(new Date()) - 30, // minimum available and selectable year
    maxYear: getYear(new Date()) + 30, // maximum available and selectable year
    placeholder: '', // placeholder in case date model is null | undefined, example: 'Please pick a date'
    format: 'LLLL do yyyy', // date format to display in input
    formatTitle: 'LLLL yyyy',
    formatDays: 'EEEEE',
    firstCalendarDay: 0, // 0 - Sunday, 1 - Monday
    locale: locale, // date-fns locale
    position: 'bottom',
    inputClass: '', // custom input CSS class to be applied
    calendarClass: 'datepicker-default', // custom datepicker calendar CSS class to be applied
    scrollBarColor: '#dfe3e9', // in case you customize you theme, here you define scroll bar color
    // keyboardEvents: true // enable keyboard events
  };
  stores: any;
  storeValidation: any;
  banners: any;
  imageBase = environment.imageBase;

  constructor(
    private formbuilder: FormBuilder,
    private ApiService: ApiService,
    private toast: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initVirtualForm();
    this.getStores();
    this.getBanners();
  }

  initVirtualForm() {
    this.virtualForm = this.formbuilder.group({
      name: ['', [Validators.required]],
      mobile: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      date: ['', [Validators.required]],
      email: [
        '',
        [
          Validators.required,
          Validators.pattern(
            '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,4}$'
          ),
        ],
      ],
      lookingFor: ['', [Validators.required]],
      time: ['', [Validators.required]],
    });
  }

  get vf() {
    return this.virtualForm.controls;
  }

  getStores() {
    this.ApiService.getData(apiEndPoints.GET_STORES).subscribe((res: any) => {
      if (res.ErrorCode == 0) {
        this.stores = res?.Data;
      }
    });
  }

  submit() {
    if (!this.selectedStore) {
      this.storeValidation = 'Store is required';
    }

    if (this.virtualForm.invalid) {
      this.virtualForm.markAllAsTouched();
      return;
    }

    let data = {
      name: this.virtualForm.get('name')?.value,
      mobile: this.virtualForm.get('mobile')?.value,
      email: this.virtualForm.get('email')?.value,
      store: this.selectedStore,
      date: this.virtualForm.get('date')?.value,
      looking_for: this.virtualForm.get('lookingFor')?.value,
      time: this.virtualForm.get('time')?.value,
    };
    if (this.selectedStore) {
      this.ApiService.postData(apiEndPoints.virtualShop, data).subscribe(
        (res: any) => {
          if (res.ErrorCode == 0) {
            this.toast.success(res?.Message);
            this.virtualForm.reset();
            window.location.reload();
          } else this.toast.error(res?.Message);
        }
      );
    }
  }

  getBanners() {
    this.ApiService.getData(apiEndPoints.page_banners).subscribe((res: any) => {
      if (res?.ErrorCode == 0) {
        this.banners = res?.Data?.videocall_page_banner;
      }
    });
  }

  bannerRedirections(type: any, id: any) {
    if (type == 1) this.router.navigate(['/detail', id]);
    else if (type == 2 || 3 || 4) this.router.navigate(['/listing', type, id]);

    if (type == 5 && id == 3) {
      this.router.navigateByUrl('/virtual-shopping');
    } else if (type == 5 && id == 4) {
      this.router.navigateByUrl('/zora-collection');
    } else if (type == 5 && id == 5) {
      this.router.navigateByUrl('/divo-collection');
    } else if (type == 5 && id == 6) {
      this.router.navigateByUrl('/poyem-collection');
    } else if (type == 5 && id == 7) {
      this.router.navigateByUrl('/orlin-collection');
    } else if (type == 5 && id == 8) {
      this.router.navigateByUrl('/book-appointment');
    } else if (type == 5 && id == 2) {
      this.router.navigateByUrl('/gold-schemes');
    } else if (type == 5 && id == 1) {
      this.router.navigateByUrl('/advance-booking');
    }else if(type == 5 && id == 9){
      this.router.navigateByUrl('/gift-voucher');
    }else if (type == 5 && id == 10){
      this.router.navigateByUrl('/digital-gold');
    }else if (type == 5 && id == 11){
      this.router.navigateByUrl('/about');
    }
  }
}
